{"extends": "next/core-web-vitals", "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-unsafe-function-type": "off", "react/no-unescaped-entities": "off", "react-hooks/rules-of-hooks": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-return": "off"}, "ignorePatterns": ["**/*.js", "**/*.jsx"]}