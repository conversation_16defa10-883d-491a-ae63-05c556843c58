# Chat Negotiation Feature Integration Guide

## Overview

The Chat Negotiation feature has been successfully integrated into the DocGIC web application. This feature provides gamified negotiation simulation where users can practice negotiation skills against AI opponents with different personalities and difficulty levels.

## Features Integrated

### 1. Core Components

- **Types**: Complete TypeScript definitions for the chat negotiation API
- **Service Layer**: Full API client with error handling and response transformation
- **Custom Hook**: React hook for state management and API interactions
- **UI Components**: Complete user interface for all negotiation workflows

### 2. Pages Created

- **Main Dashboard** (`/chat-negotiation`): Browse scenarios and manage sessions
- **Scenario Setup** (`/chat-negotiation/[scenarioId]/setup`): Configure AI personality and session parameters
- **Active Session** (`/chat-negotiation/sessions/[sessionId]`): Real-time negotiation interface
- **Results Page** (`/chat-negotiation/sessions/[sessionId]/results`): Detailed performance analysis

### 3. Key Features

- **AI Personality Customization**: Adjust aggressiveness, flexibility, risk tolerance, and communication style
- **Real-time Chat Interface**: Interactive negotiation with typing indicators and suggestions
- **Performance Tracking**: Comprehensive metrics including trust, respect, and pressure levels
- **Data Extraction**: Automatic extraction of offers, terms, and negotiation strategies
- **Gamification Integration**: Connects with existing gamification system for achievements and scoring
- **Export Functionality**: Download session reports in multiple formats (PDF, JSON, CSV)

## File Structure

```
src/
├── lib/
│   ├── types/
│   │   └── chat-negotiation.ts              # TypeScript definitions
│   ├── services/
│   │   └── chat-negotiation-service.ts      # API service layer
│   └── data/
│       └── sample-negotiation-scenarios.ts  # Sample data for development
├── hooks/
│   └── use-chat-negotiation.ts              # React hook for state management
├── app/(dashboard)/chat-negotiation/
│   ├── page.tsx                             # Main dashboard
│   ├── [scenarioId]/setup/page.tsx          # Scenario configuration
│   └── sessions/[sessionId]/
│       ├── page.tsx                         # Active negotiation
│       └── results/page.tsx                 # Results analysis
└── components/ui/
    ├── tabs.tsx                             # Tab navigation component
    ├── progress.tsx                         # Progress bars
    ├── slider.tsx                           # Range sliders
    ├── select.tsx                           # Dropdown selects
    └── textarea.tsx                         # Text input areas
```

## API Endpoints Implemented

### Session Management
- `POST /api/chat-negotiation/sessions` - Create new session
- `GET /api/chat-negotiation/sessions/:id` - Get session details
- `GET /api/chat-negotiation/sessions` - List user sessions
- `PATCH /api/chat-negotiation/sessions/:id/pause` - Pause session
- `PATCH /api/chat-negotiation/sessions/:id/resume` - Resume session
- `PATCH /api/chat-negotiation/sessions/:id/complete` - Complete session

### Negotiation Actions
- `POST /api/chat-negotiation/sessions/:id/moves` - Send negotiation move
- `POST /api/chat-negotiation/extract-data` - Extract data from message
- `POST /api/chat-negotiation/sessions/:id/suggestions` - Get AI suggestions

### Scenarios and Analytics
- `GET /api/chat-negotiation/scenarios` - List available scenarios
- `GET /api/chat-negotiation/scenarios/:id` - Get scenario details
- `GET /api/chat-negotiation/sessions/:id/analytics` - Session performance data
- `GET /api/chat-negotiation/sessions/:id/export` - Export session data

## Data Models

### Core Types

```typescript
// Main session object
interface NegotiationSession {
  id: string;
  scenarioId: string;
  status: 'active' | 'completed' | 'paused';
  currentRound: number;
  extractedTerms: ExtractedTerms;
  relationshipMetrics: RelationshipMetrics;
  score: number;
  aiPersonality: AiPersonality;
  // ... additional fields
}

// AI configuration
interface AiPersonality {
  characterId: string;
  aggressiveness: number;      // 0.0 - 1.0
  flexibility: number;         // 0.0 - 1.0
  riskTolerance: number;       // 0.0 - 1.0
  communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
}

// Relationship tracking
interface RelationshipMetrics {
  trust: number;               // 0-100
  respect: number;             // 0-100
  pressure: number;            // 0-100
}
```

## Integration Points

### 1. Sidebar Navigation
The feature has been added to the sidebar under "AI & Automation" category:
```typescript
{
  label: "Chat Negotiation",
  href: "/chat-negotiation",
  icon: Users2,
  active: pathname === "/chat-negotiation" || pathname.startsWith("/chat-negotiation/"),
}
```

### 2. Gamification System
Integrates with existing gamification service for:
- Character unlocking and selection
- Achievement tracking
- Experience points and leveling
- Performance analytics

### 3. Credit System
- Chat moves consume 3 credits per message
- Data extraction is free
- Credit validation before sending moves
- Usage tracking in responses

## Configuration

### Environment Variables
No additional environment variables required. Uses existing API configuration.

### Feature Flags
Consider adding feature flags for:
- Chat negotiation availability
- Advanced AI personalities
- Export functionality
- Gamification integration

## Sample Data

Sample negotiation scenarios are provided in `src/lib/data/sample-negotiation-scenarios.ts`:
- Software License Agreement (Beginner)
- Merger & Acquisition Deal (Advanced)
- Commercial Real Estate Lease (Intermediate)
- International Trade Agreement (Advanced)
- Executive Compensation Package (Intermediate)
- Partnership Joint Venture (Advanced)

## Testing Considerations

### Unit Tests
- Service layer API calls
- Hook state management
- Data transformation functions
- Error handling scenarios

### Integration Tests
- Complete negotiation workflow
- Session state persistence
- Real-time updates
- Export functionality

### End-to-End Tests
- User journey from scenario selection to completion
- Multi-round negotiations
- Performance metric calculations
- Results page functionality

## Performance Optimizations

1. **Lazy Loading**: Components are loaded on-demand
2. **Memoization**: Expensive calculations are cached
3. **Debounced Input**: Suggestion requests are debounced
4. **Progressive Enhancement**: Core functionality works without JavaScript

## Security Considerations

1. **Authentication**: All endpoints require Bearer token
2. **Authorization**: Users can only access their own sessions
3. **Rate Limiting**: 60 requests per minute per user
4. **Input Validation**: All user inputs are validated
5. **Data Sanitization**: Messages are sanitized before processing

## Accessibility

- Full keyboard navigation support
- Screen reader compatible
- High contrast mode support
- Proper ARIA labels and roles
- Focus management in modals

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Future Enhancements

### Planned Features
1. **WebSocket Integration**: Real-time updates and typing indicators
2. **Voice Integration**: Voice-to-text and text-to-speech
3. **Advanced Analytics**: Detailed conversation analysis
4. **Team Negotiations**: Multi-participant scenarios
5. **Custom Scenarios**: User-generated negotiation scenarios

### API Extensions
1. **Scenario Builder**: Create custom negotiation scenarios
2. **Advanced Metrics**: Deeper behavioral analysis
3. **Historical Trends**: Long-term performance tracking
4. **Coaching System**: AI-powered improvement suggestions

## Troubleshooting

### Common Issues

1. **Session Not Loading**: Check authentication and session permissions
2. **AI Not Responding**: Verify credit balance and API connectivity
3. **Export Failing**: Check browser file download permissions
4. **Suggestions Not Working**: Ensure message content is not empty

### Debug Information
- Enable network debugging for API calls
- Check browser console for JavaScript errors
- Verify WebSocket connections for real-time features
- Monitor performance metrics for optimization opportunities

## Deployment Checklist

- [ ] API endpoints implemented and tested
- [ ] Database migrations for session storage
- [ ] Credit system integration verified
- [ ] Gamification hooks connected
- [ ] Export functionality configured
- [ ] Rate limiting enabled
- [ ] Monitoring and logging set up
- [ ] Performance benchmarks established

## Support and Maintenance

### Monitoring
- Track API response times
- Monitor error rates
- Watch credit consumption patterns
- Analyze user engagement metrics

### Maintenance Tasks
- Regular cleanup of completed sessions
- Performance optimization reviews
- Security audit of API endpoints
- User feedback analysis and feature improvements