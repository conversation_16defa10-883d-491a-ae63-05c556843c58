# 📄 Document Integration for Chat Negotiation

## 🎯 Overview

The Document Integration feature bridges contract analysis with negotiation practice, allowing users to practice negotiating real contract issues identified through AI-powered contract analysis.

## 🚀 Key Features

### 1. **Contract-to-Negotiation Pipeline**

- Analyze contracts using playbooks
- Extract negotiation points from deviations
- Generate practice scenarios based on real issues
- Create AI opponents tailored to contract complexity

### 2. **Intelligent Scenario Generation**

- **Automatic Issue Extraction**: Converts contract deviations into negotiation points
- **Risk-Based Difficulty**: Adjusts AI personality based on contract risk level
- **Contextual Focus Areas**: Identifies key negotiation categories (liability, payment, etc.)
- **Strategic Recommendations**: Provides negotiation strategies for each issue

### 3. **Document-Aware AI Responses**

- **Contract Context**: AI responses reference specific contract issues
- **Intelligent Suggestions**: Conversation prompts based on contract analysis
- **Risk-Informed Behavior**: AI personality adapts to contract risk profile

## 🔧 API Endpoints

### Create Scenario from Analysis

```http
POST /api/chat-negotiation/scenarios/from-analysis
```

**Request:**

```json
{
  "analysisId": "analysis-uuid"
}
```

**Response:**

```json
{
  "id": "doc-scenario-analysis-uuid",
  "title": "Negotiate: Employment Agreement",
  "description": "Practice negotiating the key issues identified...",
  "contractType": "EMPLOYMENT_CONTRACT",
  "focusAreas": ["Payment Terms", "Risk & Liability", "Contract Duration"],
  "negotiationPoints": [
    {
      "id": "point-1",
      "category": "Risk & Liability",
      "issue": "Termination Rights Balance",
      "currentTerm": "Either party may terminate...",
      "suggestedImprovement": "Negotiate more favorable terms",
      "riskLevel": "HIGH",
      "priority": 1,
      "negotiationStrategy": "Request modification to reduce risk exposure",
      "fallbackOptions": ["Accept current terms with additional protections"]
    }
  ],
  "difficulty": "ADVANCED",
  "estimatedDuration": 20,
  "aiPersonalityRecommendation": {
    "aggressiveness": 0.7,
    "flexibility": 0.3,
    "riskTolerance": 0.2,
    "communicationStyle": "DIRECT"
  }
}
```

### Create Session from Document

```http
POST /api/chat-negotiation/sessions/from-analysis
```

**Request:**

```json
{
  "analysisId": "analysis-uuid",
  "aiPersonality": {
    "characterId": "contract_specialist",
    "aggressiveness": 0.6,
    "flexibility": 0.4,
    "riskTolerance": 0.3,
    "communicationStyle": "ANALYTICAL"
  }
}
```

**Response:**

```json
{
  "id": "session-uuid",
  "scenarioId": "doc-scenario-analysis-uuid",
  "sourceDocumentId": "document-uuid",
  "sourceAnalysisId": "analysis-uuid",
  "documentContext": {
    "contractType": "EMPLOYMENT_CONTRACT",
    "contractTitle": "Employment Agreement",
    "analysisScore": 65,
    "riskLevel": "HIGH",
    "totalDeviations": 8,
    "keyFindings": [
      "Unbalanced termination rights",
      "Missing severance provisions"
    ]
  },
  "status": "active",
  "currentRound": 1,
  "relationshipMetrics": {
    "trust": 50,
    "respect": 50,
    "pressure": 20
  },
  "score": 0,
  "aiPersonality": {
    /* AI personality settings */
  }
}
```

### Get Document Context

```http
GET /api/chat-negotiation/sessions/:id/document-context
```

**Response:**

```json
{
  "context": "Contract Analysis Context:\n- Contract Type: EMPLOYMENT_CONTRACT\n- Overall Score: 65/100\n- Risk Level: HIGH\n- Total Deviations: 8\n\nKey Issues to Negotiate:\n- Termination Rights Balance: Either party may terminate...\n- Confidentiality Term Limitation: Confidentiality obligations...",
  "hasDocumentContext": true
}
```

## 🎮 User Experience Flow

### 1. **Contract Analysis**

```mermaid
graph LR
    A[Upload Contract] --> B[Select Playbook]
    B --> C[Run Analysis]
    C --> D[Review Results]
    D --> E[Start Negotiation Practice]
```

### 2. **Negotiation Practice**

```mermaid
graph LR
    A[Analysis Results] --> B[Generate Scenario]
    B --> C[Create Session]
    C --> D[Practice Negotiation]
    D --> E[Get Feedback]
```

## 🧠 AI Personality Adaptation

The system automatically adjusts AI behavior based on contract analysis:

### Risk-Based Personality

- **CRITICAL Risk**: Aggressive (0.8), Low Flexibility (0.2), Direct Communication
- **HIGH Risk**: Moderate Aggressive (0.6), Medium Flexibility (0.4), Analytical Communication
- **MEDIUM Risk**: Balanced (0.5), High Flexibility (0.6), Diplomatic Communication
- **LOW Risk**: Collaborative (0.3), Very Flexible (0.8), Friendly Communication

### Deviation-Based Adjustments

- **Many Deviations**: Less flexible AI, more focused on specific issues
- **Few Deviations**: More collaborative AI, open to creative solutions
- **Critical Issues**: Direct communication style, emphasis on risk mitigation

## 🎯 Negotiation Point Categories

The system automatically categorizes contract issues:

- **Payment Terms**: Pricing, payment schedules, late fees
- **Risk & Liability**: Indemnification, limitation of liability, insurance
- **Contract Duration**: Term length, renewal, termination
- **Intellectual Property**: Ownership, licensing, confidentiality
- **Performance**: Deliverables, SLAs, quality standards
- **Dispute Resolution**: Arbitration, jurisdiction, governing law
- **General Terms**: Miscellaneous provisions

## 🔍 Example Scenarios

### Employment Contract Negotiation

**Analysis Found**: Unbalanced termination rights, missing severance
**Generated Scenario**: Practice negotiating fair termination clauses
**AI Personality**: Direct, focused on employment law compliance
**Key Points**: Notice periods, severance packages, cause definitions

### NDA Negotiation

**Analysis Found**: One-way confidentiality, unlimited term
**Generated Scenario**: Practice negotiating mutual confidentiality
**AI Personality**: Analytical, focused on information protection
**Key Points**: Mutual obligations, reasonable time limits, exceptions

### Service Agreement Negotiation

**Analysis Found**: High liability exposure, unclear deliverables
**Generated Scenario**: Practice negotiating risk allocation
**AI Personality**: Collaborative but risk-conscious
**Key Points**: Liability caps, performance standards, change management

## 📊 Benefits

### For Legal Professionals

- **Real-World Practice**: Negotiate actual contract issues, not hypotheticals
- **Risk Awareness**: Understand implications of different contract terms
- **Strategy Development**: Learn negotiation approaches for specific issues
- **Confidence Building**: Practice difficult conversations in safe environment

### For Law Students

- **Practical Application**: Apply contract law knowledge to real scenarios
- **Pattern Recognition**: Learn to identify common contract issues
- **Professional Skills**: Develop negotiation and communication abilities
- **Industry Exposure**: Experience different contract types and industries

### For Organizations

- **Team Training**: Standardize negotiation approaches across teams
- **Risk Mitigation**: Ensure negotiators understand contract implications
- **Knowledge Transfer**: Share expertise through practice scenarios
- **Performance Tracking**: Monitor negotiation skill development

## 🔮 Future Enhancements

- **Multi-Party Negotiations**: Support for complex deal structures
- **Industry-Specific Scenarios**: Tailored scenarios for different sectors
- **Historical Analysis**: Learn from past negotiation outcomes
- **Integration with CRM**: Connect with deal management systems
- **Video Conferencing**: Practice negotiations via video calls
- **Team Negotiations**: Collaborative negotiation practice sessions
