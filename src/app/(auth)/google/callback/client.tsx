"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/auth-context";
import { Spinner } from "@/components/ui/spinner";

export function GoogleCallbackClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get("token");
  const error = searchParams.get("error");
  const { loginWithToken } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      console.log("[Google Callback] Processing callback...");
      console.log("[Google Callback] Token:", token ? "present" : "missing");
      console.log("[Google Callback] Error:", error);

      // Check for OAuth errors first
      if (error) {
        console.error("[Google Callback] OAuth error:", error);
        router.push("/login?error=oauth_failed");
        return;
      }

      // Check for token
      if (!token) {
        console.error("[Google Callback] No token received");
        router.push("/login?error=no_token");
        return;
      }

      try {
        console.log("[Google Callback] Attempting login with token...");
        await loginWithToken(token);
        console.log("[Google Callback] Login successful!");
        // loginWithToken handles the redirect to dashboard
      } catch (err) {
        console.error("[Google Callback] Login failed:", err);
        console.error("[Google Callback] Error details:", {
          message: err instanceof Error ? err.message : 'Unknown error',
          stack: err instanceof Error ? err.stack : undefined,
          error: err
        });

        // Add more specific error handling
        let errorParam = "login_failed";
        if (err instanceof Error) {
          if (err.message.includes("Profile fetch failed")) {
            errorParam = "profile_fetch_failed";
          } else if (err.message.includes("401")) {
            errorParam = "unauthorized";
          } else if (err.message.includes("Network")) {
            errorParam = "network_error";
          }
        }

        router.push(`/login?error=${errorParam}`);
      } finally {
        setIsProcessing(false);
      }
    };

    handleCallback();
  }, [token, error, loginWithToken, router]);

  if (!isProcessing) {
    return null; // Don't render anything after processing is complete
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <Spinner size="lg" />
        <span className="text-lg">Signing you in...</span>
        {token && (
          <span className="text-sm text-muted-foreground">
            Processing authentication...
          </span>
        )}
      </div>
    </div>
  );
}