import { Suspense } from 'react';
import { PasswordResetClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

export default function PasswordResetPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    }>
      <PasswordResetClient />
    </Suspense>
  );
}