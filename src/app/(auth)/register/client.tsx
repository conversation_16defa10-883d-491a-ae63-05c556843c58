'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { useAuth } from '@/lib/auth/auth-context';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { MessageSquare, ArrowLeft } from 'lucide-react';
import GoogleLoginButton from '@/components/GoogleLoginButton';

interface RegistrationData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username?: string;
  organizationName?: string;
  createNewOrganization: boolean;
  organizationId?: string;
}

export function RegisterClient() {
  const [formData, setFormData] = useState<RegistrationData>({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    username: '',
    createNewOrganization: true,
    organizationName: '',
    organizationId: '',
  });

  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { register } = useAuth();
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Ensure required fields are always included
      const registrationData = {
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        ...formData.username ? { username: formData.username } : {},
        ...formData.organizationName ? { organizationName: formData.organizationName } : {},
        createNewOrganization: true
      };

      // Call register without awaiting the profile fetch
      const registerPromise = register(registrationData);
      
      // Immediately redirect to verification page without waiting for profile fetch
      router.push(`/verification-pending?email=${encodeURIComponent(formData.email)}`);
      
      // Wait for register to complete in the background
      await registerPromise;
    } catch (err) {
      // If there's an error, we'll still be on the registration page
      setError(err instanceof Error ? err.message : 'Failed to create account');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Back to Home Button - Fixed position at top left */}
      <div className="absolute top-6 left-6 z-10">
        <Link href="/" className="inline-flex items-center text-sm text-muted-foreground hover:text-primary transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Link>
      </div>
      
      <div className="min-h-screen flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          <Card className="w-full p-8 space-y-6">
        <div className="text-center space-y-2">
          <div className="flex justify-center mb-4">
            {/* Light mode logo */}
            <Image
              src="/logos/light-512.png"
              alt="Docgic Logo Light"
              width={80}
              height={80}
              className="rounded-lg block dark:hidden"
            />
            {/* Dark mode logo */}
            <Image
              src="/logos/trans-512.png"
              alt="Docgic Logo Dark"
              width={80}
              height={80}
              className="rounded-lg hidden dark:block"
            />
          </div>
          <h1 className="text-2xl font-bold">Join Docgic</h1>
          <p className="text-muted-foreground">
            Start using legal AI that actually works
          </p>
        </div>

        <GoogleLoginButton 
          className="w-full mb-6" 
          variant="outline"
          isLoading={isLoading}
          text="Sign up with Google"
        />

        <div className="relative mb-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or continue with email
            </span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Input
                name="firstName"
                placeholder="First Name"
                value={formData.firstName}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Input
                name="lastName"
                placeholder="Last Name"
                value={formData.lastName}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          <div className="space-y-2">
            <Input
              name="username"
              placeholder="Username (optional)"
              value={formData.username}
              onChange={handleChange}
            />
          </div>
          <div className="space-y-2">
            <Input
              type="email"
              name="email"
              placeholder="Email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>
          <div className="space-y-2">
            <Input
              type="password"
              name="password"
              placeholder="Password"
              value={formData.password}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Input
              name="organizationName"
              placeholder="Organization Name (optional)"
              value={formData.organizationName}
              onChange={handleChange}
            />
          </div>

          {error && (
            <div className="text-sm text-destructive text-center">
              {error}
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? 'Creating account...' : 'Create account'}
          </Button>
        </form>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <Link
              href="/login"
              className="text-primary hover:underline"
            >
              Sign in
            </Link>
          </p>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          By creating an account, you agree to our{' '}
          <Link href="/terms" className="text-primary hover:underline font-medium">
            Terms of Service
          </Link>{' '}
          and{' '}
          <Link href="/privacy" className="text-primary hover:underline font-medium">
                      Privacy Policy
        </Link>
      </div>
    </Card>
        </div>
      </div>
    </div>
  );
}