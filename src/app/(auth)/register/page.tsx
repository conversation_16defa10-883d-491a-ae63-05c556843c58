import { Suspense } from 'react';
import { RegisterClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

export default function RegisterPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    }>
      <RegisterClient />
    </Suspense>
  );
}
