'use client';

import { Suspense, useState } from 'react';
import { useResendVerificationEmail } from '@/lib/auth/auth-service';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MessageSquare, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type FormValues = z.infer<typeof formSchema>;

function ResendVerificationContent() {
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  
  const { mutate: resendVerificationEmail, isPending } = useResendVerificationEmail();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = (values: FormValues) => {
    setStatus('idle');
    setMessage('');
    
    resendVerificationEmail(values.email, {
      onSuccess: (data) => {
        setStatus('success');
        setMessage(data.message || 'Verification email has been sent. Please check your inbox.');
        form.reset();
      },
      onError: (error) => {
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Failed to send verification email. Please try again.');
      }
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <Card className="w-full max-w-md p-8 space-y-6">
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <MessageSquare className="h-12 w-12 text-primary" />
          </div>
          <h1 className="text-2xl font-bold">Resend Verification Email</h1>
          <p className="text-muted-foreground">
            Enter your email address to receive a new verification link
          </p>
        </div>

        {status === 'success' && (
          <Alert className="bg-green-50 border-green-200 text-green-800">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle>Email Sent</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        {status === 'error' && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="<EMAIL>" 
                      type="email" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full"
              disabled={isPending}
            >
              {isPending ? 'Sending...' : 'Send Verification Email'}
            </Button>
          </form>
        </Form>

        <div className="text-center">
          <Button asChild variant="link" className="gap-1">
            <Link href="/login">
              <ArrowLeft className="h-4 w-4" />
              Back to Login
            </Link>
          </Button>
        </div>
      </Card>
    </div>
  );
}

export default function ResendVerificationPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    }>
      <ResendVerificationContent />
    </Suspense>
  );
}
