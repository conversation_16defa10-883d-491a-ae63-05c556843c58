'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useVerifyEmail } from '@/lib/auth/auth-service';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { UseMutationResult } from '@tanstack/react-query';

export function VerifyEmailClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Verifying your email...');
  
  const { mutate: verifyEmail } = useVerifyEmail() as UseMutationResult<
    { success: boolean; message: string },
    Error,
    string,
    unknown
  >;

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setMessage('Verification token is missing. Please check your email link and try again.');
      return;
    }

    verifyEmail(token, {
      onSuccess: (data) => {
        setStatus('success');
        setMessage(data.message || 'Your email has been successfully verified.');
      },
      onError: (error) => {
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Failed to verify email. The token may be invalid or expired.');
      }
    });
  }, [token, verifyEmail]);

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <Card className="w-full max-w-md p-8 space-y-6">
        <div className="text-center space-y-4">
          {status === 'loading' && (
            <div className="flex justify-center">
              <Loader2 className="h-12 w-12 text-primary animate-spin" />
            </div>
          )}
          
          {status === 'success' && (
            <div className="flex justify-center">
              <CheckCircle className="h-12 w-12 text-green-500" />
            </div>
          )}
          
          {status === 'error' && (
            <div className="flex justify-center">
              <XCircle className="h-12 w-12 text-destructive" />
            </div>
          )}
          
          <h1 className="text-2xl font-bold">
            {status === 'loading' ? 'Verifying Email' : 
             status === 'success' ? 'Email Verified' : 
             'Verification Failed'}
          </h1>
          
          <p className="text-muted-foreground">
            {message}
          </p>
        </div>

        <div className="flex flex-col space-y-3">
          {status === 'success' && (
            <Button onClick={() => router.push('/chat')}>
              Continue to Dashboard
            </Button>
          )}
          
          {status === 'error' && (
            <Button asChild variant="outline">
              <Link href="/resend-verification">
                Resend Verification Email
              </Link>
            </Button>
          )}
          
          <Button asChild variant={status === 'success' ? 'outline' : 'default'}>
            <Link href="/login">
              Back to Login
            </Link>
          </Button>
        </div>
      </Card>
    </div>
  );
}