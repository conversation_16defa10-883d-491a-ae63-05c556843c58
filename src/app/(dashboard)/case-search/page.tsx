import { Suspense } from 'react';
import { CaseSearchClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

export default function CaseSearchPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-screen">
        <Spinner size="lg" />
      </div>
    }>
      <CaseSearchClient />
    </Suspense>
  );
}
