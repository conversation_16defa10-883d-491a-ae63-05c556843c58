"use client";

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  Play,
  Users,
  Target,
  Clock,
  Brain,
  MessageSquare,
  Settings,
  Info,
  CheckCircle
} from 'lucide-react';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import { gamificationService } from '@/lib/services/gamification-service';
import { useChatNegotiation } from '@/hooks/use-chat-negotiation';
import { useAuth } from '@/lib/auth/auth-context';
import type {
  Sc<PERSON>rioResponse,
  CreateSessionRequest,
  AiPersonality
} from '@/lib/types/chat-negotiation';
import type { AICharacter } from '@/lib/services/gamification-service';

export default function ScenarioSetupPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { createSession } = useChatNegotiation();
  
  const [scenario, setScenario] = useState<ScenarioResponse | null>(null);
  const [characters, setCharacters] = useState<AICharacter[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  
  // AI Personality Configuration
  const [selectedCharacter, setSelectedCharacter] = useState<string>('');
  const [aggressiveness, setAggressiveness] = useState([0.5]);
  const [flexibility, setFlexibility] = useState([0.7]);
  const [riskTolerance, setRiskTolerance] = useState([0.6]);
  const [communicationStyle, setCommunicationStyle] = useState<AiPersonality['communicationStyle']>('ANALYTICAL');

  const scenarioId = params.scenarioId as string;

  useEffect(() => {
    loadScenarioData();
  }, [scenarioId]);

  const loadScenarioData = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      const [scenarioData, charactersData] = await Promise.all([
        chatNegotiationService.getScenario(scenarioId),
        gamificationService.getCharacters(user.id, { unlocked: true })
      ]);
      
      setScenario(scenarioData);
      setCharacters(charactersData.filter(c => c.unlocked));
      
      // Set default character if available
      if (charactersData.length > 0) {
        setSelectedCharacter(charactersData[0].id);
      }
    } catch (error) {
      console.error('Failed to load scenario data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartNegotiation = async () => {
    if (!scenario || !selectedCharacter) return;

    try {
      setIsCreating(true);
      
      const request: CreateSessionRequest = {
        scenarioId: scenario._id || scenario.id,
        aiPersonality: {
          characterId: selectedCharacter,
          aggressiveness: aggressiveness[0],
          flexibility: flexibility[0],
          riskTolerance: riskTolerance[0],
          communicationStyle,
        },
        metadata: {
          source: 'web_app',
          version: '1.0',
          setupTime: Date.now(),
        }
      };

      const session = await createSession(request);
      router.push(`/chat-negotiation/sessions/${session.id}`);
    } catch (error) {
      console.error('Failed to create session:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const selectedCharacterData = characters.find(c => c.id === selectedCharacter);

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="h-10 bg-gray-200 rounded"></div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!scenario) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Scenario not found</h1>
          <p className="text-gray-600 mb-4">The requested scenario could not be loaded.</p>
          <Button onClick={() => router.push('/chat-negotiation')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Scenarios
          </Button>
        </div>
      </div>
    );
  }

  const getDifficultyColor = (difficulty: string) => {
    if (difficulty === 'beginner') return 'bg-green-100 text-green-800';
    if (difficulty === 'intermediate') return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getDifficultyText = (difficulty: string) => {
    return difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:gap-4 sm:space-y-0">
        <Button variant="ghost" onClick={() => router.push('/chat-negotiation')} className="self-start">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-xl sm:text-2xl font-bold">Scenario Setup</h1>
          <p className="text-gray-600 text-sm sm:text-base">Configure your negotiation parameters</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Scenario Details */}
        <div className="lg:col-span-2 space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-xl">{scenario.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {scenario.description}
                  </CardDescription>
                </div>
                <Badge className={getDifficultyColor(scenario.difficulty)}>
                  {getDifficultyText(scenario.difficulty)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Category:</span>
                  <span className="text-sm text-gray-600">{scenario.industry}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">Type:</span>
                  <span className="text-sm text-gray-600">{scenario.contractType}</span>
                </div>
              </div>

              <Separator />

              {scenario.tags && scenario.tags.length > 0 && (
                <div>
                  <h3 className="font-medium mb-3 flex items-center gap-2">
                    <Info className="h-4 w-4 text-blue-500" />
                    Tags
                  </h3>
                  <div className="flex flex-wrap gap-1">
                    {scenario.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {scenario.parties && scenario.parties.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <h3 className="font-medium mb-3 flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-500" />
                      Parties Involved
                    </h3>
                    <div className="space-y-2">
                      {scenario.parties.slice(0, 2).map((party: any, index) => (
                        <div key={index} className="text-sm">
                          <span className="font-medium">{party.name || `Party ${index + 1}`}:</span>
                          <span className="text-gray-600 ml-1">{party.role || 'Negotiating party'}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Configuration Panel */}
        <div className="space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                AI Character
              </CardTitle>
              <CardDescription>
                Choose your negotiation counterpart
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select value={selectedCharacter} onValueChange={setSelectedCharacter}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a character" />
                </SelectTrigger>
                <SelectContent>
                  {characters.map((character) => (
                    <SelectItem key={character.id} value={character.id}>
                      <div className="flex items-center gap-2">
                        <span>{character.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          Level {character.difficulty}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedCharacterData && (
                <div className="p-3 bg-gray-50 rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{selectedCharacterData.name}</span>
                    <Badge variant="outline">{selectedCharacterData.title}</Badge>
                  </div>
                  <p className="text-xs text-gray-600">{selectedCharacterData.backstory}</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedCharacterData.specialties.map((specialty, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                AI Personality
              </CardTitle>
              <CardDescription>
                Customize the AI's negotiation style
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="text-sm font-medium">
                  Aggressiveness: {aggressiveness[0].toFixed(1)}
                </label>
                <Slider
                  value={aggressiveness}
                  onValueChange={setAggressiveness}
                  max={1}
                  min={0}
                  step={0.1}
                  className="mt-2"
                />
                <p className="text-xs text-gray-500 mt-1">
                  How forceful the AI is in negotiations
                </p>
              </div>

              <div>
                <label className="text-sm font-medium">
                  Flexibility: {flexibility[0].toFixed(1)}
                </label>
                <Slider
                  value={flexibility}
                  onValueChange={setFlexibility}
                  max={1}
                  min={0}
                  step={0.1}
                  className="mt-2"
                />
                <p className="text-xs text-gray-500 mt-1">
                  How willing the AI is to compromise
                </p>
              </div>

              <div>
                <label className="text-sm font-medium">
                  Risk Tolerance: {riskTolerance[0].toFixed(1)}
                </label>
                <Slider
                  value={riskTolerance}
                  onValueChange={setRiskTolerance}
                  max={1}
                  min={0}
                  step={0.1}
                  className="mt-2"
                />
                <p className="text-xs text-gray-500 mt-1">
                  How comfortable the AI is with uncertainty
                </p>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Communication Style
                </label>
                <Select value={communicationStyle} onValueChange={(value: any) => setCommunicationStyle(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DIRECT">Direct</SelectItem>
                    <SelectItem value="DIPLOMATIC">Diplomatic</SelectItem>
                    <SelectItem value="ANALYTICAL">Analytical</SelectItem>
                    <SelectItem value="EMOTIONAL">Emotional</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Button 
            onClick={handleStartNegotiation}
            className="w-full"
            disabled={!selectedCharacter || isCreating}
          >
            {isCreating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Session...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start Negotiation
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}