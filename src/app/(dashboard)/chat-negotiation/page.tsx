"use client";

import { DevOnlyWrapper } from '@/components/dev-only-wrapper';
import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { 
  Play, 
  Users, 
  Trophy, 
  Clock, 
  Target,
  MessageSquare,
  Brain,
  Zap,
  Star,
  TrendingUp,
  Calendar,
  Filter,
  Plus,
  MoreVertical,
  Pencil,
  Copy,
  Trash2
} from 'lucide-react';
import { useChatNegotiation } from '@/hooks/use-chat-negotiation';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import type { ScenarioResponse } from '@/lib/types/chat-negotiation';
import DocumentScenarioCreator from '@/components/chat-negotiation/document-scenario-creator';
import { DocumentScenarioErrorBoundary } from '@/components/chat-negotiation/document-scenario-error-boundary';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { DevOnlyRoute } from "@/components/dev-only-wrapper";

interface SessionCardProps {
  session: any;
  onContinue: (sessionId: string) => void;
}

function SessionCard({ session, onContinue }: SessionCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDuration = (createdAt: string, updatedAt: string) => {
    const duration = new Date(updatedAt).getTime() - new Date(createdAt).getTime();
    const minutes = Math.floor(duration / 60000);
    return minutes > 0 ? `${minutes}m` : '<1m';
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between gap-2">
          <CardTitle className="text-lg truncate min-w-0">Negotiation Session</CardTitle>
          <Badge className={`${getStatusColor(session.status)} flex-shrink-0`}>
            {session.status}
          </Badge>
        </div>
        <CardDescription className="truncate">
          Round {session.currentRound} • Score: {Math.round(session.score * 10) / 10}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4">
          <div className="flex items-center gap-2 min-w-0">
            <MessageSquare className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <span className="text-sm text-gray-600 truncate">{session.totalMessages} messages</span>
          </div>
          <div className="flex items-center gap-2 min-w-0">
            <Clock className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <span className="text-sm text-gray-600 truncate">
              {formatDuration(session.createdAt, session.updatedAt)}
            </span>
          </div>
        </div>
        
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="truncate min-w-0">Trust:</span>
            <span className="font-medium flex-shrink-0 ml-2">{session.relationshipMetrics.trust}/100</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="truncate min-w-0">Respect:</span>
            <span className="font-medium flex-shrink-0 ml-2">{session.relationshipMetrics.respect}/100</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="truncate min-w-0">Pressure:</span>
            <span className="font-medium flex-shrink-0 ml-2">{session.relationshipMetrics.pressure}/100</span>
          </div>
        </div>

        <Button
          onClick={() => onContinue(session.id)}
          className="w-full"
          disabled={session.status === 'completed'}
        >
          {session.status === 'completed' ? 'View Results' : 'Continue'}
        </Button>
      </CardContent>
    </Card>
  );
}

interface ScenarioCardProps {
  scenario: ScenarioResponse;
  onStart: (scenarioId: string) => void;
  onEdit: (scenarioId: string) => void;
  onClone: (scenarioId: string) => void;
  onDelete: (scenarioId: string) => void;
}

function ScenarioCard({ scenario, onStart, onEdit, onClone, onDelete }: ScenarioCardProps) {
  const getDifficultyColor = (difficulty: string) => {
    if (difficulty === 'beginner') return 'bg-green-100 text-green-800';
    if (difficulty === 'intermediate') return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getDifficultyText = (difficulty: string) => {
    return difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg mb-2 truncate">{scenario.name}</CardTitle>
            <CardDescription className="line-clamp-2">
              {scenario.description}
            </CardDescription>
          </div>
          <div className="flex-shrink-0 flex items-center gap-1">
            <Badge className={getDifficultyColor(scenario.difficulty)}>
              {getDifficultyText(scenario.difficulty)}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(scenario._id || scenario.id)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  <span>Edit</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onClone(scenario._id || scenario.id)}>
                  <Copy className="mr-2 h-4 w-4" />
                  <span>Clone</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete(scenario._id || scenario.id)}
                  className="text-red-500 focus:text-red-500"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 mb-4">
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium">Industry:</span>
            <span className="text-sm text-gray-600">{scenario.industry}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium">Type:</span>
            <span className="text-sm text-gray-600">{scenario.contractType}</span>
          </div>
        </div>

        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Tags:</h4>
          <div className="flex flex-wrap gap-1">
            {scenario.tags?.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            )) || <span className="text-xs text-gray-500">No tags</span>}
            {scenario.tags && scenario.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{scenario.tags.length - 3} more
              </Badge>
            )}
          </div>
        </div>

        <Button
          onClick={() => onStart(scenario._id || scenario.id)}
          className="w-full"
        >
          <Play className="h-4 w-4 mr-2" />
          Start Negotiation
        </Button>
      </CardContent>
    </Card>
  );
}

export default function ChatNegotiationPage() {
  return (
    <DevOnlyRoute>
      <ChatNegotiationPageContent />
    </DevOnlyRoute>
  );
}

function ChatNegotiationPageContent() {
  const { sessions, loadSessions } = useChatNegotiation();
  const [scenarios, setScenarios] = useState<ScenarioResponse[]>([]);
  const [isLoadingScenarios, setIsLoadingScenarios] = useState(true);
  const [activeTab, setActiveTab] = useState('scenarios');
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [scenarioToDelete, setScenarioToDelete] = useState<string | null>(null);

  const loadScenarios = useCallback(async () => {
    try {
      setIsLoadingScenarios(true);
      const data = await chatNegotiationService.getScenarios();
      setScenarios(data);
    } catch (error) {
      console.error('Failed to load scenarios:', error);
    } finally {
      setIsLoadingScenarios(false);
    }
  }, []);

  useEffect(() => {
    loadSessions();
    loadScenarios();
  }, [loadSessions, loadScenarios]);

  const handleStartScenario = (scenarioId: string) => {
    router.push(`/chat-negotiation/sessions/setup?scenarioId=${scenarioId}`);
  };

  const handleEditScenario = (scenarioId: string) => {
    router.push(`/chat-negotiation/scenarios/${scenarioId}/edit`);
  };

  const handleCloneScenario = async (scenarioId: string) => {
    try {
      await chatNegotiationService.cloneScenario(scenarioId);
      await loadScenarios();
      // TODO: Add toast notification for success
    } catch (error) {
      console.error('Failed to clone scenario:', error);
      // TODO: Add toast notification for error
    }
  };

  const openDeleteDialog = (scenarioId: string) => {
    setScenarioToDelete(scenarioId);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteScenario = async () => {
    if (!scenarioToDelete) return;
    try {
      await chatNegotiationService.deleteScenario(scenarioToDelete);
      await loadScenarios();
      // TODO: Add toast notification for success
    } catch (error) {
      console.error('Failed to delete scenario:', error);
      // TODO: Add toast notification for error
    } finally {
      setIsDeleteDialogOpen(false);
      setScenarioToDelete(null);
    }
  };

  const handleContinueSession = (sessionId: string) => {
    // Navigate to active session
    window.location.href = `/chat-negotiation/sessions/${sessionId}`;
  };

  const handleDocumentScenarioCreated = (scenario: ScenarioResponse) => {
    // Refresh scenarios list and show success message
    loadScenarios();
    // TODO: Add toast notification for success
  };

  const handleDocumentSessionCreated = (sessionId: string) => {
    // Navigate to the new session
    console.log('Navigating to session:', sessionId);
    router.push(`/chat-negotiation/sessions/${sessionId}`);
  };

  const activeSessionsCount = sessions.data.filter(s => s.status === 'active').length;
  const completedSessionsCount = sessions.data.filter(s => s.status === 'completed').length;
  const averageScore = sessions.data.length > 0 
    ? Math.round(sessions.data.reduce((sum, s) => sum + s.score, 0) / sessions.data.length)
    : 0;

  return (
    <DevOnlyWrapper>
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Negotiation Training</h1>
          <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
            Practice your negotiation skills with AI-powered scenarios
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={() => router.push('/chat-negotiation/scenarios/create')}
            size="sm"
            className="flex-1 sm:flex-none"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Scenario
          </Button>
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <Play className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
              </div>
              <div className="min-w-0">
                <p className="text-xs sm:text-sm text-gray-600 truncate">Active Sessions</p>
                <p className="text-lg sm:text-2xl font-bold">{activeSessionsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-1.5 sm:p-2 bg-green-100 rounded-lg flex-shrink-0">
                <Trophy className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
              </div>
              <div className="min-w-0">
                <p className="text-xs sm:text-sm text-gray-600 truncate">Completed</p>
                <p className="text-lg sm:text-2xl font-bold">{completedSessionsCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-1.5 sm:p-2 bg-purple-100 rounded-lg flex-shrink-0">
                <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
              </div>
              <div className="min-w-0">
                <p className="text-xs sm:text-sm text-gray-600 truncate">Avg Score</p>
                <p className="text-lg sm:text-2xl font-bold">{averageScore}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="p-1.5 sm:p-2 bg-orange-100 rounded-lg flex-shrink-0">
                <Brain className="h-4 w-4 sm:h-5 sm:w-5 text-orange-600" />
              </div>
              <div className="min-w-0">
                <p className="text-xs sm:text-sm text-gray-600 truncate">Scenarios</p>
                <p className="text-lg sm:text-2xl font-bold">{scenarios.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="scenarios">Available Scenarios</TabsTrigger>
          <TabsTrigger value="sessions">My Sessions</TabsTrigger>
          <TabsTrigger value="documents">From Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="scenarios" className="mt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {isLoadingScenarios ? (
              Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      <div className="h-10 bg-gray-200 rounded w-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              scenarios.map((scenario) => (
                <ScenarioCard
                  key={scenario.id}
                  scenario={scenario}
                  onStart={handleStartScenario}
                  onEdit={handleEditScenario}
                  onClone={handleCloneScenario}
                  onDelete={openDeleteDialog}
                />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="mt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {sessions.isLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-full"></div>
                      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      <div className="h-10 bg-gray-200 rounded w-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : sessions.data.length > 0 ? (
              sessions.data.map((session) => (
                <SessionCard
                  key={session.id}
                  session={session}
                  onContinue={handleContinueSession}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No sessions yet</h3>
                <p className="text-gray-600 mb-4">Start your first negotiation to see your sessions here</p>
                <Button onClick={() => setActiveTab('scenarios')}>
                  Browse Scenarios
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="documents" className="mt-6">
          <DocumentScenarioErrorBoundary>
            <DocumentScenarioCreator
              onScenarioCreated={handleDocumentScenarioCreated}
              onSessionCreated={handleDocumentSessionCreated}
            />
          </DocumentScenarioErrorBoundary>
        </TabsContent>
      </Tabs>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the negotiation scenario.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setScenarioToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteScenario} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    </DevOnlyWrapper>
  );
}