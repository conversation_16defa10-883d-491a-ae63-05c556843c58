"use client";

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScenarioCreationForm } from '@/components/chat-negotiation/scenario-creation-form';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import type { ScenarioResponse, CreateScenarioRequest } from '@/lib/types/chat-negotiation';

export default function EditScenarioPage() {
  const router = useRouter();
  const params = useParams();
  const scenarioId = params.scenarioId as string;

  const [scenario, setScenario] = useState<CreateScenarioRequest | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!scenarioId) return;

    const fetchScenario = async () => {
      try {
        setIsLoading(true);
        const data = await chatNegotiationService.getScenario(scenarioId);
        // Transform the response to match the form's expected input type
        const transformedData: CreateScenarioRequest = {
          name: data.name,
          description: data.description,
          industry: data.industry,
          contractType: data.contractType,
          difficulty: data.difficulty as 'beginner' | 'intermediate' | 'expert',
          timeline: {
            value: data.timeline?.expectedDuration || 30,
            unit: 'days'
          },
          parties: data.parties || [],
          initialOffer: data.initialOffer,
          constraints: data.constraints,
          tags: data.tags || []
        };
        setScenario(transformedData);
      } catch (err) {
        setError('Failed to load scenario. Please try again.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchScenario();
  }, [scenarioId]);

  const handleSuccess = () => {
    router.push('/chat-negotiation');
  };

  const handleCancel = () => {
    router.push('/chat-negotiation');
  };

  if (isLoading) {
    return <div className="container mx-auto p-6">Loading...</div>;
  }

  if (error) {
    return <div className="container mx-auto p-6 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:gap-4 sm:space-y-0">
        <Button variant="ghost" onClick={() => router.push('/chat-negotiation')} className="self-start">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Edit Scenario</h1>
          <p className="text-gray-600 text-sm sm:text-base">
            Modify your custom negotiation scenario
          </p>
        </div>
      </div>

      {scenario && (
        <ScenarioCreationForm
          initialData={scenario}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          isEditing
        />
      )}
    </div>
  );
}
