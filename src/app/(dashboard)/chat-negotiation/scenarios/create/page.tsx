"use client";

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScenarioCreationForm } from '@/components/chat-negotiation/scenario-creation-form';

export default function CreateScenarioPage() {
  const router = useRouter();

  const handleSuccess = (scenarioId: string) => {
    // Redirect to the scenario setup page for the newly created scenario
    router.push(`/chat-negotiation/${scenarioId}/setup`);
  };

  const handleCancel = () => {
    router.push('/chat-negotiation');
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:gap-4 sm:space-y-0">
        <Button variant="ghost" onClick={() => router.push('/chat-negotiation')} className="self-start">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Create Custom Scenario</h1>
          <p className="text-gray-600 text-sm sm:text-base">
            Design your own negotiation scenario for practice and training
          </p>
        </div>
      </div>

      {/* Creation Form */}
      <ScenarioCreationForm onSuccess={handleSuccess} onCancel={handleCancel} />
    </div>
  );
}