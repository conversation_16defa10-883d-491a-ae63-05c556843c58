"use client";

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Send,
  Pause,
  Play,
  Square,
  ArrowLeft,
  MessageSquare,
  TrendingUp,
  Users,
  Clock,
  Lightbulb,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Eye,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useChatNegotiation } from '@/hooks/use-chat-negotiation';
import { useNegotiationWebSocket } from '@/hooks/use-negotiation-websocket';
import type { ChatNegotiationMessage, SendMoveRequest } from '@/lib/types/chat-negotiation';
import { AnalyticsErrorBoundary } from '@/components/ErrorBoundary';

interface MessageBubbleProps {
  message: ChatNegotiationMessage;
  isLatest?: boolean;
}

function MessageBubble({ message, isLatest }: MessageBubbleProps) {
  const isUser = message.role === 'user';
  
  return (
    <div className={`mb-4 ${isUser ? 'ml-4' : 'mr-4'}`}>
      {/* Message Header */}
      <div className={`flex items-center gap-2 mb-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
          isUser
            ? 'bg-primary text-primary-foreground'
            : 'bg-muted text-muted-foreground'
        }`}>
          {isUser ? 'U' : 'AI'}
        </div>
        <span className="text-xs text-muted-foreground font-medium">
          {isUser ? 'You' : 'AI Assistant'}
        </span>
        <span className="text-xs text-muted-foreground/70">
          {message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : 'Invalid time'}
        </span>
      </div>
      
      {/* Message Content */}
      <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-[85%] ${isUser ? 'order-1' : 'order-2'}`}>
          <div
            className={`p-4 rounded-lg shadow-sm ${
              isUser
                ? 'bg-primary text-primary-foreground rounded-br-sm'
                : 'bg-card border text-card-foreground rounded-bl-sm'
            }`}
          >
          <p className="text-sm">{message.content}</p>
          
          {message.extractedData && (
            <div className="mt-3 pt-3 border-t border-opacity-20 border-gray-300">
              <div className="text-xs opacity-75 space-y-1">
                {message.extractedData.offer?.price && (
                  <div>💰 Price: ${message.extractedData.offer.price.toLocaleString()}</div>
                )}
                {message.extractedData.strategy && (
                  <div>🎯 Strategy: {message.extractedData.strategy}</div>
                )}
                {message.extractedData.sentiment && (
                  <div>😊 Sentiment: {message.extractedData.sentiment}</div>
                )}
                {message.extractedData.topic && (
                  <div>📋 Topic: {message.extractedData.topic}</div>
                )}
              </div>
            </div>
          )}
          
          {/* Show relationship impact for user messages */}
          {isUser && message.relationshipImpact && (
            <div className="mt-3 pt-3 border-t border-border/20">
              <div className="text-xs opacity-75 space-y-1">
                <div className="font-medium">Impact:</div>
                {message.relationshipImpact.trustChange !== undefined && (
                  <div className={`${message.relationshipImpact.trustChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    Trust: {message.relationshipImpact.trustChange >= 0 ? '+' : ''}{message.relationshipImpact.trustChange}
                  </div>
                )}
                {message.relationshipImpact.respectChange !== undefined && (
                  <div className={`${message.relationshipImpact.respectChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    Respect: {message.relationshipImpact.respectChange >= 0 ? '+' : ''}{message.relationshipImpact.respectChange}
                  </div>
                )}
                {message.scoreImpact !== undefined && (
                  <div className={`${message.scoreImpact >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    Score: {message.scoreImpact >= 0 ? '+' : ''}{message.scoreImpact}
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Show suggestions (either from message.suggestions or AI metadata) */}
          {((message.suggestions && message.suggestions.length > 0) ||
            (!isUser && message.aiResponseMetadata?.suggestions && message.aiResponseMetadata.suggestions.length > 0)) && (
            <div className="mt-3 pt-3 border-t border-border/20">
              <div className="text-xs opacity-75">
                <div className="flex items-center gap-1 mb-1">
                  <Lightbulb className="h-3 w-3" />
                  Suggestions:
                </div>
                <ul className="list-disc list-inside space-y-0.5">
                  {(message.suggestions || message.aiResponseMetadata?.suggestions || []).slice(0, 2).map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
          </div>
          
          {/* Processing Time */}
          {(message.processingTime || message.processingTimeMs) && (
            <div className={`text-xs text-gray-400 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
              Processing: {message.processingTime || message.processingTimeMs}ms
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function NegotiationSessionPage() {
  const params = useParams();
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const sessionId = params.sessionId as string;
  const {
    session,
    messages,
    isLoading,
    error,
    isTyping,
    currentMessage,
    setCurrentMessage,
    sendMove,
    pauseSession,
    resumeSession,
    completeSession,
    getSuggestions,
    clearError,
    canSendMessage,
    isActive,
    isPaused,
    isCompleted
  } = useChatNegotiation({ sessionId, autoLoad: true });

  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [isAiTyping, setIsAiTyping] = useState(false);

  // WebSocket integration for real-time updates - DISABLED in development to prevent infinite requests
  const webSocketResult = useNegotiationWebSocket({
    sessionId,
    enabled: process.env.NODE_ENV !== 'development' && !!sessionId && !!session && (isActive || isPaused),
    onMoveSent: (response) => {
      // Handle real-time move updates
      console.log('Real-time move update:', response);
    },
    onAiTyping: () => {
      setIsAiTyping(true);
      setTimeout(() => setIsAiTyping(false), 3000); // Clear typing indicator after 3s
    },
    onSessionUpdated: (updatedData) => {
      // Update session with real-time data
      console.log('Real-time session update:', updatedData);
    },
    onError: (error) => {
      console.error('WebSocket error:', error);
      // Don't throw errors to prevent infinite loops
    }
  });
  
  const { isConnected } = webSocketResult;

  useEffect(() => {
    // Safely scroll to end of messages
    try {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
      console.warn('Error scrolling to messages end:', error);
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!canSendMessage || !currentMessage.trim()) return;

    try {
      const request: SendMoveRequest = {
        content: currentMessage.trim(),
        context: {
          userConfidence: 0.8,
          timeSpent: 30
        }
      };

      await sendMove(request);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Don't throw to prevent infinite loops
    }
  };

  const handleGetSuggestions = async () => {
    if (!session) return;

    try {
      setIsLoadingSuggestions(true);
      const result = await getSuggestions({
        currentMessage,
        situation: 'mid_negotiation'
      });
      setSuggestions(result?.suggestions || []);
    } catch (error) {
      console.error('Failed to get suggestions:', error);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handlePause = async () => {
    try {
      await pauseSession();
    } catch (error) {
      console.error('Failed to pause session:', error);
    }
  };

  const handleResume = async () => {
    try {
      await resumeSession();
    } catch (error) {
      console.error('Failed to resume session:', error);
    }
  };

  const handleComplete = async () => {
    try {
      await completeSession();
      router.push(`/chat-negotiation/sessions/${sessionId}/results`);
    } catch (error) {
      console.error('Failed to complete session:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-3">
              <Card>
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-96 bg-gray-100 rounded"></div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Session not found</h1>
          <p className="text-gray-600 mb-4">The requested negotiation session could not be loaded.</p>
          <Button onClick={() => router.push('/chat-negotiation')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Negotiations
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AnalyticsErrorBoundary>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:gap-4 sm:space-y-0">
          <Button variant="ghost" onClick={() => router.push('/chat-negotiation')} className="self-start">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Negotiation Session</h1>
            <div className="flex flex-wrap items-center gap-2 mt-1">
              <Badge className={getStatusColor(session.status)}>
                {session.status}
              </Badge>
              <span className="text-xs sm:text-sm text-gray-600">Round {session.currentRound}</span>
              <span className="text-xs sm:text-sm text-gray-600">Score: {session.score}</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <div className="flex items-center gap-1 px-2 py-1 rounded-md text-xs">
            {isConnected ? (
              <>
                <Wifi className="h-3 w-3 text-green-500" />
                <span className="text-green-600 dark:text-green-400">Live</span>
              </>
            ) : (
              <>
                <WifiOff className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Offline</span>
              </>
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAnalytics(!showAnalytics)}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          
          {isActive && (
            <Button variant="outline" size="sm" onClick={handlePause}>
              <Pause className="h-4 w-4 mr-2" />
              Pause
            </Button>
          )}
          
          {isPaused && (
            <Button variant="outline" size="sm" onClick={handleResume}>
              <Play className="h-4 w-4 mr-2" />
              Resume
            </Button>
          )}
          
          {(isActive || isPaused) && (
            <Button variant="destructive" size="sm" onClick={handleComplete}>
              <Square className="h-4 w-4 mr-2" />
              End
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-red-700">{typeof error === 'string' ? error : 'An error occurred'}</span>
              <Button variant="ghost" size="sm" onClick={() => {
                try {
                  clearError();
                } catch (e) {
                  console.error('Error clearing error:', e);
                }
              }}>
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
        {/* Chat Area */}
        <div className="lg:col-span-3">
          <Card className="h-[600px] sm:h-[700px] lg:h-[800px] flex flex-col">
            <CardHeader className="pb-3 flex-shrink-0">
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Negotiation Chat
                {messages.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {messages.length} messages loaded from API
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Engage with your AI counterpart to reach a mutually beneficial agreement
              </CardDescription>
            </CardHeader>
            
            <CardContent className="flex-1 flex flex-col min-h-0 p-0">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4">
                {messages.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Start the conversation to begin your negotiation</p>
                  </div>
                ) : (
                   <>
                     {messages.map((message, index) => (
                       <div key={message.id}>
                         <MessageBubble
                           message={message}
                           isLatest={index === messages.length - 1}
                         />
                         {/* Add separator between conversation turns, but not after the last message */}
                         {index < messages.length - 1 && (
                           <div className="flex justify-center my-4">
                             <div className="h-px bg-gray-200 w-full max-w-xs"></div>
                           </div>
                         )}
                       </div>
                     ))}
                    {(isTyping || isAiTyping) && (
                      <div className="flex justify-start mb-4">
                        <div className="bg-muted p-4 rounded-lg">
                          <div className="flex items-center space-x-2">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                            {isAiTyping && (
                              <span className="text-xs text-muted-foreground">AI is typing...</span>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </>
                )}
              </div>

              {/* Input Area */}
              {(isActive || isPaused) && (
                <div className="border-t p-4 space-y-3 flex-shrink-0">
                  {suggestions.length > 0 && (
                    <div className="bg-muted/50 p-3 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Lightbulb className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium text-foreground">Suggestions</span>
                      </div>
                      <div className="space-y-1">
                        {suggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentMessage(suggestion)}
                            className="block w-full text-left text-sm text-muted-foreground hover:text-foreground hover:bg-muted p-2 rounded transition-colors"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex flex-col sm:flex-row gap-2">
                    <Textarea
                      value={currentMessage}
                      onChange={(e) => setCurrentMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your negotiation message..."
                      className="flex-1 min-h-[60px] sm:min-h-[80px] resize-none text-sm"
                      disabled={!isActive}
                    />
                    <div className="flex flex-row sm:flex-col gap-2">
                      <Button
                        onClick={handleSendMessage}
                        disabled={!canSendMessage}
                        size="sm"
                        className="flex-1 sm:flex-none"
                      >
                        <Send className="h-4 w-4 sm:mr-0 mr-2" />
                        <span className="sm:hidden">Send</span>
                      </Button>
                      <Button
                        onClick={handleGetSuggestions}
                        variant="outline"
                        size="sm"
                        disabled={isLoadingSuggestions || !currentMessage.trim()}
                        className="flex-1 sm:flex-none"
                      >
                        <Lightbulb className="h-4 w-4 sm:mr-0 mr-2" />
                        <span className="sm:hidden">Suggest</span>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sm:space-y-6">
          {/* Session Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Session Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Trust</span>
                    <span className="font-medium">{session.relationshipMetrics.trust}/100</span>
                  </div>
                  <Progress value={session.relationshipMetrics.trust} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Respect</span>
                    <span className="font-medium">{session.relationshipMetrics.respect}/100</span>
                  </div>
                  <Progress value={session.relationshipMetrics.respect} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Pressure</span>
                    <span className="font-medium">{session.relationshipMetrics.pressure}/100</span>
                  </div>
                  <Progress value={session.relationshipMetrics.pressure} className="h-2" />
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-600">Messages</div>
                  <div className="font-medium">{session.totalMessages}</div>
                </div>
                <div>
                  <div className="text-gray-600">Score</div>
                  <div className="font-medium">{session.score?.toFixed(3)}</div>
                </div>
                <div>
                  <div className="text-gray-600">Round</div>
                  <div className="font-medium">{session.currentRound}</div>
                </div>
                <div>
                  <div className="text-gray-600">Avg Response</div>
                  <div className="font-medium">{session.aiResponseTime}ms</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Terms */}
          {session.extractedTerms && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Current Terms
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {session.extractedTerms.price && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Price:</span>
                    <span className="font-medium">
                      {session.extractedTerms.currency || '$'}{session.extractedTerms.price.toLocaleString()}
                    </span>
                  </div>
                )}
                
                {session.extractedTerms.terms && session.extractedTerms.terms.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-600 mb-2">Terms:</div>
                    <div className="space-y-1">
                      {session.extractedTerms.terms.map((term, index) => (
                        <div key={index} className="text-sm">
                          {term}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* AI Personality */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                AI Personality
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-600">Style</div>
                  <div className="font-medium">{session.aiPersonality.communicationStyle}</div>
                </div>
                <div>
                  <div className="text-gray-600">Aggression</div>
                  <div className="font-medium">{session.aiPersonality.aggressiveness.toFixed(1)}</div>
                </div>
                <div>
                  <div className="text-gray-600">Flexibility</div>
                  <div className="font-medium">{session.aiPersonality.flexibility.toFixed(1)}</div>
                </div>
                <div>
                  <div className="text-gray-600">Risk Tolerance</div>
                  <div className="font-medium">{session.aiPersonality.riskTolerance.toFixed(1)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </AnalyticsErrorBoundary>
  );
}