"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft,
  Trophy,
  TrendingUp,
  MessageSquare,
  Clock,
  Target,
  Users,
  Download,
  Share2,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star
} from 'lucide-react';
import { useChatNegotiation } from '@/hooks/use-chat-negotiation';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';

export default function NegotiationResultsPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;
  
  const { session, loadSession, isLoading, error } = useChatNegotiation({ 
    sessionId, 
    autoLoad: true 
  });
  
  const [analytics, setAnalytics] = useState<any>(null);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);

  useEffect(() => {
    if (session && session.status === 'completed') {
      loadAnalytics();
    }
  }, [session]);

  const loadAnalytics = async () => {
    try {
      setIsLoadingAnalytics(true);
      const data = await chatNegotiationService.getSessionAnalytics(sessionId);
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setIsLoadingAnalytics(false);
    }
  };

  const handleExport = async (format: 'json' | 'pdf' | 'csv') => {
    try {
      const blob = await chatNegotiationService.exportSession(sessionId, format);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `negotiation-session-${sessionId}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export session:', error);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Negotiation Results',
          text: `I just completed a negotiation session with a score of ${session?.score}!`,
          url: window.location.href
        });
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Failed to share:', error);
    }
  };

  const handleRetry = () => {
    router.push(`/chat-negotiation/${session?.scenarioId}/setup`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-24 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Session not found</h1>
          <p className="text-gray-600 mb-4">The requested negotiation session could not be loaded.</p>
          <Button onClick={() => router.push('/chat-negotiation')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Negotiations
          </Button>
        </div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return CheckCircle;
    if (score >= 60) return AlertCircle;
    return XCircle;
  };

  const getPerformanceRating = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Great';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Fair';
    return 'Needs Improvement';
  };

  const ScoreIcon = getScoreIcon(session.score);

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:gap-4 sm:space-y-0">
          <Button variant="ghost" onClick={() => router.push('/chat-negotiation')} className="self-start">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Negotiation Results</h1>
            <p className="text-gray-600 text-sm sm:text-base">Session completed on {new Date(session.updatedAt).toLocaleDateString()}</p>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button size="sm" onClick={handleRetry}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>

      {/* Main Results */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Overall Performance */}
        <div className="lg:col-span-2 space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-yellow-500" />
                Overall Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Score Display */}
              <div className="text-center">
                <div className="flex items-center justify-center gap-3 mb-4">
                  <ScoreIcon className={`h-12 w-12 ${getScoreColor(session.score)}`} />
                  <div>
                    <div className={`text-4xl font-bold ${getScoreColor(session.score)}`}>
                      {session.score}
                    </div>
                    <div className="text-sm text-gray-600">out of 100</div>
                  </div>
                </div>
                <div className="text-xl font-semibold text-gray-900 mb-2">
                  {getPerformanceRating(session.score)}
                </div>
                <Progress value={session.score} className="h-3" />
              </div>

              <Separator />

              {/* Key Metrics */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{session.currentRound}</div>
                  <div className="text-sm text-gray-600">Rounds</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{session.totalMessages}</div>
                  <div className="text-sm text-gray-600">Messages</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round((new Date(session.updatedAt).getTime() - new Date(session.createdAt).getTime()) / 60000)}m
                  </div>
                  <div className="text-sm text-gray-600">Duration</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{session.aiResponseTime}ms</div>
                  <div className="text-sm text-gray-600">Avg Response</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="relationship">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="relationship">Relationship</TabsTrigger>
                  <TabsTrigger value="terms">Final Terms</TabsTrigger>
                  <TabsTrigger value="communication">Communication</TabsTrigger>
                </TabsList>

                <TabsContent value="relationship" className="mt-6 space-y-4">
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Trust Level</span>
                        <span className="text-sm text-gray-600">{session.relationshipMetrics.trust}/100</span>
                      </div>
                      <Progress value={session.relationshipMetrics.trust} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Respect Level</span>
                        <span className="text-sm text-gray-600">{session.relationshipMetrics.respect}/100</span>
                      </div>
                      <Progress value={session.relationshipMetrics.respect} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Pressure Level</span>
                        <span className="text-sm text-gray-600">{session.relationshipMetrics.pressure}/100</span>
                      </div>
                      <Progress value={session.relationshipMetrics.pressure} className="h-2" />
                      <p className="text-xs text-gray-500 mt-1">Lower is better</p>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="terms" className="mt-6">
                  {session.extractedTerms ? (
                    <div className="space-y-4">
                      {session.extractedTerms.price && (
                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <span className="font-medium">Final Price:</span>
                          <span className="text-lg font-bold">
                            {session.extractedTerms.currency || '$'}{session.extractedTerms.price.toLocaleString()}
                          </span>
                        </div>
                      )}
                      
                      {session.extractedTerms.terms && session.extractedTerms.terms.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-3">Agreed Terms:</h4>
                          <div className="space-y-2">
                            {session.extractedTerms.terms.map((term, index) => (
                              <div key={index} className="flex items-start gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                <span className="text-sm dark:text-gray-200">{term}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No final terms were extracted from this session.</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="communication" className="mt-6">
                  {analytics ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-xl font-bold text-blue-600">{analytics.communication.messagesCount}</div>
                          <div className="text-sm text-gray-600">Total Messages</div>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-xl font-bold text-green-600">{analytics.communication.averageResponseTime}ms</div>
                          <div className="text-sm text-gray-600">Avg Response Time</div>
                        </div>
                      </div>
                      
                      {analytics.communication.sentimentAnalysis && (
                        <div>
                          <h4 className="font-medium mb-3">Communication Style</h4>
                          <div className="text-sm text-gray-600">
                            Your overall communication was {analytics.communication.sentimentAnalysis.overall || 'neutral'} in tone.
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-500">Loading communication analysis...</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sm:space-y-6">
          {/* AI Opponent */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                AI Opponent
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <div className="font-medium">Communication Style</div>
                <div className="text-sm text-gray-600">{session.aiPersonality.communicationStyle}</div>
              </div>
              
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <div className="text-gray-600">Aggressiveness</div>
                  <div className="font-medium">{session.aiPersonality.aggressiveness.toFixed(1)}</div>
                </div>
                <div>
                  <div className="text-gray-600">Flexibility</div>
                  <div className="font-medium">{session.aiPersonality.flexibility.toFixed(1)}</div>
                </div>
                <div>
                  <div className="text-gray-600">Risk Tolerance</div>
                  <div className="font-medium">{session.aiPersonality.riskTolerance.toFixed(1)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => handleExport('json')}
              >
                <Download className="h-4 w-4 mr-2" />
                Download Report
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/chat-negotiation')}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                New Negotiation
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={handleRetry}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Scenario
              </Button>
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {session.score >= 80 && (
                  <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-lg">
                    <Trophy className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">High Performer</span>
                  </div>
                )}
                
                {session.relationshipMetrics.trust >= 80 && (
                  <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
                    <Users className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Trust Builder</span>
                  </div>
                )}
                
                {session.currentRound <= 5 && (
                  <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                    <Clock className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Quick Closer</span>
                  </div>
                )}
                
                {session.relationshipMetrics.pressure <= 30 && (
                  <div className="flex items-center gap-2 p-2 bg-purple-50 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Calm Negotiator</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}