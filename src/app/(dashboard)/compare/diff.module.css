.diffContainer {
  /* Container styles */
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.diffContainer [data-diff="add"] {
  background-color: rgba(74, 222, 128, 0.2);
  color: #166534;
}

.diffContainer [data-diff="remove"] {
  background-color: rgba(248, 113, 113, 0.2);
  color: #b91c1c;
}

.diffContainer [data-diff="change"] {
  background-color: rgba(251, 191, 36, 0.2);
  color: #92400e;
}

/* Dark mode styles */
:global(.dark) .diffContainer [data-diff="add"] {
  background-color: rgba(22, 163, 74, 0.85);
  color: white;
}

:global(.dark) .diffContainer [data-diff="remove"] {
  background-color: rgba(220, 38, 38, 0.85);
  color: white;
}

:global(.dark) .diffContainer [data-diff="change"] {
  background-color: rgba(217, 119, 6, 0.85);
  color: white;
}