"use client";

import { Suspense, useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ArrowLeftRight, Loader2, Sparkles, FileText, Zap, BarChart3, ChevronRight, Info } from "lucide-react";
import { DocumentComparisonSelector } from "@/components/document-comparison/document-comparison-selector";
import { EnhancedComparison } from "@/components/document-comparison/enhanced-comparison";
import { documentComparisonService, type EnhancedComparisonResult, type EnhancedComparisonRequest } from "@/lib/services/document-comparison-service";
import { documentService } from "@/lib/services/document-service";
import { useToast } from "@/hooks/use-toast";
import { Spinner } from "@/components/ui/spinner";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";

export const dynamic = 'force-dynamic';

function EnhancedComparisonContent() {
  type SelectedDocuments = {
    documentA: string;
    documentB: string;
  };

  const searchParams = useSearchParams();
  const docA = searchParams.get('docA') || "";
  const docB = searchParams.get('docB') || "";

  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocuments>({ 
    documentA: docA, 
    documentB: docB 
  });
  const [comparisonResult, setComparisonResult] = useState<EnhancedComparisonResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [, setDocumentAContent] = useState("");
  const [, setDocumentBContent] = useState("");
  const [documents, setDocuments] = useState<any[]>([]);
  const [showingSelector, setShowingSelector] = useState(true);
  const { toast } = useToast();

  // Fetch documents for name display
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const response = await documentService.getDocuments(1, 100);
        setDocuments(response.items || []);
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    };
    fetchDocuments();
  }, []);

  const getDocumentName = (docId: string) => {
    const doc = documents.find(d => d.id === docId);
    return doc ? (doc.filename || doc.name || `Document ${docId.substring(0, 8)}...`) : `Document ${docId.substring(0, 8)}...`;
  };

  const handleEnhancedCompare = (documentA: string, documentB: string) => {
    setSelectedDocuments({
      documentA,
      documentB,
    });
    setShowingSelector(false);
  };

  const handleStartComparison = () => {
    // This will trigger the useEffect to start the actual comparison
    setSelectedDocuments(prev => ({ ...prev }));
  };

  const resetSelection = () => {
    setSelectedDocuments({ documentA: "", documentB: "" });
    setComparisonResult(null);
    setShowingSelector(true);
  };

  // Comparison logic
  useEffect(() => {
    if (selectedDocuments.documentA && selectedDocuments.documentB && !showingSelector && !comparisonResult) {
      const performComparison = async () => {
        try {
          setLoading(true);
          
          // Fetch document contents
          const [docAContent, docBContent] = await Promise.all([
            documentService.getDocumentContent(selectedDocuments.documentA),
            documentService.getDocumentContent(selectedDocuments.documentB)
          ]);

          setDocumentAContent(docAContent);
          setDocumentBContent(docBContent);

          // Perform enhanced comparison
          const comparisonRequest: EnhancedComparisonRequest = {
            documentA: docAContent,
            documentB: docBContent,
            type: "both",
            includeVisualDiff: true,
            includeSectionReferences: true,
            documentAMetadata: {
              id: selectedDocuments.documentA,
              title: getDocumentName(selectedDocuments.documentA),
              createdAt: new Date().toISOString(),
              organizationId: "",
              userId: ""
            },
            documentBMetadata: {
              id: selectedDocuments.documentB,
              title: getDocumentName(selectedDocuments.documentB),
              createdAt: new Date().toISOString(),
              organizationId: "",
              userId: ""
            }
          };

          const result = await documentComparisonService.compareDocuments(comparisonRequest);
          setComparisonResult(result);

          toast({
            title: "Comparison Complete",
            description: "Enhanced document comparison has been generated successfully.",
          });
        } catch (error) {
          console.error("Error performing enhanced comparison:", error);
          toast({
            title: "Comparison Failed",
            description: "Failed to perform enhanced comparison. Please try again.",
            variant: "destructive",
          });
        } finally {
          setLoading(false);
        }
      };

      performComparison();
    }
  }, [selectedDocuments, showingSelector, comparisonResult, toast, getDocumentName]);

  const renderSelectionUI = () => (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="text-center py-8">
        <div className="flex justify-center mb-4">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
              <Zap className="w-3 h-3 text-yellow-900" />
            </div>
          </div>
        </div>
        <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Enhanced Document Comparison
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Powered by AI for deep analysis, visual diff highlighting, and intelligent insights
        </p>
      </div>

      {/* Features Preview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card className="border-blue-200 bg-blue-50/50 dark:bg-blue-950/20 dark:border-blue-800">
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <BarChart3 className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="font-semibold text-sm">AI-Powered Analysis</h3>
            </div>
            <p className="text-xs text-muted-foreground">
              Intelligent comparison with context-aware insights and recommendations
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-purple-200 bg-purple-50/50 dark:bg-purple-950/20 dark:border-purple-800">
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <FileText className="w-5 h-5 text-purple-600 mr-2" />
              <h3 className="font-semibold text-sm">Visual Diff Highlighting</h3>
            </div>
            <p className="text-xs text-muted-foreground">
              Side-by-side comparison with precise change visualization
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-green-200 bg-green-50/50 dark:bg-green-950/20 dark:border-green-800">
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <Sparkles className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="font-semibold text-sm">Section References</h3>
            </div>
            <p className="text-xs text-muted-foreground">
              Detailed section-by-section analysis with cross-references
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Selection Card */}
      <Card className="shadow-lg border-2 border-primary/20">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-b">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center">
                <FileText className="w-6 h-6 mr-3 text-primary" />
                Select Documents to Compare
              </CardTitle>
              <CardDescription className="mt-1">
                Choose two documents for enhanced AI-powered comparison and analysis
              </CardDescription>
            </div>
            <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
              <Sparkles className="w-3 h-3 mr-1" />
              Enhanced
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <Alert className="mb-6 border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800 dark:text-blue-200">
              Enhanced comparison provides AI-powered insights, visual diff highlighting, and detailed section analysis.
              Best for legal documents, contracts, and complex text comparisons.
            </AlertDescription>
          </Alert>
          
          <DocumentComparisonSelector
            onCompare={(primaryDocId, relatedDocIds) => {
              if (relatedDocIds.length > 0) {
                handleEnhancedCompare(primaryDocId, relatedDocIds[0]);
              }
            }}
            onCancel={() => {}}
          />
        </CardContent>
      </Card>

      {/* Additional Info */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Looking for a simpler comparison? Try our{" "}
          <Button variant="link" className="p-0 h-auto text-sm" onClick={() => window.location.href = "/compare/basic"}>
            Basic Comparison
          </Button>
          {" "}tool instead.
        </p>
      </div>
    </div>
  );

  const renderConfirmationStep = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Confirm Document Selection</h2>
        <p className="text-muted-foreground">
          Review your selected documents before starting the enhanced comparison
        </p>
      </div>

      {/* Selected Documents Display */}
      <Card className="shadow-lg border-2 border-green-200 bg-green-50/30 dark:bg-green-950/20 dark:border-green-800">
        <CardHeader className="bg-green-100/50 dark:bg-green-950/30 border-b border-green-200 dark:border-green-800">
          <CardTitle className="text-lg flex items-center text-green-800 dark:text-green-200">
            <FileText className="w-5 h-5 mr-2" />
            Selected Documents
          </CardTitle>
          <CardDescription className="text-green-700 dark:text-green-300">
            These documents will be compared using enhanced AI analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Primary Document */}
            <div className="space-y-3">
              <div className="flex items-center">
                <Badge variant="default" className="bg-blue-600 text-white mr-2">
                  Primary
                </Badge>
                <h3 className="font-semibold">First Document</h3>
              </div>
              <Card className="border-blue-200 bg-blue-50/50 dark:bg-blue-950/20 dark:border-blue-700">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <FileText className="w-8 h-8 text-blue-600 mr-3" />
                    <div>
                      <p className="font-medium text-blue-900 dark:text-blue-100">
                        {getDocumentName(selectedDocuments.documentA)}
                      </p>
                      <p className="text-xs text-blue-700 dark:text-blue-300">
                        ID: {selectedDocuments.documentA.substring(0, 8)}...
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Related Document */}
            <div className="space-y-3">
              <div className="flex items-center">
                <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2">
                  Compare To
                </Badge>
                <h3 className="font-semibold">Second Document</h3>
              </div>
              <Card className="border-purple-200 bg-purple-50/50 dark:bg-purple-950/20 dark:border-purple-700">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <FileText className="w-8 h-8 text-purple-600 mr-3" />
                    <div>
                      <p className="font-medium text-purple-900 dark:text-purple-100">
                        {getDocumentName(selectedDocuments.documentB)}
                      </p>
                      <p className="text-xs text-purple-700 dark:text-purple-300">
                        ID: {selectedDocuments.documentB.substring(0, 8)}...
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Comparison Arrow */}
          <div className="flex justify-center my-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <ArrowLeftRight className="w-5 h-5 text-white" />
              </div>
              <div className="w-12 h-0.5 bg-gradient-to-r from-purple-500 to-blue-500"></div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="outline"
              onClick={resetSelection}
              className="flex items-center"
            >
              ← Change Documents
            </Button>
            <Button
              onClick={handleStartComparison}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex items-center"
              size="lg"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Start Enhanced Comparison
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* What to Expect */}
      <Card className="border-amber-200 bg-amber-50/30 dark:bg-amber-950/20 dark:border-amber-800">
        <CardContent className="p-4">
          <h4 className="font-semibold text-amber-800 dark:text-amber-200 mb-2 flex items-center">
            <Info className="w-4 h-4 mr-2" />
            What to Expect
          </h4>
          <ul className="text-sm text-amber-700 dark:text-amber-300 space-y-1">
            <li>• AI-powered analysis of document differences and similarities</li>
            <li>• Visual highlighting of changes with precise line-by-line comparison</li>
            <li>• Section-by-section breakdown with intelligent insights</li>
            <li>• Processing may take 30-60 seconds for large documents</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="max-w-6xl mx-auto">
        {showingSelector ? (
          renderSelectionUI()
        ) : !selectedDocuments.documentA || !selectedDocuments.documentB ? (
          renderSelectionUI()
        ) : !loading && !comparisonResult ? (
          renderConfirmationStep()
        ) : (
          <div>
            <div className="flex items-center justify-between mb-6">
              <Button
                variant="outline"
                onClick={resetSelection}
                className="flex items-center"
              >
                ← Back to Selection
              </Button>
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                <Sparkles className="w-3 h-3 mr-1" />
                Enhanced Mode
              </Badge>
            </div>
            
            {loading ? (
              <Card className="shadow-lg">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                      <Loader2 className="w-8 h-8 text-white animate-spin" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Analyzing Documents</h3>
                  <p className="text-muted-foreground text-center max-w-md">
                    Our AI is performing enhanced comparison with visual diff highlighting and section analysis.
                    This may take a moment for large documents.
                  </p>
                  <div className="flex items-center mt-4 space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : comparisonResult ? (
              <EnhancedComparison result={comparisonResult} />
            ) : (
              <Card className="shadow-lg border-destructive/20">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <div className="w-16 h-16 bg-destructive/10 rounded-2xl flex items-center justify-center mb-4">
                    <FileText className="w-8 h-8 text-destructive" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Comparison Failed</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    We couldn't generate the enhanced comparison. Please try again or select different documents.
                  </p>
                  <Button 
                    onClick={resetSelection}
                    className="flex items-center"
                  >
                    <ChevronRight className="w-4 h-4 mr-2" />
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function EnhancedComparisonPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    }>
      <EnhancedComparisonContent />
    </Suspense>
  );
}
