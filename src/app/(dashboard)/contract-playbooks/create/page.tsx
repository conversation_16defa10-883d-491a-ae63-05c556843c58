"use client";

import React, { Suspense, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Plus, Trash2, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useCreatePlaybook } from '@/lib/services/contract-playbooks-service';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import { CONTRACT_TYPES, RULE_TYPES, SEVERITY_LEVELS } from '@/lib/types/contract-playbooks';
import type { CreatePlaybookRequest, CreatePlaybookRule } from '@/lib/types/contract-playbooks';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

function CreatePlaybookPageContent() {
  const router = useRouter();
  const { toast } = useToast();
  const createPlaybook = useCreatePlaybook();

  const [formData, setFormData] = useState<CreatePlaybookRequest>({
    name: '',
    contractType: 'nda',
    description: '',
    version: '1.0.0',
    rules: [],
    metadata: {
      industry: '',
      jurisdiction: '',
      riskProfile: 'medium',
      tags: [],
    },
    isActive: true,
    isTemplate: false,
  });

  const [newRule, setNewRule] = useState<CreatePlaybookRule>({
    name: '',
    category: '',
    ruleType: 'required_clause',
    severity: 'MEDIUM',
    criteria: {
      keywords: [],
      semanticConcepts: [],
    },
    description: '',
    isActive: true,
  });

  const [keywordInput, setKeywordInput] = useState('');
  const [conceptInput, setConceptInput] = useState('');
  const [tagInput, setTagInput] = useState('');

  const handleInputChange = (field: keyof CreatePlaybookRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleMetadataChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      metadata: { ...prev.metadata, [field]: value }
    }));
  };

  const handleRuleChange = (field: keyof CreatePlaybookRule, value: any) => {
    setNewRule(prev => ({ ...prev, [field]: value }));
  };

  const handleCriteriaChange = (field: string, value: any) => {
    setNewRule(prev => ({
      ...prev,
      criteria: { ...prev.criteria, [field]: value }
    }));
  };

  const addKeyword = () => {
    if (keywordInput.trim()) {
      const keywords = [...(newRule.criteria.keywords || []), keywordInput.trim()];
      handleCriteriaChange('keywords', keywords);
      setKeywordInput('');
    }
  };

  const removeKeyword = (index: number) => {
    const keywords = newRule.criteria.keywords?.filter((_, i) => i !== index) || [];
    handleCriteriaChange('keywords', keywords);
  };

  const addConcept = () => {
    if (conceptInput.trim()) {
      const concepts = [...(newRule.criteria.semanticConcepts || []), conceptInput.trim()];
      handleCriteriaChange('semanticConcepts', concepts);
      setConceptInput('');
    }
  };

  const removeConcept = (index: number) => {
    const concepts = newRule.criteria.semanticConcepts?.filter((_, i) => i !== index) || [];
    handleCriteriaChange('semanticConcepts', concepts);
  };

  const addTag = () => {
    if (tagInput.trim()) {
      const tags = [...(formData.metadata.tags || []), tagInput.trim()];
      handleMetadataChange('tags', tags);
      setTagInput('');
    }
  };

  const removeTag = (index: number) => {
    const tags = formData.metadata.tags?.filter((_, i) => i !== index) || [];
    handleMetadataChange('tags', tags);
  };

  const addRule = () => {
    if (!newRule.name.trim() || !newRule.category.trim() || !newRule.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required rule fields.",
        variant: "destructive",
      });
      return;
    }

    setFormData(prev => ({
      ...prev,
      rules: [...prev.rules, { ...newRule }]
    }));

    setNewRule({
      name: '',
      category: '',
      ruleType: 'required_clause',
      severity: 'MEDIUM',
      criteria: {
        keywords: [],
        semanticConcepts: [],
      },
      description: '',
      isActive: true,
    });
    setKeywordInput('');
    setConceptInput('');
  };

  const removeRule = (index: number) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a playbook name.",
        variant: "destructive",
      });
      return;
    }

    if (formData.rules.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please add at least one rule to the playbook.",
        variant: "destructive",
      });
      return;
    }

    try {
      const playbook = await createPlaybook.mutateAsync(formData);
      toast({
        title: "Playbook created",
        description: `"${formData.name}" has been successfully created.`,
      });
      router.push(`/contract-playbooks/${playbook.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <FeatureGuard featureId="contract_playbooks">
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Create Contract Playbook</h1>
            <p className="text-muted-foreground">
              Define rules and criteria for automated contract analysis.
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Set up the basic details for your contract playbook.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Standard NDA Playbook"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contractType">Contract Type *</Label>
                  <Select
                    value={formData.contractType}
                    onValueChange={(value) => handleInputChange('contractType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CONTRACT_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe the purpose and scope of this playbook..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={formData.version}
                    onChange={(e) => handleInputChange('version', e.target.value)}
                    placeholder="1.0.0"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isTemplate"
                    checked={formData.isTemplate}
                    onCheckedChange={(checked) => handleInputChange('isTemplate', checked)}
                  />
                  <Label htmlFor="isTemplate">Template</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
              <CardDescription>
                Additional information to categorize and organize your playbook.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="industry">Industry</Label>
                  <Input
                    id="industry"
                    value={formData.metadata.industry || ''}
                    onChange={(e) => handleMetadataChange('industry', e.target.value)}
                    placeholder="e.g., Technology, Healthcare"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="jurisdiction">Jurisdiction</Label>
                  <Input
                    id="jurisdiction"
                    value={formData.metadata.jurisdiction || ''}
                    onChange={(e) => handleMetadataChange('jurisdiction', e.target.value)}
                    placeholder="e.g., United States, California"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="riskProfile">Risk Profile</Label>
                  <Select
                    value={formData.metadata.riskProfile || 'medium'}
                    onValueChange={(value) => handleMetadataChange('riskProfile', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex gap-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Add a tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} size="sm">
                    Add
                  </Button>
                </div>
                {formData.metadata.tags && formData.metadata.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.metadata.tags.map((tag, index) => (
                      <div key={index} className="flex items-center gap-1 bg-secondary px-2 py-1 rounded text-sm">
                        {tag}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0"
                          onClick={() => removeTag(index)}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rules ({formData.rules.length})</CardTitle>
              <CardDescription>
                Define the analysis rules that will be applied to contracts.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {formData.rules.length > 0 && (
                <div className="space-y-4">
                  <h4 className="font-medium">Current Rules</h4>
                  {formData.rules.map((rule, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h5 className="font-medium">{rule.name}</h5>
                        <p className="text-sm text-muted-foreground">{rule.description}</p>
                        <div className="flex gap-2 mt-2">
                          <span className="text-xs bg-secondary px-2 py-1 rounded">{rule.ruleType}</span>
                          <span className="text-xs bg-secondary px-2 py-1 rounded">{rule.severity}</span>
                          <span className="text-xs bg-secondary px-2 py-1 rounded">{rule.category}</span>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => removeRule(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Separator />
                </div>
              )}

              <div className="space-y-4">
                <h4 className="font-medium">Add New Rule</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ruleName">Rule Name *</Label>
                    <Input
                      id="ruleName"
                      value={newRule.name}
                      onChange={(e) => handleRuleChange('name', e.target.value)}
                      placeholder="e.g., Confidentiality Definition"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ruleCategory">Category *</Label>
                    <Input
                      id="ruleCategory"
                      value={newRule.category}
                      onChange={(e) => handleRuleChange('category', e.target.value)}
                      placeholder="e.g., Definitions, Terms"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ruleType">Rule Type *</Label>
                    <Select
                      value={newRule.ruleType}
                      onValueChange={(value) => handleRuleChange('ruleType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {RULE_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ruleSeverity">Severity *</Label>
                    <Select
                      value={newRule.severity}
                      onValueChange={(value) => handleRuleChange('severity', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {SEVERITY_LEVELS.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ruleDescription">Description *</Label>
                  <Textarea
                    id="ruleDescription"
                    value={newRule.description}
                    onChange={(e) => handleRuleChange('description', e.target.value)}
                    placeholder="Describe what this rule checks for..."
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Keywords</Label>
                  <div className="flex gap-2">
                    <Input
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      placeholder="Add a keyword..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                    />
                    <Button type="button" onClick={addKeyword} size="sm">
                      Add
                    </Button>
                  </div>
                  {newRule.criteria.keywords && newRule.criteria.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {newRule.criteria.keywords.map((keyword, index) => (
                        <div key={index} className="flex items-center gap-1 bg-secondary px-2 py-1 rounded text-sm">
                          {keyword}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0"
                            onClick={() => removeKeyword(index)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Semantic Concepts</Label>
                  <div className="flex gap-2">
                    <Input
                      value={conceptInput}
                      onChange={(e) => setConceptInput(e.target.value)}
                      placeholder="Add a semantic concept..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addConcept())}
                    />
                    <Button type="button" onClick={addConcept} size="sm">
                      Add
                    </Button>
                  </div>
                  {newRule.criteria.semanticConcepts && newRule.criteria.semanticConcepts.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {newRule.criteria.semanticConcepts.map((concept, index) => (
                        <div key={index} className="flex items-center gap-1 bg-secondary px-2 py-1 rounded text-sm">
                          {concept}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0"
                            onClick={() => removeConcept(index)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="ruleActive"
                    checked={newRule.isActive}
                    onCheckedChange={(checked) => handleRuleChange('isActive', checked)}
                  />
                  <Label htmlFor="ruleActive">Rule Active</Label>
                </div>

                <Button type="button" onClick={addRule} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Add Rule
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={createPlaybook.isPending} className="gap-2">
              <Save className="h-4 w-4" />
              {createPlaybook.isPending ? 'Creating...' : 'Create Playbook'}
            </Button>
          </div>
        </form>
      </div>
    </FeatureGuard>
  );
}

export default function CreatePlaybookPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    }>
      <CreatePlaybookPageContent />
    </Suspense>
  );
}
