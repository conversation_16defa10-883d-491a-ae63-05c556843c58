"use client";

import { DevOnlyWrapper } from '@/components/dev-only-wrapper';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Search, Filter, BookOpen, FileCheck, MoreVertical, Edit, Trash2, Copy, Download } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { usePlaybooks, useDeletePlaybook, useDuplicatePlaybook, useExportPlaybook } from '@/lib/services/contract-playbooks-service';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import { CONTRACT_TYPES } from '@/lib/types/contract-playbooks';
import type { ContractPlaybook, PlaybookSearchParams } from '@/lib/types/contract-playbooks';
import { format } from 'date-fns';
import { DevOnlyRoute } from "@/components/dev-only-wrapper";

export default function ContractPlaybooksPage() {
  return (
    <DevOnlyRoute>
      <ContractPlaybooksPageContent />
    </DevOnlyRoute>
  );
}

function ContractPlaybooksPageContent() {
  const router = useRouter();
  const { toast } = useToast();
  const [searchParams, setSearchParams] = useState<PlaybookSearchParams>({
    page: 1,
    limit: 20,
  });

  // API hooks
  const { data: playbooksData, isLoading, error, refetch } = usePlaybooks(searchParams);
  const deletePlaybook = useDeletePlaybook();
  const duplicatePlaybook = useDuplicatePlaybook();
  const exportPlaybook = useExportPlaybook();

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({ ...prev, query: query || undefined, page: 1 }));
  };

  const handleFilterChange = (key: keyof PlaybookSearchParams, value: string | boolean | undefined) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deletePlaybook.mutateAsync(id);
      toast({
        title: "Playbook deleted",
        description: `"${name}" has been successfully deleted.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDuplicate = async (id: string, name: string) => {
    try {
      await duplicatePlaybook.mutateAsync({
        id,
        data: { name: `${name} (Copy)` }
      });
      toast({
        title: "Playbook duplicated",
        description: `"${name}" has been successfully duplicated.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExport = async (id: string, name: string) => {
    try {
      const exportData = await exportPlaybook.mutateAsync(id);
      
      // Create and download file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_playbook.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Playbook exported",
        description: `"${name}" has been successfully exported.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getContractTypeLabel = (type: string) => {
    return CONTRACT_TYPES.find(ct => ct.value === type)?.label || type;
  };

  const getRiskLevelColor = (riskProfile?: string) => {
    switch (riskProfile?.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DevOnlyWrapper>
      <FeatureGuard featureId="contract_playbooks" fallback={
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <FileCheck className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Contract Playbooks</h2>
          <p className="text-muted-foreground mb-6">
            Upgrade to PRO to access automated contract analysis with customizable playbooks.
          </p>
          <Button onClick={() => router.push('/subscription')}>
            Upgrade to PRO
          </Button>
        </div>
      </div>
    }>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <BookOpen className="h-8 w-8" />
              Contract Playbooks
            </h1>
            <p className="text-muted-foreground mt-1">
              Create and manage automated contract analysis rules and workflows.
            </p>
          </div>
          <Button onClick={() => router.push('/contract-playbooks/create')} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Playbook
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search playbooks..."
                    className="pl-10"
                    value={searchParams.query || ''}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>
              </div>
              <Select
                value={searchParams.contractType || 'all'}
                onValueChange={(value) => handleFilterChange('contractType', value === 'all' ? undefined : value)}
              >
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Contract Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {CONTRACT_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={searchParams.isActive === undefined ? 'all' : searchParams.isActive ? 'active' : 'inactive'}
                onValueChange={(value) => handleFilterChange('isActive', value === 'all' ? undefined : value === 'active')}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>


        {/* User Playbooks Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pt-6">
            <FileCheck className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">Your Contract Playbooks</h2>
            {playbooksData && (
              <Badge variant="outline">{playbooksData.total} playbooks</Badge>
            )}
          </div>

        {/* Content */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">Failed to load playbooks. Please try again.</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </CardContent>
          </Card>
        ) : !playbooksData?.playbooks.length ? (
          <Card>
            <CardContent className="p-8 text-center">
              <BookOpen className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {searchParams.query || searchParams.contractType
                  ? "No playbooks found"
                  : "No Personal Playbooks Yet"
                }
              </h3>
              <p className="text-muted-foreground mb-6">
                {searchParams.query || searchParams.contractType
                  ? "No playbooks match your current filters. Try adjusting your search criteria."
                  : "Create your first custom contract playbook or clone one from the sample templates above to get started."
                }
              </p>
              <div className="flex gap-3 justify-center">
                <Button onClick={() => router.push('/contract-playbooks/create')}>
                  Create New Playbook
                </Button>
                {!(searchParams.query || searchParams.contractType) && (
                  <Button variant="outline" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
                    Browse Sample Templates
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Playbooks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {playbooksData.playbooks.map((playbook: ContractPlaybook) => (
                <PlaybookCard
                  key={playbook.id}
                  playbook={playbook}
                  onEdit={() => router.push(`/contract-playbooks/${playbook.id}/edit`)}
                  onView={() => router.push(`/contract-playbooks/${playbook.id}`)}
                  onDelete={() => handleDelete(playbook.id, playbook.name)}
                  onDuplicate={() => handleDuplicate(playbook.id, playbook.name)}
                  onExport={() => handleExport(playbook.id, playbook.name)}
                  getContractTypeLabel={getContractTypeLabel}
                  getRiskLevelColor={getRiskLevelColor}
                />
              ))}
            </div>

            {/* Pagination */}
            {playbooksData.totalPages > 1 && (
              <div className="flex justify-center gap-2">
                <Button
                  variant="outline"
                  disabled={searchParams.page === 1}
                  onClick={() => setSearchParams(prev => ({ ...prev, page: (prev.page || 1) - 1 }))}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {searchParams.page} of {playbooksData.totalPages}
                </span>
                <Button
                  variant="outline"
                  disabled={searchParams.page === playbooksData.totalPages}
                  onClick={() => setSearchParams(prev => ({ ...prev, page: (prev.page || 1) + 1 }))}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
        </div>
      </div>
      </FeatureGuard>
    </DevOnlyWrapper>
  );
}

interface PlaybookCardProps {
  playbook: ContractPlaybook;
  onEdit: () => void;
  onView: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onExport: () => void;
  getContractTypeLabel: (type: string) => string;
  getRiskLevelColor: (riskProfile?: string) => string;
}

function PlaybookCard({ 
  playbook, 
  onEdit, 
  onView, 
  onDelete, 
  onDuplicate, 
  onExport,
  getContractTypeLabel,
  getRiskLevelColor 
}: PlaybookCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={onView}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-1">{playbook.name}</CardTitle>
            <CardDescription className="line-clamp-2">
              {playbook.description || 'No description provided'}
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onEdit(); }}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onDuplicate(); }}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onExport(); }}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={(e) => { e.stopPropagation(); onDelete(); }}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="secondary">
              {getContractTypeLabel(playbook.contractType)}
            </Badge>
            <Badge 
              variant="outline" 
              className={getRiskLevelColor(playbook.metadata.riskProfile)}
            >
              {playbook.metadata.riskProfile || 'Unknown'} Risk
            </Badge>
            <Badge variant={playbook.isActive ? "default" : "secondary"}>
              {playbook.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          
          <div className="text-sm text-muted-foreground">
            <div className="flex justify-between">
              <span>{playbook.rules.length} rules</span>
              <span>v{playbook.version}</span>
            </div>
            <div className="mt-1">
              Updated {new Date(playbook.updatedAt).toLocaleDateString()}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
