"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>ir<PERSON>, Coins, ArrowRight } from "lucide-react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Spinner } from "@/components/ui/spinner";

export function CreditPurchaseSuccessClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const { refreshCreditBalance } = useSubscription();
  const [isLoading, setIsLoading] = useState(true);
  const [isVerified, setIsVerified] = useState(false);

  useEffect(() => {
    const verifyPurchase = async () => {
      // Skip if already verified to prevent loop calls
      if (isVerified) return;
      
      if (!sessionId) {
        router.push("/subscription");
        return;
      }

      try {
        // Refresh credit balance to get the latest credit details
        await refreshCreditBalance();
        setIsLoading(false);
        setIsVerified(true); // Mark as verified to prevent additional calls
      } catch (error) {
        console.error("Error verifying credit purchase:", error);
        router.push("/subscription");
      }
    };

    verifyPurchase();
  }, [sessionId, refreshCreditBalance, router, isVerified]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <Card className="text-center">
        <CardHeader className="pb-4">
          <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-green-600">
            Credit Purchase Successful!
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Your credit purchase has been processed successfully. The credits have been added to your account and are ready to use.
            </p>
            
            <div className="flex items-center justify-center gap-2 text-lg font-semibold">
              <Coins className="h-5 w-5 text-primary" />
              <span>Credits Added to Your Account</span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={() => router.push("/subscription")}
              className="flex items-center gap-2"
            >
              View Credit Balance
              <ArrowRight className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push("/chat")}
              className="flex items-center gap-2"
            >
              Start Using Credits
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              You will receive an email confirmation shortly with your purchase details.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}