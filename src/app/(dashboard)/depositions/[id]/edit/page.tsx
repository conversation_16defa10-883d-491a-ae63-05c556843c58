import { DepositionForm } from "@/components/deposition/deposition-form"
import { depositionService } from "@/lib/services/deposition-service"

type EditDepositionPageProps = {
  params: Promise<{ id: string }>
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function EditDepositionPage({ params }: EditDepositionPageProps) {
  const { id } = await params
  const deposition = await depositionService.getDepositionPreparation(id)

  return (
    <div className="container py-6">
      <DepositionForm initialData={deposition} isEditing={true} />
    </div>
  )
}

export async function generateMetadata({ params }: EditDepositionPageProps) {
  const { id } = await params

  return {
    title: `Edit Deposition | Legal Document Analysis`,
    description: "Edit deposition preparation details",
  }
}
