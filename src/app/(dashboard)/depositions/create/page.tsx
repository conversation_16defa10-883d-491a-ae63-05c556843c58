import { Suspense } from 'react';
import { CreateDepositionClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const metadata = {
  title: "Create Deposition | Legal Document Analysis",
  description: "Create a new deposition preparation",
};

export const dynamic = 'force-dynamic';

export default function CreateDepositionPage() {
  return (
    <div className="container py-6">
      <Suspense fallback={
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" />
        </div>
      }>
        <CreateDepositionClient />
      </Suspense>
    </div>
  );
}
