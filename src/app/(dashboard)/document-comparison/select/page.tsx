import { Suspense } from 'react';
import { DocumentSelectionClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

export default function DocumentSelectionPage() {
  return (
    <div className="container py-6 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">Document Comparison</h1>
      <Suspense fallback={
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" />
        </div>
      }>
        <DocumentSelectionClient />
      </Suspense>
    </div>
  );
}
