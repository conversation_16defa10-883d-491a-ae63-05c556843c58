"use client";

import { DocumentUploader } from "@/components/documents/document-uploader";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";

export function UploadDocumentClient() {
  const router = useRouter();
  const { toast } = useToast();

  const handleUploadComplete = () => {
    toast({
      title: "Upload Complete",
      description: "Your document has been uploaded successfully.",
    });

    // Navigate to the documents list
    router.push("/documents");
  };

  const handleError = (error: Error) => {
    toast({
      title: "Upload Failed",
      description: error.message,
      variant: "destructive",
    });
  };

  return (
    <div className="container py-6 max-w-3xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-foreground">Upload Document</h1>
        <Button
          variant="outline"
          onClick={() => router.push("/documents")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Documents
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upload a Document</CardTitle>
          <CardDescription>
            Upload a document to analyze, compare, or reference in your
            research.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DocumentUploader
            onUploadComplete={handleUploadComplete}
            onError={handleError}
          />
        </CardContent>
      </Card>
    </div>
  );
}