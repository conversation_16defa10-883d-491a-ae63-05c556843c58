import { Suspense } from 'react';
import { UploadDocumentClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

export default function UploadDocumentPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    }>
      <UploadDocumentClient />
    </Suspense>
  );
}
