"use client"
import type React from "react"
import { Sidebar } from "@/components/sidebar";
import { CollaborationProvider } from "@/lib/collaboration/collaboration-context";
import { GlobalHeader } from "@/components/global-header";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <CollaborationProvider>
      <div className="flex flex-col min-h-screen">
        <GlobalHeader />
        <div className="flex flex-1 relative">
          {/* Sidebar */}
          <Sidebar />

          {/* Main content area (without header) */}
          {/* md:ml-64 is important to push content to the right of the sidebar on medium screens and up */}
          <div className="flex-1 flex flex-col md:ml-72">
            <main className="flex-1 overflow-hidden">
              <div className="w-full h-full overflow-auto">{children}</div>
            </main>
          </div>
        </div>
      </div>
    </CollaborationProvider>
  );
}
