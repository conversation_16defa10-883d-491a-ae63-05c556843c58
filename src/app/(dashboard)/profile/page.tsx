"use client";

import { Suspense, useEffect, useState } from "react";
import { useProfile, useUpdateProfile } from "@/lib/auth/auth-service";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle2, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "@/components/ui/use-toast";
import Link from "next/link";
import { Spinner } from "@/components/ui/spinner";

interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  picture?: string;
}

function ProfilePageContent() {
  const { data: profile, isLoading, error, refetch } = useProfile();
  const updateProfile = useUpdateProfile();
  
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
  });
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [showLoadingState, setShowLoadingState] = useState(false);
  const [showErrorState, setShowErrorState] = useState(false);

  useEffect(() => {
    // Set loading state on client-side only to avoid hydration mismatch
    setShowLoadingState(isLoading);
  }, [isLoading]);

  useEffect(() => {
    // Set error state on client-side only to avoid hydration mismatch
    setShowErrorState(!!error || !profile);
  }, [error, profile]);

  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
      });
    }
  }, [profile]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear any previous errors when the user starts typing
    setUpdateError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Only include fields that have changed
      const updateData: UpdateProfileRequest = {};
      
      if (formData.firstName !== profile?.firstName) {
        updateData.firstName = formData.firstName;
      }
      
      if (formData.lastName !== profile?.lastName) {
        updateData.lastName = formData.lastName;
      }
      
      // Only make the API call if there are changes
      if (Object.keys(updateData).length > 0) {
        await updateProfile.mutateAsync(updateData);
        toast({
          title: "Profile Updated",
          description: "Your profile information has been successfully updated.",
          variant: "default",
        });
        
        // Refresh profile data
        refetch();
      }
      
      setIsEditing(false);
    } catch (err) {
      const errorMessage = err instanceof Error 
        ? err.message 
        : "Failed to update profile. Please try again.";
      
      setUpdateError(errorMessage);
      
      toast({
        title: "Update Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const getInitials = (name?: string) => {
    if (!name) return "U";
    return name.charAt(0).toUpperCase();
  };

  // Render loading state
  if (showLoadingState) {
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">Profile</h1>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render error state
  if (showErrorState) {
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">Profile</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load profile information. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Profile</h1>

      {updateError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{updateError}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>Manage your personal information and account settings</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    {isEditing ? (
                      <Input
                        id="firstName"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                      />
                    ) : (
                      <div className="p-2 border rounded-md bg-muted/50">
                        {profile?.firstName || "Not provided"}
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    {isEditing ? (
                      <Input
                        id="lastName"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                      />
                    ) : (
                      <div className="p-2 border rounded-md bg-muted/50">
                        {profile?.lastName || "Not provided"}
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <div className="p-2 border rounded-md bg-muted/50 flex items-center justify-between">
                    <span>{profile?.email}</span>
                    {profile?.emailVerified ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                        <CheckCircle2 className="h-3 w-3" />
                        Verified
                      </Badge>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                          Not Verified
                        </Badge>
                        <Button variant="link" size="sm" asChild>
                          <Link href="/resend-verification">Verify Now</Link>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Role</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {profile?.role || profile?.roles?.join(", ") || "User"}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Organization</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {profile?.organizationName || "Personal Workspace"}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Account Created</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {profile?.createdAt 
                      ? new Date(profile.createdAt).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })
                      : "Unknown"}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Last Login</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {profile?.lastLoginAt
                      ? new Date(profile.lastLoginAt).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        })
                      : "Unknown"}
                  </div>
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-between">
              {isEditing ? (
                <>
                  <Button variant="outline" onClick={() => setIsEditing(false)} disabled={updateProfile.isPending}>
                    Cancel
                  </Button>
                  <Button type="submit" onClick={handleSubmit} disabled={updateProfile.isPending}>
                    {updateProfile.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Changes"
                    )}
                  </Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)}>
                  Edit Profile
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Account Summary</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center space-y-4">
              <Avatar className="h-24 w-24">
                {profile?.picture ? (
                  <AvatarImage src={profile.picture} alt={profile.firstName || profile.email} />
                ) : null}
                <AvatarFallback className="text-xl">
                  {getInitials(profile?.firstName)}
                  {getInitials(profile?.lastName)}
                </AvatarFallback>
              </Avatar>
              
              <div className="text-center">
                <h3 className="font-medium text-lg">
                  {profile?.firstName && profile?.lastName
                    ? `${profile.firstName} ${profile.lastName}`
                    : profile?.email}
                </h3>
                <p className="text-muted-foreground text-sm">{profile?.email}</p>
              </div>

              <div className="w-full pt-4">
                <div className="flex justify-between py-2 border-b">
                  <span className="text-muted-foreground">Subscription</span>
                  <Badge variant={profile?.subscriptionStatus === "active" ? "default" : "outline"}>
                    {profile?.subscriptionTier || "Free"}
                  </Badge>
                </div>
                
                <div className="flex justify-between py-2 border-b">
                  <span className="text-muted-foreground">Status</span>
                  <Badge 
                    variant={profile?.subscriptionStatus === "active" ? "default" : "destructive"}
                  >
                    {profile?.subscriptionStatus || "Inactive"}
                  </Badge>
                </div>
                
                <div className="flex justify-between py-2 border-b">
                  <span className="text-muted-foreground">2FA Enabled</span>
                  <span>{profile?.twoFactorEnabled ? "Yes" : "No"}</span>
                </div>
                
                <div className="flex justify-between py-2">
                  <span className="text-muted-foreground">Email Verified</span>
                  <span>{profile?.emailVerified ? "Yes" : "No"}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard/subscription">
                  Manage Subscription
                </Link>
              </Button>
              
              {profile && !profile.twoFactorEnabled && (
                <Button variant="outline" className="w-full">
                  Enable 2FA
                </Button>
              )}
              
              {profile && !profile.emailVerified && (
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/resend-verification">
                    Verify Email
                  </Link>
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    }>
      <ProfilePageContent />
    </Suspense>
  );
}
