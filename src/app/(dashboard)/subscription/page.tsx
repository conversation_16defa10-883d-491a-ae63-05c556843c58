"use client";

import { useState, useEffect } from "react";
import { withSuspense } from "@/lib/hoc/withSuspense";
import { useSearchParams } from "next/navigation";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { PlanSelector } from "@/components/subscription/plan-selector";
import { SubscriptionDetails } from "@/components/subscription/subscription-details";
import { UsageStats } from "@/components/subscription/usage-stats";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from "@/components/ui/spinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { CreditDashboard } from "@/components/subscription/credit-dashboard";

function SubscriptionPage() {
	const { subscription, isLoading, error } = useSubscription();
	const searchParams = useSearchParams();
	const [activeTab, setActiveTab] = useState("overview");

	// Handle URL tab parameter
	useEffect(() => {
		const tabParam = searchParams.get('tab');
		if (tabParam && ['overview', 'plans', 'credits', 'usage'].includes(tabParam)) {
			setActiveTab(tabParam);
		}
	}, [searchParams]);

	if (isLoading) {
		return (
			<div className="flex justify-center items-center h-64">
				<Spinner size="lg" />
			</div>
		);
	}

	if (error) {
		return (
			<Alert variant="destructive" className="max-w-3xl mx-auto my-8">
				<AlertCircle className="h-4 w-4" />
				<AlertTitle>Error</AlertTitle>
				<AlertDescription>
					Failed to load subscription information. Please try again later.
				</AlertDescription>
			</Alert>
		);
	}

	return (
		<div className="container py-6 max-w-6xl">
			<h1 className="text-2xl font-bold mb-6 text-foreground">
				Subscription Management
			</h1>

			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="space-y-6"
			>
				<TabsList className="grid grid-cols-4 w-full max-w-2xl">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="plans">Plans</TabsTrigger>
					<TabsTrigger value="credits">Credits & Packages</TabsTrigger>
					<TabsTrigger value="usage">Usage</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-6">
					<SubscriptionDetails
						subscription={subscription}
						onNavigateToCredits={() => setActiveTab("credits")}
					/>
				</TabsContent>

				<TabsContent value="plans" className="space-y-6">
					<PlanSelector currentTier={subscription?.tier} />
				</TabsContent>

				<TabsContent value="credits" className="space-y-6">
					<CreditDashboard defaultTab="purchase" />
				</TabsContent>

				<TabsContent value="usage" className="space-y-6">
					<UsageStats subscription={subscription} />
				</TabsContent>
			</Tabs>
		</div>
	);
}

export default withSuspense(SubscriptionPage);
