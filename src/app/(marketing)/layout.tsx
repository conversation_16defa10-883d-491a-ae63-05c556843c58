import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AuthenticatedButtons } from "@/components/auth/authenticated-buttons";
import { ThemeToggle } from "@/app/theme-toggle";

export const metadata: Metadata = {
  title: "Docgic | Legal Research Without the Research",
  description: "Stop reading 200-page contracts line by line. Chat with any legal document, compare agreements intelligently, and research case law with AI that thinks like a lawyer.",
};

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-2">
        <div className="flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src="/logos/light-512.png"
              alt="Docgic Logo Light"
              width={300}
              height={300}
              className="w-32 object-cover block dark:hidden"
              priority
            />
            <Image
              src="/logos/trans-512.png"
              alt="Docgic Logo Dark"
              width={300}
              height={300}
              className="w-32 object-cover hidden dark:block"
              priority
            />
          </Link>
          <nav className="hidden md:flex items-center gap-6">
            <Link
              href="#features"
              className="text-sm font-medium hover:text-primary"
            >
              Features
            </Link>
            <Link
              href="#how-it-works"
              className="text-sm font-medium hover:text-primary"
            >
              How It Works
            </Link>
            <Link
              href="#pricing"
              className="text-sm font-medium hover:text-primary"
            >
              Pricing
            </Link>
          </nav>
          <div className="flex items-center gap-4">
            <ThemeToggle />
            <AuthenticatedButtons />
          </div>
        </div>
      </header>

      <main className="flex-1">
        {children}
      </main>

      <footer className="border-t py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-2">
              <Image 
                src="/logos/trans-512.png" 
                alt="Docgic Logo" 
                width={300} 
                height={300} 
                className="w-32"
                />
            </div>
            
            <div className="flex flex-col md:flex-row gap-4 md:gap-8 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-foreground transition-colors">Privacy Policy</Link>
              <Link href="/terms" className="hover:text-foreground transition-colors">Terms of Service</Link>
              <Link href="mailto:<EMAIL>" className="hover:text-foreground transition-colors">Contact Us</Link>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
            <p>&copy; {new Date().getFullYear()} Cendrift LLC. All rights reserved.</p>
            <p className="mt-2">Docgic is a trademark of Cendrift LLC.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}