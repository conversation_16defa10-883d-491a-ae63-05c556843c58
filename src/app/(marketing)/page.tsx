import {
  ArrowRight,
  CheckCircle,
  FileText,
  MessageSquare,
  GitCompare,
  Search,
  Users,
  Lock,
  Linkedin,
  Twitter,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import Link from "next/link";
import { HeroButtons } from "@/components/auth/hero-buttons";

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 md:py-28 bg-gradient-to-b from-background to-muted/50">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center text-center space-y-4 max-w-4xl mx-auto">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                  Lawyers deserve a better research tool than Google
                  <span className="block text-2xl sm:text-3xl md:text-4xl text-muted-foreground mt-2">
                    (We built the AI that does it better.)
                  </span>
                </h1>
                <p className="max-w-[700px] text-muted-foreground md:text-xl mx-auto">
                Chat with contracts, compare agreements, and research case law with AI that thinks like a lawyer. Finding one clause shouldn't take three hours.
                </p>
              </div>
              <HeroButtons />
              <div className="flex flex-col gap-2 mt-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <span>50 credits free</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Document review that doesn't ruin your weekend
                </h2>
                <p className="max-w-[700px] mx-auto text-muted-foreground md:text-xl">
                  While your competition burns through associates, you'll be closing deals. 85% faster reviews, 90% fewer errors, 100% more billable hours.
                </p>
              </div>
            </div>

            {/* AI-Powered Legal Analysis */}
            <div className="mt-16">
              <div className="flex flex-col items-center gap-8">
                {/* Content Section */}
                <div className="text-center max-w-4xl space-y-6">
                  <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                    <Search className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">
                      AI Legal Researcher
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold tracking-tight md:text-3xl">
                    Legal research at light speed
                  </h3>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Skip Westlaw rabbit holes. Get comprehensive research with sources and analysis in seconds, not hours.
                  </p>
                  
                  {/* Benefits Grid */}
                  <div className="grid md:grid-cols-3 gap-6 mt-8">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6" />
                      <h4 className="font-medium">Instant Case Law Research</h4>
                      <p className="text-sm text-muted-foreground">
                        Ask any legal question and get comprehensive analysis with relevant cases, statutes, and regulations cited.
                      </p>
                    </div>
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Synthesis & Analysis</h4>
                      <p className="text-sm text-muted-foreground">
                        Don't just get a list of cases. Get actual analysis that connects the dots and builds your argument.
                      </p>
                    </div>
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Current Law Updates</h4>
                      <p className="text-sm text-muted-foreground">
                        Ask questions like "What stands out in this section?" and get citation-backed answers.
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Screenshot Section */}
                <div className="w-full max-w-5xl">
                  <div className="rounded-lg overflow-hidden shadow-2xl border">
                    <Image
                      src="/images/legal-research.png"
                      alt="AI legal research interface showing comprehensive case analysis"
                      width={800}
                      height={400}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Chat Experience */}
            <div className="mt-24">
              <div className="flex flex-col items-center gap-8">
                {/* Content Section */}
                <div className="text-center max-w-4xl space-y-6">
                  <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                    <MessageSquare className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">
                      Legal-Specific AI Chat
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold">
                    Chat with your contracts (they finally talk back)
                  </h3>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Ask questions like a human. Get answers like Westlaw, without the migraines.
                  </p>
                  
                  {/* Benefits Grid */}
                  <div className="grid md:grid-cols-3 gap-6 mt-8">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Understands your contract language</h4>
                      <p className="text-sm text-muted-foreground">
                        Our AI understands legal terminology and concepts like force majeure, indemnification, and material adverse change clauses.
                      </p>
                    </div>
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Citation-Backed Answers</h4>
                      <p className="text-sm text-muted-foreground">
                        Every answer comes with direct references to specific sections, so you can verify and cite with confidence.
                      </p>
                    </div>
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Cross-Document Analysis</h4>
                      <p className="text-sm text-muted-foreground">
                        Find inconsistencies between contracts or track changes across versions of the same agreement.
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Screenshot Section */}
                <div className="w-full max-w-5xl">
                  <div className="rounded-lg overflow-hidden shadow-2xl border">
                    <Image
                      src="/images/chat-with-doc.png"
                      alt="Interactive document chat interface with AI-powered Q&A"
                      width={800}
                      height={400}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Document Management & Comparison */}
            <div className="mt-24">
              <div className="flex flex-col items-center gap-8">
                {/* Content Section */}
                <div className="text-center max-w-4xl space-y-6">
                  <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                    <GitCompare className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">
                      Document Comparison
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold">
                    Redlines you can trust
                  </h3>
                  <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Track changes like a bloodhound. Never miss a sneaky edit again.
                  </p>
                  
                  {/* Benefits Grid */}
                  <div className="grid md:grid-cols-2 gap-8 mt-8 justify-center">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Never Miss a Client's Change</h4>
                      <p className="text-sm text-muted-foreground">
                        Automatic version tracking ensures you catch every modification in contracts, eliminating costly oversights.
                      </p>
                    </div>
                    <div className="flex flex-col items-center text-center space-y-2">
                      <CheckCircle className="h-6 w-6 text-primary" />
                      <h4 className="font-medium">Spot Risky Modifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Our visual highlighting makes problematic changes obvious, even in complex legal documents with hundreds of revisions.
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Screenshot Section */}
                <div className="w-full max-w-5xl">
                  <div className="rounded-lg overflow-hidden shadow-2xl border">
                    <Image
                      src="/images/doc-comparison.png"
                      alt="Document comparison interface showing detailed redlines and changes"
                      width={800}
                      height={400}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Feature Grid */}
            <div className="mt-24 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Document Research Integration */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="flex flex-col space-y-4">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Search className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">
                    Document Research Integration
                  </h3>
                  <p className="text-muted-foreground">
                    Automatically detect and enrich citations with
                    metadata from trusted databases.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Automatic citation detection</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Citation enrichment</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Comprehensive case information</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Advanced Analytics */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="flex flex-col space-y-4">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">
                    Advanced Analytics
                  </h3>
                  <p className="text-muted-foreground">
                    Gain insights into your document analysis patterns and 
                    track your legal workflow efficiency.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Document analysis insights</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Workflow efficiency tracking</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Performance benchmarking</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Security & Compliance */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="flex flex-col space-y-4">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Lock className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">Legal-Grade Security</h3>
                  <p className="text-muted-foreground">
                    Designed for law firms with client confidentiality requirements and strict data governance needs.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Attorney-client privilege compliant</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Per-client document isolation</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Audit trails for all document access</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Pricing that won't bankrupt your practice
                </h2>
                <p className="max-w-[700px] text-muted-foreground md:text-xl">
                  No complicated billing. No surprise charges. Just pay for what you use and get back to lawyering.
                </p>
              </div>
            </div>
            <div className="mt-16 grid gap-8 md:grid-cols-3">
              {/* Law Student Plan */}
              <div className="rounded-lg border bg-card p-8 shadow-sm">
                <div className="flex flex-col space-y-6">
                  <h3 className="text-2xl font-bold">🎓 Law Student</h3>
                  <div className="space-y-2">
                    <p className="text-4xl font-bold">FREE</p>
                    <p className="text-muted-foreground">Perfect for legal education</p>
                  </div>
                  <div className="space-y-2 py-2 border-y">
                    <div className="flex justify-between">
                      <span>Document Limit</span>
                      <span className="font-medium">10</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credits per Month</span>
                      <span className="font-medium">50</span>
                    </div>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Document analysis</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Chat with documents</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Legal research</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Document comparison</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Case law search</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Document organization</span>
                    </li>
                  </ul>
                  <Button className="w-full" asChild>
                    <Link href="/register">Get Started Free</Link>
                  </Button>
                </div>
              </div>

              {/* Lawyer Plan */}
              <div className="rounded-lg border bg-primary p-8 shadow-sm text-primary-foreground">
                <div className="flex flex-col space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-2xl font-bold">⚖️ Lawyer</h3>
                    <Badge>Most Popular</Badge>
                  </div>
                  <div className="space-y-2">
                    <p className="text-4xl font-bold">$29.99</p>
                    <p className="text-muted-foreground">For practicing attorneys</p>
                  </div>
                  <div className="space-y-2 py-2 border-y border-primary-foreground/20">
                    <div className="flex justify-between">
                      <span>Document Limit</span>
                      <span className="font-medium">200</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credits per Month</span>
                      <span className="font-medium">500</span>
                    </div>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Advanced document analysis</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Enhanced document comparison</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Precedent analysis</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Deposition analysis</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Priority support</span>
                    </li>
                  </ul>
                  <Button variant="secondary" className="w-full" asChild>
                    <Link href="/register">Start Free Trial</Link>
                  </Button>
                </div>
              </div>

              {/* Law Firm Plan */}
              <div className="rounded-lg border bg-card p-8 shadow-sm">
                <div className="flex flex-col space-y-6">
                  <h3 className="text-2xl font-bold">🏢 Law Firm</h3>
                  <div className="space-y-2">
                    <p className="text-4xl font-bold">$99.99</p>
                    <p className="text-muted-foreground">For law firms & organizations</p>
                  </div>
                  <div className="space-y-2 py-2 border-y">
                    <div className="flex justify-between">
                      <span>Document Limit</span>
                      <span className="font-medium">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credits per Month</span>
                      <span className="font-medium">2000</span>
                    </div>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>All Lawyer features</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Custom integrations</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Extended data retention</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Dedicated support</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Enterprise-grade security</span>
                    </li>
                  </ul>
                  <Button className="w-full" asChild>
                    <Link href="/register">Start Free Trial</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section id="how-it-works" className="py-16 md:py-24 bg-muted/50">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  How Docgic Works
                </h2>
                <p className="max-w-[700px] text-muted-foreground md:text-xl">
                  Get started with Docgic in just a few simple steps.
                </p>
              </div>
            </div>
            <div className="mt-16 grid gap-8 md:grid-cols-3">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-secondary text-2xl font-bold">
                  1
                </div>
                <h3 className="text-xl font-bold">Upload Documents</h3>
                <p className="text-muted-foreground">
                  Upload your documents to our secure platform. We support
                  various formats including PDF, DOCX, and more.
                </p>
              </div>
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-secondary text-2xl font-bold">
                  2
                </div>
                <h3 className="text-xl font-bold">AI Analysis</h3>
                <p className="text-muted-foreground">
                  Our AI automatically analyzes your documents, extracting key
                  information, identifying clauses, and preparing them for
                  interaction.
                </p>
              </div>
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-secondary text-2xl font-bold">
                  3
                </div>
                <h3 className="text-xl font-bold">Analyze & Export</h3>
                <p className="text-muted-foreground">
                  Chat with your documents, compare versions, generate detailed
                  reports, and export your findings in various formats.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 md:py-24 bg-primary text-primary-foreground">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Ready to Transform Your Document Workflow?
                </h2>
                <p className="max-w-[700px] md:text-xl">
                  Join the professionals who are saving time and gaining
                  insights with Docgic.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <Button variant="secondary" size="lg" asChild>
                  <Link href="/register">Start Free Trial</Link>
                </Button>
                {/* <Button
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
                  asChild
                >
                  <Link href="/contact">Schedule Demo</Link>
                </Button> */}
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-12 md:py-16 bg-muted/30">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-6 text-center">
              <div className="space-y-2">
                <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl">
                  Connect with the Creator
                </h2>
                <p className="text-muted-foreground">
                  Have questions or feedback? Let's connect on social media.
                </p>
              </div>
              
              <div className="flex items-center gap-6">
                <a
                  href="https://www.linkedin.com/in/osazee-agbonze-91a6a3190/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 px-6 py-3 rounded-lg border bg-background hover:bg-muted/50 transition-colors group"
                >
                  <Linkedin className="h-5 w-5 text-blue-600 group-hover:text-blue-700" />
                  <div className="text-left">
                    <div className="font-medium text-sm">LinkedIn</div>
                    <div className="text-xs text-muted-foreground">Connect professionally</div>
                  </div>
                </a>
                
                <a
                  href="https://x.com/saxzzy03"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 px-6 py-3 rounded-lg border bg-background hover:bg-muted/50 transition-colors group"
                >
                  <Twitter className="h-5 w-5 text-gray-800 group-hover:text-black dark:text-gray-200 dark:group-hover:text-white" />
                  <div className="text-left">
                    <div className="font-medium text-sm">X (Twitter)</div>
                    <div className="text-xs text-muted-foreground">Follow for updates</div>
                  </div>
                </a>
              </div>
              
              <div className="flex flex-col items-center gap-2">
                <p className="text-sm text-muted-foreground">
                  Built with ❤️ by Osazee Agbonze
                </p>
                <div className="text-xs text-muted-foreground">
                  <a 
                    href="/privacy-policy" 
                    className="hover:text-primary underline underline-offset-4"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Policy
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
