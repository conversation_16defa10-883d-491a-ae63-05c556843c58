import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check } from "lucide-react";

interface PricingTier {
  name: string;
  price: string;
  description: string;
  features: string[];
  cta: {
    text: string;
    href: string;
  };
  highlighted?: boolean;
  currentPlan?: boolean;
}

const pricingTiers: PricingTier[] = [
  {
    name: "Law Student",
    price: "$0",
    description: "Essential document analysis tools for law students and legal education. 50 monthly credits for AI-powered features.",
    features: [
      "10 documents",
      "Unlimited AI analysis",
      "50 credits/month for AI features",
      "Unlimited CRUD operations (FREE)"
    ],
    cta: {
      text: "Current Plan",
      href: "/register?plan=law_student"
    },
    currentPlan: true
  },
  {
    name: "Lawyer",
    price: "$29.99",
    description: "Professional plan for individual lawyers with advanced features. Includes unlimited document analysis, advanced AI insights, priority support, 500 monthly credits, and legal research tools.",
    features: [
      "200 documents",
      "Unlimited AI analysis",
      "500 credits/month for AI features",
      "Unlimited CRUD operations (FREE)"
    ],
    cta: {
      text: "Upgrade",
      href: "/register?plan=lawyer"
    },
    highlighted: true
  },
  {
    name: "Law Firm",
    price: "$99.99",
    description: "Enterprise plan for law firms with advanced features. Everything in Pro plus unlimited users, 2000 monthly credits, custom integrations, and dedicated support.",
    features: [
      "unlimited documents",
      "Unlimited AI analysis",
      "2000 credits/month for AI features",
      "Unlimited CRUD operations (FREE)"
    ],
    cta: {
      text: "Upgrade",
      href: "/register?plan=law_firm"
    }
  }
];

export default function PricingPage() {
  return (
    <div className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4">Pricing that won't bankrupt your practice</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            From broke law students to BigLaw partners. No calculators needed. No surprise overage fees. No 47-page contracts.
          </p>
        </div>

        {/* Pricing Tiers */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingTiers.map((tier) => (
            <Card
              key={tier.name}
              className={`relative ${
                tier.highlighted 
                  ? "border-2 border-primary shadow-lg scale-105" 
                  : "border border-border"
              } bg-card`}
            >
              {tier.currentPlan && (
                <Badge 
                  variant="secondary" 
                  className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-muted text-muted-foreground"
                >
                  Current Plan
                </Badge>
              )}
              
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold">{tier.name}</CardTitle>
                <CardDescription className="text-sm text-muted-foreground leading-relaxed">
                  {tier.description}
                </CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{tier.price}</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <ul className="space-y-3">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              
              <CardFooter className="pt-4">
                <Button
                  asChild
                  className="w-full"
                  variant={tier.currentPlan ? "secondary" : tier.highlighted ? "default" : "outline"}
                  disabled={tier.currentPlan}
                >
                  <Link href={tier.cta.href}>
                    {tier.cta.text}
                    {!tier.currentPlan && (
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    )}
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center mb-12">
            What's included in each plan
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4 font-medium">Features</th>
                  <th className="text-center p-4 font-medium">Law Student</th>
                  <th className="text-center p-4 font-medium">Lawyer</th>
                  <th className="text-center p-4 font-medium">Law Firm</th>
                </tr>
              </thead>
              <tbody>
                                 {[
                   { feature: "Document Analysis", student: "10/month", lawyer: "200/month", firm: "Unlimited" },
                   { feature: "AI Credits", student: "50/month", lawyer: "500/month", firm: "2000/month" },
                   { feature: "Legal Research", student: "✓", lawyer: "✓", firm: "✓" },
                   { feature: "Document Comparison", student: "✓", lawyer: "✓", firm: "✓" },
                   { feature: "Case Law Search", student: "✓", lawyer: "✓", firm: "✓" },
                   { feature: "Chat with Documents", student: "✓", lawyer: "✓", firm: "✓" },
                   { feature: "Advanced Analysis", student: "—", lawyer: "✓", firm: "✓" },
                   { feature: "Enhanced Comparison", student: "—", lawyer: "✓", firm: "✓" },
                   { feature: "Precedent Analysis", student: "—", lawyer: "✓", firm: "✓" },
                   { feature: "Deposition Analysis", student: "—", lawyer: "✓", firm: "✓" },
                   { feature: "Export & Reports", student: "—", lawyer: "✓", firm: "✓" },
                   { feature: "Priority Support", student: "—", lawyer: "✓", firm: "✓" },
                   { feature: "Custom Integrations", student: "—", lawyer: "—", firm: "✓" },
                   { feature: "Extended Data Retention", student: "—", lawyer: "—", firm: "✓" },
                   { feature: "Dedicated Support", student: "—", lawyer: "—", firm: "✓" },
                 ].map((row, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-4 font-medium">{row.feature}</td>
                    <td className="p-4 text-center text-muted-foreground">{row.student}</td>
                    <td className="p-4 text-center">{row.lawyer}</td>
                    <td className="p-4 text-center">{row.firm}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <Card className="max-w-2xl mx-auto bg-primary/5 border-primary/20">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4">Ready to upgrade your legal practice?</h3>
              <p className="text-muted-foreground mb-6">
                Join thousands of lawyers who've already made the switch to AI-powered legal analysis.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/register?plan=lawyer">
                    Start Free Trial
                  </Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/contact">
                    Talk to Sales
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

const faqs = [
  {
    question: "What happens if I run out of credits?",
    answer: "You can purchase additional credit packages tailored to your tier, or upgrade to a higher plan for more monthly credits and better rates."
  },
  {
    question: "Can law students upgrade to Lawyer tier after graduation?",
    answer: "Absolutely! We encourage law students to upgrade to the Lawyer tier when they begin practicing. All your documents and analysis history will be preserved."
  },
  {
    question: "Do you offer discounts for law schools or bar associations?",
    answer: "Yes, we offer special institutional pricing for law schools and bar associations. Contact our sales team for custom pricing."
  },
  {
    question: "Is my client data secure and confidential?",
    answer: "Yes, we maintain the highest security standards with attorney-client privilege protection, end-to-end encryption, and SOC 2 compliance."
  },
  {
    question: "What makes the Law Firm tier different from the Lawyer tier?",
    answer: "The Law Firm tier includes unlimited documents, custom integrations, extended data retention, and dedicated support with an account manager."
  },
  {
    question: "Do you offer integrations with legal practice management systems?",
    answer: "Yes, the Law Firm tier includes custom integrations with popular legal practice management systems to fit your firm's workflow."
  }
];