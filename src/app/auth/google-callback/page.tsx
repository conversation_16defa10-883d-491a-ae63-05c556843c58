import { Suspense } from 'react';
import { GoogleCallbackClient } from './client';
import { Spinner } from '@/components/ui/spinner';

export const dynamic = 'force-dynamic';

export default function GoogleCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    }>
      <GoogleCallbackClient />
    </Suspense>
  );
} 