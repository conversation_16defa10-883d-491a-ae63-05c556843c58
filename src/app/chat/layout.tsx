"use client";
import { Chat<PERSON><PERSON>ider } from "@/lib/chat/chat-context";
import type React from "react";
import { GlobalHeader } from "@/components/global-header";

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col min-h-screen relative overflow-y-hidden">
      <div className="hidden lg:block">
        <GlobalHeader />
      </div>
      {/* Page content without sidebar */}
      <main className="flex-1 flex">
        <div className="w-full h-full">
          <ChatProvider>{children}</ChatProvider>
        </div>
      </main>
    </div>
  );
}
