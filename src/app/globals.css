@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode with light gray shades instead of white */
    --background: 0 0% 96%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 98%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 98%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 93%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 93%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 93%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode with dark gray shades instead of black */
    --background: 0 0% 13%;
    --foreground: 0 0% 98%;

    --card: 0 0% 16%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 16%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 12%;

    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 20%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 24%;
    --input: 0 0% 24%;
    --ring: 0 0% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
		font-size: 18px; /* Increase base font size */
		line-height: 1.8; /* Increase line spacing */
	}
  body {
    @apply bg-background text-foreground;
  }
}

/* Force dark mode for the chat interface */
.chat-interface {
  color-scheme: dark;
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar,
.chat-interface ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track,
.chat-interface ::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

.dark ::-webkit-scrollbar-thumb,
.chat-interface ::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 5px;
}

.dark ::-webkit-scrollbar-thumb:hover,
.chat-interface ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Message styling */
.user-message {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.assistant-message {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
}

.dark .user-message {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.dark .assistant-message {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
}

/* Input field styling */
.message-input {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

.dark .message-input {
  background-color: hsl(0, 0%, 18%);
  border-color: hsl(var(--border));
}

/* Line clamp utilities for citation previews */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
