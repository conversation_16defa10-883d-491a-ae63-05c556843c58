import { Suspense } from "react";
import { Inter } from "next/font/google";
import "./globals.css";
import type { Metadata } from "next";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "./theme-provider";
import { PostHogWrapper } from "../providers/PostHogProvider"; // Added PostHogWrapper
import { UIProvider } from "@/lib/ui/ui-context"; // Added UIProvider
import { ResearchProvider } from "@/lib/legal-research/research-context";

import { AnalyticsErrorBoundary } from "@/components/ErrorBoundary"; // Added AnalyticsErrorBoundary
import { RouteTracker } from "@/components/RouteTracker"; // Added RouteTracker

export const metadata: Metadata = {
	title: "Docgic | Legal Research Without the Research",
	description: "Stop reading 200-page contracts line by line. Chat with any legal document, compare agreements intelligently, and research case law with AI that thinks like a lawyer.",
	icons: {
		icon: [
			{ url: "/logos/favicon-16x16.png", sizes: "16x16", type: "image/png" },
			{ url: "/logos/favicon-32x32.png", sizes: "32x32", type: "image/png" },
			{ url: "/logos/favicon.ico", sizes: "any" }
		],
		apple: [
			{ url: "/logos/apple-touch-icon.png" }
		],
		other: [
			{ rel: "manifest", url: "/logos/site.webmanifest" }
		]
	}
};
const inter = Inter({ subsets: ["latin"] })
export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="en" suppressHydrationWarning>
			<body className={inter.className}>
				<PostHogWrapper>
					<ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
					<UIProvider>
						<Providers>
							<AnalyticsErrorBoundary fallback={<p>Something went wrong. Please try refreshing the page.</p>}>
								<Suspense fallback={null}>
									<RouteTracker />
								</Suspense>
								<ResearchProvider>
									{children}
								</ResearchProvider>
								<Toaster />
							</AnalyticsErrorBoundary>
						</Providers>
					</UIProvider>
					</ThemeProvider>
				</PostHogWrapper>
			</body>
		</html>
	);
}
