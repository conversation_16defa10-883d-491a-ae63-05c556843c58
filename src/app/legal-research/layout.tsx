"use client";

import type React from "react";
import { GlobalHeader } from "@/components/global-header";
import { useEffect, useState } from 'react';

// Dynamically import the header to ensure it's only rendered on the client
const DynamicLegalResearchHeader = dynamic(
  () => import('@/components/legal-research/legal-research-header').then(mod => mod.LegalResearchHeader),
  { 
    ssr: false,
    loading: () => (
      <header className="sticky top-0 z-30 flex h-14 shrink-0 items-center justify-between border-b bg-background px-4 sm:px-6 overflow-hidden">
        <div className="flex items-center gap-2 sm:gap-4">
          <div className="w-32 h-8 md:w-40 md:h-10 bg-muted rounded animate-pulse" />
        </div>
      </header>
    )
  }
);

import dynamic from 'next/dynamic';

export default function LegalResearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex h-screen flex-col">
        <div className="h-14 bg-background border-b" />
        <main className="flex-1 overflow-hidden">{children}</main>
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col">
      <div className="hidden lg:block">
        <GlobalHeader />
      </div>
      {/* <DynamicLegalResearchHeader /> */}
      <main className="flex-1 overflow-hidden">{children}</main>
    </div>
  );
}
