"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useResearchContext } from "@/lib/legal-research/research-context";
import { Loader2 } from "lucide-react";

export default function NewResearchPage() {
  const router = useRouter();
  const { clearSession } = useResearchContext();

  useEffect(() => {
    // This effect runs once on mount
    clearSession();
    // Replace the current history state and navigate
    router.replace("/legal-research");
  }, [clearSession, router]);

  // Display a loading indicator while the redirect is happening
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex items-center space-x-2 text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Starting new research session...</span>
      </div>
    </div>
  );
} 