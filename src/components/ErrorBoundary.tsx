// src/components/ErrorBoundary.tsx
'use client';

import React, { ErrorInfo, ReactNode } from 'react';
import { posthog } from '../providers/PostHogProvider'; // Import configured instance

interface AnalyticsErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode; // Optional fallback UI to display when an error occurs
}

interface AnalyticsErrorBoundaryState {
  hasError: boolean;
  error?: Error; // Store the error object if needed
}

export class AnalyticsErrorBoundary extends React.Component<AnalyticsErrorBoundaryProps, AnalyticsErrorBoundaryState> {
  constructor(props: AnalyticsErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): AnalyticsErrorBoundaryState {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Check if posthog is initialized and its capture method is available
    if (posthog && typeof posthog.capture === 'function') {
      posthog.capture('frontend_error', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      });
    } else {
      // Log a warning if PostHog is not available for error reporting
      console.warn('PostHog not initialized or capture function not available. Frontend error not reported to PostHog:', error, errorInfo);
    }
    // It's also good practice to log the error to the console
    console.error("Uncaught error captured by AnalyticsErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // If a fallback UI is provided, render it. Otherwise, render a default message.
      if (this.props.fallback) {
        return this.props.fallback;
      }
      return <h1>Something went wrong. Please try again.</h1>;
    }

    return this.props.children;
  }
}
