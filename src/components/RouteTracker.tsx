// src/components/RouteTracker.tsx
'use client'; // Required for Next.js App Router hooks

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation'; // Next.js App Router hooks
import { posthog } from '../providers/PostHogProvider';

export const RouteTracker = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Ensure posthog is loaded and capture is available
    if (posthog && typeof posthog.capture === 'function') {
      // Construct the full URL from pathname and searchParams
      // Note: window.location.href might not be immediately available or accurate on server components
      // or during initial hydration. For client-side tracking after hydration, it's generally fine.
      // However, relying on pathname and searchParams is more robust within Next.js's rendering lifecycle.
      let url = window.location.origin + pathname;
      if (searchParams && searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }

      posthog.capture('$pageview', {
        // PostHog's $current_url is automatically set by the library if not provided
        // but explicitly sending it can be useful for consistency or if you have specific needs.
        '$current_url': url,
        // You can add custom properties to your pageview events if needed
        // 'path': pathname, // Example custom property
        // 'search': searchParams ? searchParams.toString() : '', // Example custom property
      });
    }
  }, [pathname, searchParams]); // Rerun effect if path, search params, or posthog instance changes

  return null; // This component does not render anything
};
