"use client";

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, Brain, Users, Clock, AlertCircle, CheckCircle, Upload } from 'lucide-react';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import { documentService } from '@/lib/services/document-service';
import type { ScenarioResponse, CreateSessionFromDocumentRequest, AiPersonality } from '@/lib/types/chat-negotiation';
import type { DocumentAnalysis } from '@/lib/types/analysis';
import type { DocumentMetadata } from '@/lib/services/document-service';

interface DocumentWithAnalysis {
  document: DocumentMetadata;
  analysis: DocumentAnalysis | null;
  hasAnalysis: boolean;
}

interface DocumentScenarioCreatorProps {
  onScenarioCreated?: (scenario: ScenarioResponse) => void;
  onSessionCreated?: (sessionId: string) => void;
}

const DEFAULT_AI_PERSONALITIES = [
  {
    id: 'balanced_negotiator',
    name: 'Balanced Negotiator',
    description: 'Well-rounded approach with moderate flexibility',
    config: {
      characterId: 'balanced_negotiator',
      aggressiveness: 0.5,
      flexibility: 0.6,
      riskTolerance: 0.5,
      communicationStyle: 'DIPLOMATIC' as const,
    }
  },
  {
    id: 'aggressive_competitor',
    name: 'Aggressive Competitor',
    description: 'High pressure, results-focused approach',
    config: {
      characterId: 'aggressive_competitor',
      aggressiveness: 0.8,
      flexibility: 0.3,
      riskTolerance: 0.7,
      communicationStyle: 'DIRECT' as const,
    }
  },
  {
    id: 'analytical_expert',
    name: 'Analytical Expert',
    description: 'Data-driven, methodical negotiation style',
    config: {
      characterId: 'analytical_expert',
      aggressiveness: 0.4,
      flexibility: 0.7,
      riskTolerance: 0.3,
      communicationStyle: 'ANALYTICAL' as const,
    }
  },
  {
    id: 'collaborative_partner',
    name: 'Collaborative Partner',
    description: 'Relationship-focused, win-win approach',
    config: {
      characterId: 'collaborative_partner',
      aggressiveness: 0.3,
      flexibility: 0.8,
      riskTolerance: 0.6,
      communicationStyle: 'DIPLOMATIC' as const,
    }
  }
];

export default function DocumentScenarioCreator({ 
  onScenarioCreated, 
  onSessionCreated 
}: DocumentScenarioCreatorProps) {
  const [documents, setDocuments] = useState<DocumentWithAnalysis[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentWithAnalysis | null>(null);
  const [selectedPersonality, setSelectedPersonality] = useState<string>('');
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(true);
  const [isCreatingScenario, setIsCreatingScenario] = useState(false);
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadDocumentsWithAnalyses = useCallback(async () => {
    try {
      setIsLoadingDocuments(true);
      setError(null);
      
      // Get all user documents
      const documentsResponse = await documentService.getDocuments(1, 50);
      
      if (!documentsResponse || !documentsResponse.items) {
        setDocuments([]);
        return;
      }
      
      // For each document, try to get its latest analysis
      const documentsWithAnalyses: DocumentWithAnalysis[] = await Promise.all(
        documentsResponse.items.map(async (doc) => {
          try {
            if (!doc || !doc.id) {
              return {
                document: doc,
                analysis: null,
                hasAnalysis: false
              };
            }
            
            const analysis = await documentService.getAnalysisResults(doc.id, { latest: true });
            return {
              document: doc,
              analysis,
              hasAnalysis: !!analysis
            };
          } catch (error) {
            // Document has no analysis yet
            return {
              document: doc,
              analysis: null,
              hasAnalysis: false
            };
          }
        })
      );
      
      // Filter to only show documents that have analyses (since we need them for negotiation scenarios)
      const analyzedDocuments = documentsWithAnalyses.filter(d => d.hasAnalysis && d.analysis);
      setDocuments(analyzedDocuments);
      
    } catch (error: any) {
      console.error('Error loading documents:', error);
      setError(error.message || 'Failed to load documents');
      setDocuments([]); // Ensure we set an empty array on error
    } finally {
      setIsLoadingDocuments(false);
    }
  }, []);

  // Load documents and their analyses on component mount
  useEffect(() => {
    loadDocumentsWithAnalyses();
  }, [loadDocumentsWithAnalyses]);

  const handleCreateScenario = useCallback(async () => {
    if (!selectedDocument?.analysis) return;

    try {
      setIsCreatingScenario(true);
      setError(null);
      
      const scenario = await chatNegotiationService.createScenarioFromAnalysis({
        analysisId: selectedDocument.analysis.analysisId
      });
      
      onScenarioCreated?.(scenario);
    } catch (err: any) {
      setError(err.message || 'Failed to create scenario from document analysis');
    } finally {
      setIsCreatingScenario(false);
    }
  }, [selectedDocument, onScenarioCreated]);

  const handleCreateSession = useCallback(async () => {
    if (!selectedDocument?.analysis || !selectedPersonality) return;

    try {
      setIsCreatingSession(true);
      setError(null);
      
      const personality = DEFAULT_AI_PERSONALITIES.find(p => p.id === selectedPersonality);
      if (!personality) throw new Error('Invalid personality selected');

      const request: CreateSessionFromDocumentRequest = {
        analysisId: selectedDocument.analysis.analysisId,
        aiPersonality: personality.config,
      };

      const session = await chatNegotiationService.createSessionFromDocument(request);
      onSessionCreated?.(session._id || session.negotiationSessionId);
    } catch (err: any) {
      setError(err.message || 'Failed to create session from document analysis');
    } finally {
      setIsCreatingSession(false);
    }
  }, [selectedDocument, selectedPersonality, onSessionCreated]);

  const getDifficultyFromAnalysis = (analysis: DocumentAnalysis) => {
    try {
      // Safety check for analysis structure
      if (!analysis || !analysis.result) {
        return 'beginner';
      }

      // For contract analysis, count high-risk clauses
      if (analysis.result.documentType === 'CONTRACT' || analysis.result.documentType === 'AGREEMENT') {
        const contractResult = analysis.result as any;
        const highRiskClauses = contractResult?.clauses?.filter((clause: any) => clause?.riskLevel === 'high')?.length || 0;
        if (highRiskClauses <= 1) return 'beginner';
        if (highRiskClauses <= 3) return 'intermediate';
        return 'expert';
      }
      // For other document types, use a simple heuristic
      return 'intermediate';
    } catch (error) {
      console.warn('Error calculating difficulty:', error);
      return 'beginner';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    if (difficulty === 'beginner') return 'bg-green-100 text-green-800';
    if (difficulty === 'intermediate') return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2 text-foreground">Create from Document Analysis</h2>
        <p className="text-muted-foreground">
          Generate negotiation scenarios and practice sessions based on your contract analysis results.
        </p>
      </div>

      {/* Document Analysis Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Select Document Analysis
          </CardTitle>
          <CardDescription>
            Choose a contract analysis to base your negotiation scenario on
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingDocuments ? (
            <div className="grid gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="animate-pulse p-4 border border-border rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-48"></div>
                      <div className="h-3 bg-muted rounded w-32"></div>
                    </div>
                    <div className="h-6 bg-muted rounded w-20"></div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded w-20"></div>
                      <div className="h-3 bg-muted rounded w-full"></div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded w-20"></div>
                      <div className="h-3 bg-muted rounded w-full"></div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded w-20"></div>
                      <div className="h-3 bg-muted rounded w-full"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No analyzed documents available</p>
              <p className="text-sm">Upload and analyze a contract first to create scenarios from it</p>
              <Button
                onClick={() => window.location.href = '/documents/upload'}
                className="mt-4"
                variant="outline"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </div>
          ) : (
            <div className="grid gap-4">
              {documents.map((docWithAnalysis) => {
                const { document, analysis } = docWithAnalysis;
                if (!analysis || !document) return null;
                
                // Add safety checks for analysis structure
                if (!analysis.result) {
                  console.warn('Analysis missing result structure:', analysis);
                  return null;
                }
                
                return (
                  <div
                    key={document.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedDocument?.document.id === document.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                        : 'border-border hover:border-accent-foreground/20'
                    }`}
                    onClick={() => setSelectedDocument(docWithAnalysis)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium text-foreground">{document.filename || 'Unknown Document'}</h4>
                        <p className="text-sm text-muted-foreground">{analysis.result?.documentType || 'Contract'}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getDifficultyColor(getDifficultyFromAnalysis(analysis))}>
                          {getDifficultyFromAnalysis(analysis)}
                        </Badge>
                        {selectedDocument?.document.id === document.id && (
                          <CheckCircle className="h-5 w-5 text-blue-500" />
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="font-medium text-foreground mb-1">Parties</div>
                        <div className="space-y-1">
                          {(() => {
                            if (analysis.result.documentType === 'CONTRACT' || analysis.result.documentType === 'AGREEMENT') {
                              const contractResult = analysis.result as any;
                              return contractResult.parties?.slice(0, 2).map((party: any, index: number) => (
                                <div key={index} className="text-muted-foreground">• {party.name}</div>
                              )) || <div className="text-muted-foreground">No parties identified</div>;
                            }
                            return <div className="text-muted-foreground">N/A for this document type</div>;
                          })()}
                        </div>
                      </div>

                      <div>
                        <div className="font-medium text-foreground mb-1">High Risk Clauses</div>
                        <div className="space-y-1">
                          {(() => {
                            if (analysis.result.documentType === 'CONTRACT' || analysis.result.documentType === 'AGREEMENT') {
                              const contractResult = analysis.result as any;
                              const highRiskClauses = contractResult.clauses?.filter((clause: any) => clause.riskLevel === 'high') || [];
                              return highRiskClauses.length > 0 ?
                                highRiskClauses.slice(0, 2).map((clause: any, index: number) => (
                                  <div key={index} className="text-muted-foreground">• {clause.title}</div>
                                )) : <div className="text-muted-foreground">No high-risk clauses</div>;
                            }
                            return <div className="text-muted-foreground">N/A for this document type</div>;
                          })()}
                        </div>
                      </div>

                      <div>
                        <div className="font-medium text-foreground mb-1">Summary</div>
                        <div className="space-y-1">
                          {(() => {
                            if (analysis.result.documentType === 'CONTRACT' || analysis.result.documentType === 'AGREEMENT') {
                              const contractResult = analysis.result as any;
                              const summary = contractResult.summary || 'No summary available';
                              return <div className="text-muted-foreground text-xs line-clamp-3">{summary}</div>;
                            }
                            return <div className="text-muted-foreground">N/A for this document type</div>;
                          })()}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t border-border">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        Uploaded {new Date(document.uploadDate).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {selectedDocument && (
        <>
          <Separator />

          {/* AI Personality Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Personality (for Direct Session)
              </CardTitle>
              <CardDescription>
                Configure the AI negotiator personality for practice sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {DEFAULT_AI_PERSONALITIES.map((personality) => (
                  <div
                    key={personality.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedPersonality === personality.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                        : 'border-border hover:border-accent-foreground/20'
                    }`}
                    onClick={() => setSelectedPersonality(personality.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-foreground">{personality.name}</h4>
                      {selectedPersonality === personality.id && (
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">{personality.description}</p>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-muted-foreground">Aggression:</span>
                        <span className="ml-1 font-medium text-foreground">{personality.config.aggressiveness.toFixed(1)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Flexibility:</span>
                        <span className="ml-1 font-medium text-foreground">{personality.config.flexibility.toFixed(1)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Risk:</span>
                        <span className="ml-1 font-medium text-foreground">{personality.config.riskTolerance.toFixed(1)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Style:</span>
                        <span className="ml-1 font-medium text-foreground">{personality.config.communicationStyle}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-red-700">{error}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleCreateScenario}
              disabled={!selectedDocument || isCreatingScenario}
              className="flex-1"
            >
              {isCreatingScenario ? (
                <>Creating Scenario...</>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Create Scenario Only
                </>
              )}
            </Button>

            <Button
              onClick={handleCreateSession}
              disabled={!selectedDocument || !selectedPersonality || isCreatingSession}
              variant="default"
              className="flex-1"
            >
              {isCreatingSession ? (
                <>Starting Session...</>
              ) : (
                <>
                  <Users className="h-4 w-4 mr-2" />
                  Start Practice Session
                </>
              )}
            </Button>
          </div>
        </>
      )}
    </div>
  );
}