"use client";

import React from 'react';
import { AlertCircle, RefreshCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface DocumentScenarioErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface DocumentScenarioErrorBoundaryProps {
  children: React.ReactNode;
}

export class DocumentScenarioErrorBoundary extends React.Component<
  DocumentScenarioErrorBoundaryProps,
  DocumentScenarioErrorBoundaryState
> {
  constructor(props: DocumentScenarioErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): DocumentScenarioErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Document Scenario Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <AlertCircle className="h-6 w-6 text-red-500 mt-1" />
              <div className="flex-1">
                <h3 className="font-medium text-red-900 dark:text-red-100 mb-2">
                  Error Loading Document Scenarios
                </h3>
                <p className="text-red-700 dark:text-red-200 text-sm mb-4">
                  There was an issue loading your documents. This might be due to a temporary problem with the document analysis service.
                </p>
                <Button
                  onClick={() => {
                    this.setState({ hasError: false });
                    window.location.reload();
                  }}
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  <RefreshCcw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}