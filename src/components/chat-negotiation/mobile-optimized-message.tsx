"use client";

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Lightbulb, DollarSign, Brain, Heart } from 'lucide-react';
import type { ChatNegotiationMessage } from '@/lib/types/chat-negotiation';

interface MobileOptimizedMessageProps {
  message: ChatNegotiationMessage;
  isLatest?: boolean;
}

export function MobileOptimizedMessage({ message, isLatest }: MobileOptimizedMessageProps) {
  const isUser = message.role === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-3 sm:mb-4`}>
      <div className={`max-w-[85%] sm:max-w-[70%] ${isUser ? 'order-1' : 'order-2'}`}>
        <Card
          className={`${
            isUser
              ? 'bg-blue-600 border-blue-600 text-white'
              : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100'
          }`}
        >
          <CardContent className="p-3 sm:p-4">
            <p className="text-sm sm:text-base leading-relaxed">{message.content}</p>
            
            {/* Mobile-optimized extracted data */}
            {message.extractedData && (
              <div className="mt-3 pt-3 border-t border-opacity-20 border-gray-300">
                <div className="space-y-2">
                  {message.extractedData.offer.price && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-3 w-3 opacity-75" />
                      <span className="text-xs opacity-75">
                        ${message.extractedData.offer.price.toLocaleString()}
                      </span>
                    </div>
                  )}
                  {message.extractedData.strategy && (
                    <div className="flex items-center gap-2">
                      <Brain className="h-3 w-3 opacity-75" />
                      <span className="text-xs opacity-75 capitalize">
                        {message.extractedData.strategy}
                      </span>
                    </div>
                  )}
                  {message.extractedData.sentiment && (
                    <div className="flex items-center gap-2">
                      <Heart className="h-3 w-3 opacity-75" />
                      <Badge 
                        variant="secondary" 
                        className="text-xs h-5 px-2"
                      >
                        {message.extractedData.sentiment}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* Mobile-optimized suggestions */}
            {message.suggestions && message.suggestions.length > 0 && (
              <div className="mt-3 pt-3 border-t border-opacity-20 border-gray-300">
                <div className="flex items-center gap-1 mb-2">
                  <Lightbulb className="h-3 w-3 opacity-75" />
                  <span className="text-xs opacity-75 font-medium">Tips:</span>
                </div>
                <div className="space-y-1">
                  {message.suggestions.slice(0, 2).map((suggestion, index) => (
                    <div key={index} className="text-xs opacity-75 pl-2 border-l-2 border-current border-opacity-30">
                      {suggestion}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Timestamp */}
        <div className={`text-xs text-gray-500 mt-1 px-1 ${isUser ? 'text-right' : 'text-left'}`}>
          {new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
          {message.processingTime && (
            <span className="ml-1 opacity-70">({message.processingTime}ms)</span>
          )}
        </div>
      </div>
    </div>
  );
}