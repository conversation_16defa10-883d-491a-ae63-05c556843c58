"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  X, 
  Save, 
  Users, 
  Target, 
  Clock, 
  DollarSign,
  AlertCircle,
  FileText,
  Briefcase,
  Flag,
  Info
} from 'lucide-react';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import type { CreateScenarioRequest, Timeline, ScenarioResponse } from '@/lib/types/chat-negotiation';

const steps = [
  { id: 1, title: 'Core Details', icon: FileText },
  { id: 2, title: 'The Parties', icon: Users },
  { id: 3, title: 'Key Issues', icon: Target },
  { id: 4, title: 'Rules & Review', icon: Flag },
];

const Stepper = ({ currentStep }: { currentStep: number }) => (
  <ol className="flex items-center w-full text-sm font-medium text-center text-gray-500 sm:text-base mb-8">
    {steps.map((step, index) => (
      <li
        key={step.id}
        className={`flex md:w-full items-center ${step.id < currentStep ? 'text-blue-600' : ''} ${index < steps.length - 1 ? "after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10" : ''}`}>
        <span className={`flex items-center ${index < steps.length - 1 ? 'sm:after:hidden' : ''} after:content-['/'] after:mx-2 after:text-gray-200`}>
          {step.id < currentStep ? (
            <svg className="w-4 h-4 mr-2.5 sm:w-5 sm:h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
            </svg>
          ) : (
            <span className="mr-2">{step.id}</span>
          )}
          {step.title}
        </span>
      </li>
    ))}
  </ol>
);

interface Party {
  name: string;
  role: string;
  priorities: string[];
  negotiationStyle: string;
}

interface ScenarioCreationFormProps {
  initialData?: CreateScenarioRequest;
  onSuccess?: (scenarioId: string) => void;
  onCancel?: () => void;
  isEditing?: boolean;
}

export function ScenarioCreationForm({ initialData, onSuccess, onCancel, isEditing = false }: ScenarioCreationFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<CreateScenarioRequest>(initialData || {
    name: '',
    description: '',
    industry: '',
    contractType: '',
    difficulty: 'beginner',
    parties: [
      { name: '', role: '', priorities: [], negotiationStyle: 'collaborative' },
      { name: '', role: '', priorities: [], negotiationStyle: 'competitive' }
    ],
    initialOffer: {
      price: undefined,
      currency: 'USD',
      terms: []
    },
    constraints: {
      maxRounds: 10,
      timeLimit: 30
    },
    tags: [],
    timeline: {
      value: 6,
      unit: 'weeks'
    }
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
    }, [initialData]);

  const [newTag, setNewTag] = useState('');
  const [newPriorities, setNewPriorities] = useState<string[]>([]);
  const [newTerm, setNewTerm] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize or update the newPriorities state when the number of parties changes
    if (formData.parties.length !== newPriorities.length) {
      setNewPriorities(Array(formData.parties.length).fill(''));
    }
  }, [formData.parties.length, newPriorities.length]);

    const handleTimelineChange = (field: keyof Timeline, value: any) => {
    const isNumeric = field === 'value';
    const parsedValue = isNumeric ? parseInt(value, 10) : value;

    if (isNumeric && isNaN(parsedValue)) {
      // Optionally handle invalid number input, e.g., keep it empty or show an error
      return;
    }

    setFormData(prev => ({
      ...prev,
      timeline: { 
        ...prev.timeline,
        [field]: isNumeric ? parsedValue : value
      }
    }));
  };

  const handleInputChange = (field: keyof CreateScenarioRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePartyChange = (index: number, field: keyof Party, value: any) => {
    const updatedParties = [...formData.parties];
    updatedParties[index] = { ...updatedParties[index], [field]: value };
    setFormData(prev => ({ ...prev, parties: updatedParties }));
  };

  const addParty = () => {
    setFormData(prev => ({
      ...prev,
      parties: [...prev.parties, { name: '', role: '', priorities: [], negotiationStyle: 'collaborative' }]
    }));
  };

  const removeParty = (index: number) => {
    if (formData.parties.length > 2) {
      setFormData(prev => ({
        ...prev,
        parties: prev.parties.filter((_, i) => i !== index)
      }));
    }
  };

    const handleNewPriorityChange = (partyIndex: number, value: string) => {
    const updatedNewPriorities = [...newPriorities];
    updatedNewPriorities[partyIndex] = value;
    setNewPriorities(updatedNewPriorities);
  };

  const addPriority = (partyIndex: number) => {
        const priorityToAdd = newPriorities[partyIndex]?.trim();
    if (priorityToAdd) {
      const updatedParties = [...formData.parties];
      updatedParties[partyIndex].priorities.push(priorityToAdd);
      setFormData(prev => ({ ...prev, parties: updatedParties }));
      
      const updatedNewPriorities = [...newPriorities];
      updatedNewPriorities[partyIndex] = '';
      setNewPriorities(updatedNewPriorities);
    }
  };

  const removePriority = (partyIndex: number, priorityIndex: number) => {
    const updatedParties = [...formData.parties];
    updatedParties[partyIndex].priorities.splice(priorityIndex, 1);
    setFormData(prev => ({ ...prev, parties: updatedParties }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter((_, i) => i !== index)
    }));
  };

  const addTerm = () => {
    if (newTerm.trim() && !formData.initialOffer?.terms?.includes(newTerm.trim())) {
      setFormData(prev => ({
        ...prev,
        initialOffer: {
          ...prev.initialOffer,
          terms: [...(prev.initialOffer?.terms || []), newTerm.trim()]
        }
      }));
      setNewTerm('');
    }
  };

  const removeTerm = (index: number) => {
    setFormData(prev => ({
      ...prev,
      initialOffer: {
        ...prev.initialOffer,
        terms: prev.initialOffer?.terms?.filter((_, i) => i !== index)
      }
    }));
  };

  const validateStep = () => {
    setError(null);
    switch (currentStep) {
      case 1:
        if (!formData.name.trim() || !formData.description.trim() || !formData.industry.trim() || !formData.contractType.trim()) {
          setError('Please fill in all required fields in this step.');
          return false;
        }
        break;
      case 2:
        if (formData.parties.some(party => !party.name.trim() || !party.role.trim())) {
          setError('Please complete all party information.');
          return false;
        }
        break;
      default:
        break;
    }
    return true;
  };

  const nextStep = () => {
    if (validateStep()) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep()) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const data = initialData as any;
      if (isEditing && (data?.id || data?._id)) {
        const response = await chatNegotiationService.updateScenario(data.id || data._id, formData);
        onSuccess?.(response._id || response.id);
      } else {
        const response = await chatNegotiationService.createScenario(formData);
        onSuccess?.(response._id || response.id);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to create scenario');
    } finally {
      setIsSubmitting(false);
    }
  };

  const industries = [
    'Technology', 'Healthcare', 'Finance', 'Real Estate', 'Manufacturing',
    'Retail', 'Legal Services', 'Consulting', 'Entertainment', 'Other'
  ];

  const contractTypes = [
    'Software License', 'Service Agreement', 'Employment Contract', 'Lease Agreement',
    'Purchase Agreement', 'Partnership Agreement', 'Consulting Agreement', 'NDA',
    'Supply Agreement', 'Other'
  ];

  const negotiationStyles = [
    'collaborative', 'competitive', 'accommodating', 'analytical', 'assertive'
  ];

  return (
    <TooltipProvider>
      <div className="space-y-6">
      <Stepper currentStep={currentStep} />

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>Core Details</CardTitle>
              <CardDescription>Define the scenario's name, description, industry, and difficulty.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Scenario Name *</Label>
                  <Input id="name" value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} placeholder="e.g., Software License Negotiation" required />
                </div>
                <div>
                  <Label htmlFor="difficulty">Difficulty Level</Label>
                  <Select value={formData.difficulty} onValueChange={(value: any) => handleInputChange('difficulty', value)}>
                    <SelectTrigger><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="expert">Expert</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea id="description" value={formData.description} onChange={(e) => handleInputChange('description', e.target.value)} placeholder="Describe the negotiation scenario..." className="min-h-[80px]" required />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="industry">Industry *</Label>
                  <Select value={formData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                    <SelectTrigger><SelectValue placeholder="Select industry" /></SelectTrigger>
                    <SelectContent>{industries.map(i => <SelectItem key={i} value={i}>{i}</SelectItem>)}</SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="contractType">Contract Type *</Label>
                  <Select value={formData.contractType} onValueChange={(value) => handleInputChange('contractType', value)}>
                    <SelectTrigger><SelectValue placeholder="Select contract type" /></SelectTrigger>
                    <SelectContent>{contractTypes.map(t => <SelectItem key={t} value={t}>{t}</SelectItem>)}</SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle>The Parties</CardTitle>
              <CardDescription>Configure the roles, names, and negotiation styles of the parties involved.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.parties.map((party, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Party {index + 1}</h4>
                    {formData.parties.length > 2 && (
                      <Button type="button" variant="ghost" size="sm" onClick={() => removeParty(index)}><X className="h-4 w-4" /></Button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div>
                      <Label>Party Name *</Label>
                      <Input value={party.name} onChange={(e) => handlePartyChange(index, 'name', e.target.value)} placeholder="e.g., Software Buyer" required />
                    </div>
                    <div>
                      <Label>Role *</Label>
                      <Input value={party.role} onChange={(e) => handlePartyChange(index, 'role', e.target.value)} placeholder="e.g., licensee, vendor" required />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-1">
                      <Label>Negotiation Style</Label>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-gray-400 cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Determines the AI's general approach. <br/>- **Collaborative:** Seeks win-win outcomes. <br/>- **Competitive:** Aims to maximize their own gain.</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Select value={party.negotiationStyle} onValueChange={(value) => handlePartyChange(index, 'negotiationStyle', value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>{negotiationStyles.map(s => <SelectItem key={s} value={s}>{s.charAt(0).toUpperCase() + s.slice(1)}</SelectItem>)}</SelectContent>
                    </Select>
                  </div>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={addParty} className="w-full"><Plus className="h-4 w-4 mr-2" />Add Another Party</Button>
            </CardContent>
          </Card>
        )}

        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle>Key Issues</CardTitle>
              <CardDescription>Detail each party's priorities and the initial offer on the table.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {formData.parties.map((party, index) => (
                <div key={index}>
                  <div className="flex items-center gap-1">
                    <Label className="font-semibold">Priorities for {party.name || `Party ${index + 1}`}</Label>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-gray-400 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>The key interests, goals, or needs for this party. <br/>What do they care about most?</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex gap-2 my-2">
                    <Input value={newPriorities[index] || ''} onChange={(e) => handleNewPriorityChange(index, e.target.value)} placeholder="Add a priority" onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addPriority(index))} />
                    <Button type="button" onClick={() => addPriority(index)} size="sm"><Plus className="h-4 w-4" /></Button>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {party.priorities.map((p, pIndex) => (
                      <Badge key={pIndex} variant="secondary">{p}<Button type="button" variant="ghost" size="sm" className="h-4 w-4 p-0 ml-1" onClick={() => removePriority(index, pIndex)}><X className="h-3 w-3" /></Button></Badge>
                    ))}
                  </div>
                </div>
              ))}
              <hr/>
              <div>
                <Label className="font-semibold">Initial Offer</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2">
                  <div>
                    <Label htmlFor="price">Initial Price</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
                      <Input id="price" type="number" value={formData.initialOffer?.price} onChange={(e) => setFormData(p => ({ ...p, initialOffer: { ...p.initialOffer, price: Number(e.target.value) } }))} placeholder="e.g., 50000" className="pl-8" />
                    </div>
                  </div>
                  <div>
                    <Label>Terms</Label>
                    <div className="flex gap-2">
                      <Input value={newTerm} onChange={(e) => setNewTerm(e.target.value)} placeholder="Add a term" onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTerm())} />
                      <Button type="button" onClick={addTerm} size="sm"><Plus className="h-4 w-4" /></Button>
                    </div>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {formData.initialOffer?.terms?.map((term, index) => (
                        <Badge key={index} variant="secondary">{term}<Button type="button" variant="ghost" size="sm" className="h-4 w-4 p-0 ml-1" onClick={() => removeTerm(index)}><X className="h-3 w-3" /></Button></Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle>Rules & Review</CardTitle>
              <CardDescription>Set constraints like time limits, add tags, and review everything.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="maxRounds">Max Rounds</Label>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-gray-400 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>The maximum number of message exchanges allowed. <br/>A 'round' consists of one message from you and one from the AI.</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Input id="maxRounds" type="number" value={formData.constraints?.maxRounds} onChange={(e) => setFormData(p => ({ ...p, constraints: { ...p.constraints, maxRounds: Number(e.target.value) } }))} placeholder="e.g., 10" />
                </div>
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-gray-400 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>The total time allowed for the entire negotiation session.</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Input id="timeLimit" type="number" value={formData.constraints?.timeLimit || ''} onChange={(e) => setFormData(prev => ({...prev, constraints: {...prev.constraints, timeLimit: parseInt(e.target.value, 10)}}))} />
                </div>
              </div>
              <div className="col-span-1 sm:col-span-2 grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="timeline-value">Timeline</Label>
                  <Input id="timeline-value" type="number" value={formData.timeline?.value || ''} onChange={(e) => handleTimelineChange('value', e.target.value)} placeholder="e.g., 6" />
                </div>
                <div>
                  <Label htmlFor="timeline-unit">Unit</Label>
                  <Select value={formData.timeline?.unit || 'weeks'} onValueChange={(value) => handleTimelineChange('unit', value)}>
                    <SelectTrigger id="timeline-unit"><SelectValue /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="days">Days</SelectItem>
                      <SelectItem value="weeks">Weeks</SelectItem>
                      <SelectItem value="months">Months</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label>Tags</Label>
                <div className="flex gap-2">
                  <Input value={newTag} onChange={(e) => setNewTag(e.target.value)} placeholder="Add a tag" onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())} />
                  <Button type="button" onClick={addTag} size="sm"><Plus className="h-4 w-4" /></Button>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {formData.tags?.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}<Button type="button" variant="ghost" size="sm" className="h-4 w-4 p-0 ml-1" onClick={() => removeTag(index)}><X className="h-3 w-3" /></Button></Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Review Your Scenario</h4>
                <p><strong>Name:</strong> {formData.name}</p>
                <p><strong>Parties:</strong> {formData.parties.map(p => p.name).join(', ')}</p>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-between gap-2">
          <div>
            {currentStep > 1 && (
              <Button type="button" variant="outline" onClick={prevStep}>
                Back
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            {onCancel && currentStep === 1 && (
                <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
            )}
            {currentStep < steps.length && (
              <Button type="button" onClick={nextStep}>
                Next
              </Button>
            )}
            {currentStep === steps.length && (
              <Button type="submit" disabled={isSubmitting}>
                <Save className="mr-2 h-4 w-4" />
                {isSubmitting ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update Scenario' : 'Create Scenario')}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
    </TooltipProvider>
  );
}