"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChatWindow } from "./chat-window";
import { MessageInput } from "./message-input";
import { useChatContext } from "@/lib/chat/chat-context";
import {
	Loader2,
	PlusCircle,
	MessageSquare,
	ChevronDown,
	ChevronRight,
	Settings,
	Menu,
	BarChart,
	Search,
	LineChart,
	FileText,
	Sparkles,
	Gavel,
 } from "lucide-react";
import {
	getStandaloneRoutesWithActive,
	getRouteCategoriesWithActive,
	ROUTE_CATEGORIES,
	DEV_ROUTE_CATEGORIES,
} from "@/lib/navigation/navigation-config";
import { format } from "date-fns";
import { usePathname, useRouter } from "next/navigation";
import { chatService } from "@/lib/services/chat-service";
import { ChatHistoryItem } from "./chat-history-item";
import { ThemeToggle } from "../../app/theme-toggle";
import { ResizablePanel } from "@/components/ui/resizable-panel";
import Link from "next/link";
import Image from "next/image";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "../ui/collapsible";
import { cn } from "../../lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useMediaQuery } from "@/hooks/use-media-query";
import { DevOnlyWrapper } from "@/components/dev-only-wrapper";

export function ChatContainer() {
	const { createSession, currentSession, documentUploaded, clearSession } =
		useChatContext();
	const router = useRouter();
	const pathname = usePathname();
	const [chatSessions, setChatSessions] = useState<
		Array<{
			id: string;
			title: string;
			createdAt: string;
			documentId?: string;
		}>
	>([]);

	const [loadingSessions, setLoadingSessions] = useState(false);
	const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
		"your documents": true,
		"ai that actually works": true,
		"real legal tools": true,
		"compare like a pro": true,
		"🚧 beta features": true,
	});

	const toggleCategory = (category: string) => {
		setExpandedCategories((prev) => ({
			...prev,
			[category]: !prev[category],
		}));
	};

	const switchSession = useCallback(
		async (sessionId: string) => {
			try {
				setLoadingSessions(true);
				const session = chatSessions.find((s) => s.id === sessionId);
				if (session && session.documentId) {
					await createSession(session.id);
					router.push(`/chat/${sessionId}`);
				}
			} catch (error) {
				console.error("Failed to switch session:", error);
			} finally {
				setLoadingSessions(false);
			}
		},
		[createSession, router, chatSessions]
	);

	const deleteSession = useCallback(
		async (sessionId: string) => {
			try {
				await chatService.deleteSession(sessionId);
				setChatSessions(chatSessions.filter((s) => s.id !== sessionId));
				if (currentSession?.id === sessionId) {
					clearSession();
					router.replace("/chat", { scroll: false });
				}
			} catch (error) {
				console.error("Failed to delete session:", error);
			}
		},
		[chatSessions, clearSession, currentSession, router]
	);

	const renameSession = useCallback(async (sessionId: string) => {
		// This would typically open a dialog to rename
		// For now, we'll just log it
		console.log("Rename session:", sessionId);
		// In a real implementation, you would:
		// 1. Show a dialog with an input field
		// 2. Call an API to update the session title
		// 3. Update the local state with setChatSessions
	}, []);

	useEffect(() => {
		const loadSessions = async () => {
			try {
				setLoadingSessions(true);
				const sessions = await chatService.getSessions();
				// Sort sessions by creation date (newest first)
				const sortedSessions = sessions.sort(
					(a, b) =>
						new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
				);
				setChatSessions(sortedSessions);
			} catch (error) {
				console.error("Failed to load chat sessions:", error);
			} finally {
				setLoadingSessions(false);
			}
		};

		loadSessions();
	}, []);

	const startNewChat = () => {
		setLoadingSessions(false);
		clearSession();
		router.replace("/chat", { scroll: false });
	};

	const chatHistory = (
		<div className="h-full flex flex-col bg-background border-r border-border">
			{/* Navigation Section - Independently Scrollable */}
			<div className="flex-1 min-h-0 max-h-[60vh]">
				<ScrollArea className="h-full px-3 py-2">
					<nav className="space-y-1">
						{/* Standalone Routes */}
						{getStandaloneRoutesWithActive(pathname).map((route) => (
							<Link
								key={route.href}
								href={route.href}
								className={cn(
									"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
									route.active
										? "bg-secondary dark:bg-[#252525] text-foreground"
										: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
								)}
							>
								<route.icon className="h-4 w-4 mr-3" />
								{route.label}
							</Link>
						))}

						{/* Production Route Categories */}
						{getRouteCategoriesWithActive(pathname, ROUTE_CATEGORIES).map((category) => (
							<Collapsible
								key={category.title}
								open={expandedCategories[category.title.toLowerCase()]}
								onOpenChange={() => toggleCategory(category.title.toLowerCase())}
								className="w-full"
							>
								<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-muted-foreground">
									<div className="flex items-center gap-3">
										<category.icon className="h-4 w-4" />
										<span>{category.title}</span>
									</div>
									{expandedCategories[category.title.toLowerCase()] ? (
										<ChevronDown className="h-4 w-4" />
									) : (
										<ChevronRight className="h-4 w-4" />
									)}
								</CollapsibleTrigger>
								<CollapsibleContent className="pl-10 space-y-1">
									{category.routes.map((route) => (
										<Link
											key={route.href}
											href={route.href}
											className={cn(
												"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
												route.active
													? "text-white"
													: "text-muted-foreground"
											)}
										>
											{route.label}
										</Link>
									))}
								</CollapsibleContent>
							</Collapsible>
						))}

						{/* Dev-only Features */}
						<DevOnlyWrapper>
							{getRouteCategoriesWithActive(pathname, DEV_ROUTE_CATEGORIES).map((category) => (
								<Collapsible
									key={category.title}
									open={expandedCategories[category.title.toLowerCase()]}
									onOpenChange={() => toggleCategory(category.title.toLowerCase())}
									className="w-full"
								>
									<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-orange-400">
										<div className="flex items-center gap-3">
											<category.icon className="h-4 w-4" />
											<span>{category.title}</span>
											<span className="text-xs bg-orange-500 text-white px-1.5 py-0.5 rounded-full">
												DEV
											</span>
										</div>
										{expandedCategories[category.title.toLowerCase()] ? (
											<ChevronDown className="h-4 w-4" />
										) : (
											<ChevronRight className="h-4 w-4" />
										)}
									</CollapsibleTrigger>
									<CollapsibleContent className="pl-10 space-y-1">
										{category.routes.map((route) => (
											<Link
												key={route.href}
												href={route.href}
												className={cn(
													"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted text-orange-400",
													route.active
														? "text-orange-300 bg-orange-900/20"
														: "text-orange-400"
												)}
											>
												{route.label}
												<span className="text-xs bg-orange-500 text-white px-1 py-0.5 rounded">
													DEV
												</span>
											</Link>
										))}
									</CollapsibleContent>
								</Collapsible>
							))}
						</DevOnlyWrapper>
					</nav>
				</ScrollArea>
			</div>

			{/* Divider */}
			<div className="border-t border-border mx-3"></div>

			{/* Recent Chats Header */}
			<div className="px-3 py-2 border-b border-border flex-shrink-0">
				<div className="flex items-center justify-between">
					<div className="flex items-center text-xs font-medium text-muted-foreground uppercase tracking-wider">
						<MessageSquare className="h-3.5 w-3.5 mr-1.5" />
						Recent Chats
					</div>
					{chatSessions.length > 0 && (
						<Button
							variant="ghost"
							size="sm"
							className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
							onClick={startNewChat}
						>
							<PlusCircle className="h-3 w-3 mr-1" />
							New
						</Button>
					)}
				</div>
			</div>

			{/* Recent Chats Section - Independently Scrollable */}
			<div className="flex-1 min-h-0">
				<ScrollArea className="h-full px-2 py-2">
					{loadingSessions ? (
						<div className="flex justify-center p-4">
							<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
						</div>
					) : chatSessions.length === 0 ? (
						<div className="text-center py-6 text-muted-foreground text-sm">
							No recent chats
						</div>
					) : (
						<div className="space-y-1 py-2">
							{chatSessions.map((session) => (
								<ChatHistoryItem
									key={session.id}
									title={session.title}
									timestamp={format(new Date(session.createdAt), "MMM d, yyyy")}
									isActive={currentSession?.id === session.id}
									onClick={() => switchSession(session.id)}
									onDelete={() => deleteSession(session.id)}
									onRename={() => renameSession(session.id)}
								/>
							))}
						</div>
					)}
				</ScrollArea>
			</div>
		</div>
	);

	const chatArea = (
		<div className="h-full w-full flex flex-col bg-background">
			<div className="flex-1 flex flex-col min-h-0">
				<div className="flex-1 overflow-hidden">
					<ChatWindow />
				</div>
				{(currentSession || documentUploaded) && (
					<div className="flex-shrink-0">
						<MessageInput />
					</div>
				)}
			</div>
		</div>
	);

	// Check if we're on mobile
	const isMobile = useMediaQuery("(max-width: 768px)");

	return (
		<div className="h-screen overflow-hidden bg-background text-foreground chat-interface">
			{isMobile ? (
				<div className="flex flex-col h-full">
					<div className="border-b border-border flex justify-between items-center h-14 px-4 flex-shrink-0">
						<Link href="/" className="flex items-center">
							<Image
								src="/logos/light-512.png"
								alt="Docgic Logo Light"
								width={100}
								height={100}
								className="w-20 object-cover block dark:hidden"
							/>
							<Image
								src="/logos/trans-512.png"
								alt="Docgic Logo Dark"
								width={100}
								height={100}
								className="w-20 object-cover hidden dark:block"
							/>
						</Link>
						<div className="flex items-center space-x-2">
							<ThemeToggle />
							<Sheet>
								<SheetTrigger asChild>
									<Button variant="ghost" size="icon" className="h-9 w-9">
										<Menu className="h-5 w-5" />
									</Button>
								</SheetTrigger>
								<SheetContent side="left" className="p-0 w-80 max-w-[85vw]">
									<div className="h-full pt-14">{chatHistory}</div>
								</SheetContent>
							</Sheet>
						</div>
					</div>
					<div className="flex-1 min-h-0">{chatArea}</div>
				</div>
			) : (
				<ResizablePanel
					leftPanel={chatHistory}
					rightPanel={chatArea}
					initialLeftWidth={320}
					minLeftWidth={280}
					maxLeftWidth={480}
				/>
			)}
		</div>
	);
}