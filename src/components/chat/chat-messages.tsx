'use client';

import { Chat, Message } from '@/lib/types/chat.types';
import React from 'react';

interface ChatMessagesProps {
  chat: Chat | null;
  messages: Message[];
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  isLoadingSession: boolean;
  setIsLoadingSession: React.Dispatch<React.SetStateAction<boolean>>;
  onNewMessageSent: () => void;
}

export function ChatMessages({ chat, messages, isLoadingSession }: ChatMessagesProps) {
  if (isLoadingSession) {
    return <div className="flex justify-center items-center h-full">Loading chat...</div>;
  }

  if (!chat) {
    return <div className="flex justify-center items-center h-full">Select a chat to start messaging.</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">{chat.name}</h2>
      <div className="space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`rounded-lg px-4 py-2 ${message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
              {message.content}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
