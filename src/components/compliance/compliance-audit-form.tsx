"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
	Loader2,
	FileText,
	Shield,
	AlertCircle,
	CheckCircle,
} from "lucide-react";
import { useDocuments } from "@/hooks/use-documents";
import {
	useRegulatoryFrameworks,
	useComplianceProfiles,
	useComplianceAudit,
} from "@/hooks/use-compliance";

const auditFormSchema = z.object({
	documentId: z.string().min(1, "Please select a document"),
	frameworks: z
		.array(z.string())
		.min(1, "Please select at least one framework"),
	profileId: z.string().optional(),
	includeRecommendations: z.boolean().default(true),
	detailedAnalysis: z.boolean().default(true),
	riskThreshold: z.enum(["low", "medium", "high"]).default("medium"),
});

type AuditFormValues = z.infer<typeof auditFormSchema>;

export function ComplianceAuditForm() {
	const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([]);

	const { documents, loading: documentsLoading } = useDocuments();
	const { data: frameworksData, isLoading: frameworksLoading } =
		useRegulatoryFrameworks();
	const { data: profilesData, isLoading: profilesLoading } =
		useComplianceProfiles();
	const { startAudit, isAuditing, auditResult, auditError } =
		useComplianceAudit();

	const form = useForm<AuditFormValues>({
		resolver: zodResolver(auditFormSchema),
		defaultValues: {
			frameworks: [],
			includeRecommendations: true,
			detailedAnalysis: true,
			riskThreshold: "medium",
		},
	});

	const frameworks = frameworksData?.frameworks || [];
	const profiles = profilesData?.profiles || [];
	const userDocuments = documents || [];

	const onSubmit = (values: AuditFormValues) => {
		startAudit(values.documentId);
	};

	const handleFrameworkToggle = (frameworkId: string, checked: boolean) => {
		const currentFrameworks = form.getValues("frameworks");
		if (checked) {
			const newFrameworks = [...currentFrameworks, frameworkId];
			form.setValue("frameworks", newFrameworks);
			setSelectedFrameworks(newFrameworks);
		} else {
			const newFrameworks = currentFrameworks.filter(
				(id) => id !== frameworkId
			);
			form.setValue("frameworks", newFrameworks);
			setSelectedFrameworks(newFrameworks);
		}
	};

	if (auditResult) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-green-600">
						<CheckCircle className="h-5 w-5" />
						Audit Completed Successfully
					</CardTitle>
					<CardDescription>
						Your compliance audit has been completed. View the results below.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="text-center">
							<div className="text-2xl font-bold">
								{auditResult.overallScore.toFixed(1)}
							</div>
							<div className="text-sm text-muted-foreground">Overall Score</div>
						</div>
						<div className="text-center">
							<Badge
								variant={
									auditResult.riskLevel === "low"
										? "default"
										: auditResult.riskLevel === "medium"
										? "secondary"
										: "destructive"
								}
							>
								{auditResult.riskLevel.toUpperCase()} RISK
							</Badge>
							<div className="text-sm text-muted-foreground mt-1">
								Risk Level
							</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold">
								{auditResult.findings.length}
							</div>
							<div className="text-sm text-muted-foreground">Findings</div>
						</div>
					</div>

					<Separator />

					<div className="space-y-2">
						<h4 className="font-medium">Framework Results</h4>
						{Object.entries(auditResult.frameworkResults).map(
							([framework, result]) => (
								<div
									key={framework}
									className="flex items-center justify-between p-2 bg-muted rounded"
								>
									<span className="font-medium">{framework}</span>
									<div className="flex items-center gap-2">
										<span className="text-sm">
											Score: {result.score.toFixed(1)}
										</span>
										<Badge variant="outline">
											{result.violations} violations
										</Badge>
									</div>
								</div>
							)
						)}
					</div>

					<Button onClick={() => window.location.reload()} className="w-full">
						Start New Audit
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
				{/* Document Selection */}
				<FormField
					control={form.control}
					name="documentId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Select Document</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Choose a document to audit" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									{documentsLoading ? (
										<SelectItem value="_loading" disabled>
											<Loader2 className="h-4 w-4 animate-spin mr-2" />
											Loading documents...
										</SelectItem>
									) : userDocuments.length === 0 ? (
										<SelectItem value="_no_documents" disabled>
											No documents available
										</SelectItem>
									) : (
										userDocuments.map((doc) => (
											<SelectItem key={doc.id} value={doc.id}>
												<div className="flex items-center gap-2">
													<FileText className="h-4 w-4" />
													{doc.title}
												</div>
											</SelectItem>
										))
									)}
								</SelectContent>
							</Select>
							<FormDescription>
								Select the document you want to audit for compliance
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Framework Selection */}
				<FormField
					control={form.control}
					name="frameworks"
					render={() => (
						<FormItem>
							<FormLabel>Regulatory Frameworks</FormLabel>
							<FormDescription>
								Select the regulatory frameworks to audit against
							</FormDescription>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{frameworksLoading ? (
									<div className="col-span-2 flex items-center justify-center p-4">
										<Loader2 className="h-4 w-4 animate-spin mr-2" />
										Loading frameworks...
									</div>
								) : (
									frameworks.map((framework) => (
										<FormField
											key={framework.id}
											control={form.control}
											name="frameworks"
											render={({ field }) => (
												<FormItem className="flex flex-row items-start space-x-3 space-y-0">
													<FormControl>
														<Checkbox
															checked={field.value?.includes(framework.id)}
															onCheckedChange={(checked) =>
																handleFrameworkToggle(
																	framework.id,
																	checked as boolean
																)
															}
														/>
													</FormControl>
													<div className="space-y-1 leading-none">
														<FormLabel className="font-medium">
															{framework.name}
														</FormLabel>
														<FormDescription className="text-xs">
															{framework.jurisdiction} •{" "}
															{framework.categories.join(", ")}
														</FormDescription>
													</div>
												</FormItem>
											)}
										/>
									))
								)}
							</div>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Compliance Profile */}
				<FormField
					control={form.control}
					name="profileId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Compliance Profile (Optional)</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select a compliance profile" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="none">No profile</SelectItem>
									{profilesLoading ? (
										<SelectItem value="loading" disabled>
											<Loader2 className="h-4 w-4 animate-spin mr-2" />
											Loading profiles...
										</SelectItem>
									) : (
										profiles.map((profile) => (
											<SelectItem key={profile.id} value={profile.id}>
												<div className="flex items-center gap-2">
													<Shield className="h-4 w-4" />
													{profile.name}
													<Badge variant="outline">{profile.industry}</Badge>
												</div>
											</SelectItem>
										))
									)}
								</SelectContent>
							</Select>
							<FormDescription>
								Use a pre-configured compliance profile for industry-specific
								rules
							</FormDescription>
						</FormItem>
					)}
				/>

				{/* Audit Options */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Audit Options</h3>

					<FormField
						control={form.control}
						name="includeRecommendations"
						render={({ field }) => (
							<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
								<div className="space-y-0.5">
									<FormLabel className="text-base">
										Include Recommendations
									</FormLabel>
									<FormDescription>
										Generate actionable recommendations for compliance
										improvements
									</FormDescription>
								</div>
								<FormControl>
									<Switch
										checked={field.value}
										onCheckedChange={field.onChange}
									/>
								</FormControl>
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="detailedAnalysis"
						render={({ field }) => (
							<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
								<div className="space-y-0.5">
									<FormLabel className="text-base">Detailed Analysis</FormLabel>
									<FormDescription>
										Perform comprehensive analysis with detailed findings
									</FormDescription>
								</div>
								<FormControl>
									<Switch
										checked={field.value}
										onCheckedChange={field.onChange}
									/>
								</FormControl>
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="riskThreshold"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Risk Threshold</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="low">
											Low - Report all potential issues
										</SelectItem>
										<SelectItem value="medium">
											Medium - Report significant issues
										</SelectItem>
										<SelectItem value="high">
											High - Report only critical issues
										</SelectItem>
									</SelectContent>
								</Select>
								<FormDescription>
									Set the sensitivity level for compliance issue detection
								</FormDescription>
							</FormItem>
						)}
					/>
				</div>

				{/* Error Display */}
				{auditError && (
					<Alert variant="destructive">
						<AlertCircle className="h-4 w-4" />
						<AlertDescription>{auditError.message}</AlertDescription>
					</Alert>
				)}

				{/* Submit Button */}
				<Button
					type="submit"
					className="w-full"
					disabled={isAuditing || selectedFrameworks.length === 0}
				>
					{isAuditing ? (
						<>
							<Loader2 className="h-4 w-4 animate-spin mr-2" />
							Running Compliance Audit...
						</>
					) : (
						<>
							<Shield className="h-4 w-4 mr-2" />
							Start Compliance Audit
						</>
					)}
				</Button>
			</form>
		</Form>
	);
}
