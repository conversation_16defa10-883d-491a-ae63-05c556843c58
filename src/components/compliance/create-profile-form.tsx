'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Plus, Trash2, AlertCircle } from 'lucide-react';
import { useState } from 'react';
import { useCreateProfile, useRegulatoryFrameworks } from '@/hooks/use-compliance';
import { Industry, CreateProfileRequest } from '@/lib/types/compliance';

const createProfileSchema = z.object({
  name: z.string().min(1, 'Profile name is required'),
  description: z.string().min(1, 'Description is required'),
  industry: z.enum(['Healthcare', 'Financial', 'Technology', 'Legal', 'Government', 'Other']),
  frameworks: z.array(z.object({
    name: z.string(),
    version: z.string(),
    enabled: z.boolean(),
    weight: z.number().min(0).max(1),
  })).min(1, 'At least one framework is required'),
  riskThresholds: z.object({
    low: z.number().min(0).max(1),
    medium: z.number().min(0).max(1),
    high: z.number().min(0).max(1),
  }),
  customRequirements: z.array(z.object({
    category: z.string(),
    requirement: z.string(),
    mandatory: z.boolean(),
  })),
});

type CreateProfileFormValues = z.infer<typeof createProfileSchema>;

interface CreateProfileFormProps {
  onSuccess?: () => void;
}

export function CreateProfileForm({ onSuccess }: CreateProfileFormProps) {
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([]);
  const [customRequirements, setCustomRequirements] = useState<Array<{
    category: string;
    requirement: string;
    mandatory: boolean;
  }>>([]);

  const { data: frameworksData, isLoading: frameworksLoading } = useRegulatoryFrameworks();
  const createProfileMutation = useCreateProfile();

  const form = useForm<CreateProfileFormValues>({
    resolver: zodResolver(createProfileSchema),
    defaultValues: {
      name: '',
      description: '',
      industry: 'Technology',
      frameworks: [],
      riskThresholds: {
        low: 0.3,
        medium: 0.6,
        high: 0.8,
      },
      customRequirements: [],
    },
  });

  const frameworks = frameworksData?.frameworks || [];

  const onSubmit = async (values: CreateProfileFormValues) => {
    try {
      // Ensure all required fields are present before submitting
      const request: CreateProfileRequest = {
        name: values.name,
        description: values.description,
        industry: values.industry,
        frameworks: values.frameworks.map(f => ({
          name: f.name,
          version: f.version,
          enabled: f.enabled,
          weight: f.weight
        })),
        riskThresholds: {
          low: values.riskThresholds.low,
          medium: values.riskThresholds.medium,
          high: values.riskThresholds.high
        },
        customRequirements: values.customRequirements.map(r => ({
          category: r.category,
          requirement: r.requirement,
          mandatory: r.mandatory
        }))
      };
      await createProfileMutation.mutateAsync(request);
      onSuccess?.();
    } catch (error) {
      console.error('Failed to create profile:', error);
    }
  };

  const handleFrameworkToggle = (frameworkId: string, checked: boolean) => {
    const framework = frameworks.find(f => f.id === frameworkId);
    if (!framework) return;

    const currentFrameworks = form.getValues('frameworks');
    
    if (checked) {
      const newFramework = {
        name: framework.name,
        version: framework.version,
        enabled: true,
        weight: 1.0 / (currentFrameworks.length + 1), // Equal weight distribution
      };
      
      // Redistribute weights
      const updatedFrameworks = currentFrameworks.map(f => ({
        ...f,
        weight: 1.0 / (currentFrameworks.length + 1),
      }));
      
      form.setValue('frameworks', [...updatedFrameworks, newFramework]);
      setSelectedFrameworks([...selectedFrameworks, frameworkId]);
    } else {
      const filteredFrameworks = currentFrameworks.filter(f => f.name !== framework.name);
      
      // Redistribute weights
      const updatedFrameworks = filteredFrameworks.map(f => ({
        ...f,
        weight: filteredFrameworks.length > 0 ? 1.0 / filteredFrameworks.length : 0,
      }));
      
      form.setValue('frameworks', updatedFrameworks);
      setSelectedFrameworks(selectedFrameworks.filter(id => id !== frameworkId));
    }
  };

  const addCustomRequirement = () => {
    const newRequirement = {
      category: '',
      requirement: '',
      mandatory: false,
    };
    setCustomRequirements([...customRequirements, newRequirement]);
    form.setValue('customRequirements', [...customRequirements, newRequirement]);
  };

  const removeCustomRequirement = (index: number) => {
    const updated = customRequirements.filter((_, i) => i !== index);
    setCustomRequirements(updated);
    form.setValue('customRequirements', updated);
  };

  const updateCustomRequirement = (index: number, field: string, value: any) => {
    const updated = customRequirements.map((req, i) => 
      i === index ? { ...req, [field]: value } : req
    );
    setCustomRequirements(updated);
    form.setValue('customRequirements', updated);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Define the basic details for your compliance profile
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Profile Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Healthcare Compliance Profile" {...field} />
                  </FormControl>
                  <FormDescription>
                    A descriptive name for this compliance profile
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe the purpose and scope of this compliance profile..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Explain what this profile is designed for and its intended use
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Industry</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Healthcare">Healthcare</SelectItem>
                      <SelectItem value="Financial">Financial Services</SelectItem>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Legal">Legal Services</SelectItem>
                      <SelectItem value="Government">Government</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The industry this profile is designed for
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Framework Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Regulatory Frameworks</CardTitle>
            <CardDescription>
              Select the regulatory frameworks to include in this profile
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="frameworks"
              render={() => (
                <FormItem>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {frameworksLoading ? (
                      <div className="col-span-2 flex items-center justify-center p-4">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading frameworks...
                      </div>
                    ) : (
                      frameworks.map((framework) => (
                        <div key={framework.id} className="flex items-start space-x-3 space-y-0 p-3 border rounded-lg">
                          <Checkbox
                            checked={selectedFrameworks.includes(framework.id)}
                            onCheckedChange={(checked) => 
                              handleFrameworkToggle(framework.id, checked as boolean)
                            }
                          />
                          <div className="space-y-1 leading-none flex-1">
                            <div className="font-medium">{framework.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {framework.jurisdiction} • {framework.version}
                            </div>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {framework.categories.map((category) => (
                                <Badge key={category} variant="outline" className="text-xs">
                                  {category}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Risk Thresholds */}
        <Card>
          <CardHeader>
            <CardTitle>Risk Thresholds</CardTitle>
            <CardDescription>
              Define the risk level thresholds for compliance scoring
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="riskThresholds.low"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Low Risk Threshold</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.1" 
                        min="0" 
                        max="1"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>0.0 - 1.0</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="riskThresholds.medium"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Medium Risk Threshold</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.1" 
                        min="0" 
                        max="1"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>0.0 - 1.0</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="riskThresholds.high"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>High Risk Threshold</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.1" 
                        min="0" 
                        max="1"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>0.0 - 1.0</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Custom Requirements */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Requirements</CardTitle>
            <CardDescription>
              Add organization-specific compliance requirements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {customRequirements.map((req, index) => (
              <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                <div className="flex-1 space-y-3">
                  <Input
                    placeholder="Category (e.g., data_security)"
                    value={req.category}
                    onChange={(e) => updateCustomRequirement(index, 'category', e.target.value)}
                  />
                  <Textarea
                    placeholder="Requirement description..."
                    value={req.requirement}
                    onChange={(e) => updateCustomRequirement(index, 'requirement', e.target.value)}
                  />
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={req.mandatory}
                      onCheckedChange={(checked) => updateCustomRequirement(index, 'mandatory', checked)}
                    />
                    <label className="text-sm">Mandatory requirement</label>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeCustomRequirement(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <Button
              type="button"
              variant="outline"
              onClick={addCustomRequirement}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Custom Requirement
            </Button>
          </CardContent>
        </Card>

        {/* Error Display */}
        {createProfileMutation.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {createProfileMutation.error.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onSuccess}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={createProfileMutation.isPending}
          >
            {createProfileMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Creating Profile...
              </>
            ) : (
              'Create Profile'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
