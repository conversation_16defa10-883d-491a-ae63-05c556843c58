"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { documentService } from "@/lib/services/document-service";
import type { DocumentMetadata } from "@/lib/services/document-service";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  CreateDepositionPreparationDto,
  DepositionPreparation,
  DepositionStatus,
  UpdateDepositionPreparationDto,
} from "@/lib/types/deposition";
import { depositionService } from "@/lib/services/deposition-service";
import { SimpleDocumentSelector } from "@/components/document-comparison/simple-document-selector";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  targetWitnesses: z
    .array(z.string())
    .min(1, "At least one witness is required"),
  caseContext: z.string().min(1, "Case context is required"),
  keyIssues: z.array(z.string()).min(1, "At least one key issue is required"),
  relatedDocumentIds: z.array(z.string()).optional().default([]),
  status: z
    .enum(["DRAFT", "IN_PROGRESS", "COMPLETED"])
    .optional()
    .default("DRAFT"),
});

type DepositionFormProps = {
  initialData?: DepositionPreparation;
  isEditing?: boolean;
};

export function DepositionForm({
  initialData,
  isEditing = false,
}: DepositionFormProps) {
  const router = useRouter();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialData?.title || "",
      description: initialData?.description || "",
      targetWitnesses: initialData?.targetWitnesses || [],
      caseContext: initialData?.caseContext || "",
      keyIssues: initialData?.keyIssues || [],
      relatedDocumentIds: initialData?.relatedDocumentIds || [],
      status: initialData?.status || "DRAFT",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      if (isEditing && initialData) {
        const updateData: UpdateDepositionPreparationDto = {
          title: values.title,
          description: values.description,
          targetWitnesses: values.targetWitnesses,
          caseContext: values.caseContext,
          keyIssues: values.keyIssues,
          relatedDocumentIds: values.relatedDocumentIds,
          status: values.status as DepositionStatus,
        };

        await depositionService.updateDepositionPreparation(
          initialData.id,
          updateData
        );
        toast({
          title: "Success",
          description: "Deposition preparation updated successfully",
        });
        router.push(`/depositions/${initialData.id}`);
      } else {
        const createData: CreateDepositionPreparationDto = {
          title: values.title,
          description: values.description,
          targetWitnesses: values.targetWitnesses,
          caseContext: values.caseContext,
          keyIssues: values.keyIssues,
          relatedDocumentIds: values.relatedDocumentIds,
        };

        const result = await depositionService.createDepositionPreparation(
          createData
        );
        toast({
          title: "Success",
          description: "Deposition preparation created successfully",
        });
        router.push(`/depositions/${result.id}`);
      }
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  };

  // Helper to add items to form array fields
  const handleItemInput = (
    value: string,
    fieldName: "targetWitnesses" | "keyIssues",
    clearOnComma: boolean = false
  ): { newValue: string; shouldClear: boolean } => {
    const items = value
      .split(",")
      .map((item) => item.trim())
      .filter((item: string) => item.length > 0);

    if (items.length > 0) {
      const currentValues: string[] = form.getValues(fieldName) || [];
      form.setValue(fieldName, [
        ...new Set([...currentValues, ...items]),
      ] as string[]);
      return { newValue: "", shouldClear: true };
    }
    return { newValue: value, shouldClear: clearOnComma };
  };

  // Add local state and watch for comma-separated inputs
  const [witnessesInput, setWitnessesInput] = React.useState("");
  const [issuesInput, setIssuesInput] = React.useState("");
  const [documents, setDocuments] = React.useState<Record<string, DocumentMetadata>>({})

  // Sync input fields with form values
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "targetWitnesses") {
        setWitnessesInput((prev) => {
          // Only update if we're not in the middle of typing
          if (!prev.includes(",")) {
            return Array.isArray(value.targetWitnesses)
              ? value.targetWitnesses.join(", ")
              : "";
          }
          return prev;
        });
      }
      if (name === "keyIssues") {
        setIssuesInput((prev) => {
          if (!prev.includes(",")) {
            return Array.isArray(value.keyIssues)
              ? value.keyIssues.join(", ")
              : "";
          }
          return prev;
        });
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Fetch documents for related documents
  React.useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const response = await documentService.getDocuments();
        const docsMap = response.items.reduce((acc, doc) => {
          acc[doc.id] = doc;
          return acc;
        }, {} as Record<string, DocumentMetadata>);
        setDocuments(docsMap);
      } catch (error) {
        console.error('Error fetching documents:', error);
      }
    };

    fetchDocuments();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isEditing
            ? "Edit Deposition Preparation"
            : "Create Deposition Preparation"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter a title for this deposition"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide additional details about this deposition"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="targetWitnesses"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target Witnesses</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {field.value.map((witness, index) => (
                          <div
                            key={index}
                            className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
                          >
                            <span>{witness}</span>
                            <button
                              type="button"
                              onClick={() =>
                                form.setValue(
                                  "targetWitnesses",
                                  field.value.filter((_, i) => i !== index)
                                )
                              }
                              className="text-secondary-foreground/70 hover:text-secondary-foreground"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Type a name and press comma or enter"
                          value={witnessesInput}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            setWitnessesInput(newValue);

                            // Handle comma immediately
                            if (newValue.endsWith(",")) {
                              const { shouldClear } = handleItemInput(
                                newValue.slice(0, -1), // Remove the trailing comma
                                "targetWitnesses",
                                true
                              );
                              if (shouldClear) {
                                setWitnessesInput("");
                              }
                            }
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              const { newValue } = handleItemInput(
                                witnessesInput,
                                "targetWitnesses"
                              );
                              setWitnessesInput(newValue);
                            }
                          }}
                          aria-label="Enter target witnesses"
                        />
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={() => {
                            const { newValue } = handleItemInput(witnessesInput, "targetWitnesses");
                            setWitnessesInput(newValue);
                          }}
                        >
                          Add
                        </Button>
                      </div>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Add witnesses individually or comma-separated
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="caseContext"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Case Context</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the context of the case"
                      {...field}
                      className="min-h-[100px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="keyIssues"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Key Issues</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {field.value.map((issue, index) => (
                          <div
                            key={index}
                            className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
                          >
                            <span>{issue}</span>
                            <button
                              type="button"
                              onClick={() =>
                                form.setValue(
                                  "keyIssues",
                                  field.value.filter((_, i) => i !== index)
                                )
                              }
                              className="text-secondary-foreground/70 hover:text-secondary-foreground"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Type an issue and press comma or enter"
                          value={issuesInput}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            setIssuesInput(newValue);

                            // Handle comma immediately
                            if (newValue.endsWith(",")) {
                              const { shouldClear } = handleItemInput(
                                newValue.slice(0, -1), // Remove the trailing comma
                                "keyIssues",
                                true
                              );
                              if (shouldClear) {
                                setIssuesInput("");
                              }
                            }
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              const { newValue } = handleItemInput(
                                issuesInput,
                                "keyIssues"
                              );
                              setIssuesInput(newValue);
                            }
                          }}
                          aria-label="Enter key issues"
                        />
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={() => {
                            const { newValue } = handleItemInput(
                              issuesInput,
                              "keyIssues"
                            );
                            setIssuesInput(newValue);
                          }}
                        >
                          Add
                        </Button>
                      </div>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Add key issues individually or comma-separated
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="relatedDocumentIds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Related Documents</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        {field.value.map((docId, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between bg-secondary p-2 rounded-md"
                          >
                            <span className="text-sm">
                              {documents[docId]?.title || documents[docId]?.filename || docId}
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const newIds = [...field.value];
                                newIds.splice(index, 1);
                                form.setValue("relatedDocumentIds", newIds);
                              }}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            type="button"
                            variant="outline"
                            className="w-full"
                          >
                            Add Document
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[600px]">
                          <DialogHeader>
                            <DialogTitle>Select a Document</DialogTitle>
                          </DialogHeader>
                          <div className="py-4">
                            <SimpleDocumentSelector
                              onSelect={(docId) => {
                                // Only add if not already in the list
                                if (!field.value.includes(docId)) {
                                  form.setValue("relatedDocumentIds", [
                                    ...field.value,
                                    docId,
                                  ]);
                                }
                              }}
                              title="Select a Related Document"
                              description="Choose a document to add to this deposition"
                            />
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Select documents that are relevant to this deposition
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {isEditing && (
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="DRAFT">Draft</SelectItem>
                        <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                        <SelectItem value="COMPLETED">Completed</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/depositions")}
              >
                Cancel
              </Button>
              <Button type="submit">
                {isEditing ? "Update" : "Create"} Deposition
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

export default DepositionForm;
