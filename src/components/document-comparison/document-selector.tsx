"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea } from "@/components/ui/scroll-area";
import { documentService, type DocumentMetadata } from "@/lib/services/document-service";
import { Loader2, FileText, Search, TagIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { DocumentOrganizationPanel } from "./document-organization-panel";

interface Document {
  id: string;
  title: string;
  type: string;
  date: string;
}

export function DocumentSelector() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [documentsMetadata, setDocumentsMetadata] = useState<DocumentMetadata[]>([]);
  const [primaryDocId, setPrimaryDocId] = useState<string>("");
  const [selectedDocs, setSelectedDocs] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [showOrganization, setShowOrganization] = useState(false);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await documentService.getDocuments();
        setDocumentsMetadata(response.items);
        
        const docs = response.items.map(doc => ({
          id: doc.id,
          title: doc.title || doc.filename,
          type: doc.fileType || 'Unknown',
          date: new Date(doc.uploadDate).toLocaleDateString()
        }));
        setDocuments(docs);

        if (docs.length > 0) {
          setPrimaryDocId(docs[0].id);
        }
      } catch (err) {
        console.error("Error fetching documents:", err);
        setError("Failed to load documents. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const handlePrimaryDocChange = (docId: string) => {
    setPrimaryDocId(docId);

    // If the primary doc was previously selected as a related doc, remove it
    if (selectedDocs.includes(docId)) {
      setSelectedDocs(selectedDocs.filter((id) => id !== docId));
    }
  };

  const handleRelatedDocChange = (docId: string, checked: boolean) => {
    if (checked) {
      // Don't allow selecting the primary doc as a related doc
      if (docId !== primaryDocId) {
        setSelectedDocs([...selectedDocs, docId]);
      }
    } else {
      setSelectedDocs(selectedDocs.filter((id) => id !== docId));
    }
  };

  const handleCompare = () => {
    if (!primaryDocId || selectedDocs.length === 0) return;

    router.push(
      `/document-comparison?primaryDoc=${primaryDocId}&relatedDocs=${selectedDocs.join(
        ","
      )}`
    );
  };

  const getSelectedDocumentMetadata = () => {
    if (!primaryDocId) return null;
    return documentsMetadata.find(doc => doc.id === primaryDocId) || null;
  };

  const filteredDocuments = documents.filter(
    (doc) =>
      doc.title?.toLowerCase().includes(searchQuery?.toLowerCase()) ||
      doc.type?.toLowerCase().includes(searchQuery?.toLowerCase())
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Compare Documents</CardTitle>
            <CardDescription>
              Select documents to compare and analyze
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setShowOrganization(!showOrganization)}
            className="flex items-center"
          >
            <TagIcon className="h-4 w-4 mr-2" />
            {showOrganization ? "Hide Organization" : "Organize Documents"}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search documents..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {error ? (
          <div className="flex items-center justify-center py-8 text-destructive">
            <p>{error}</p>
          </div>
        ) : isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <p>Loading documents...</p>
          </div>
        ) : (
          <div className="space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-3">
                Select Primary Document
              </h3>
              <RadioGroup
                value={primaryDocId}
                onValueChange={handlePrimaryDocChange}
              >
                <ScrollArea className="h-[200px] pr-4">
                  <div className="space-y-2">
                    {filteredDocuments.map((doc) => (
                      <div
                        key={`primary-${doc.id}`}
                        className="flex items-center space-x-2 p-2 rounded-md hover:bg-secondary/50"
                      >
                        <RadioGroupItem
                          value={doc.id}
                          id={`primary-${doc.id}`}
                        />
                        <Label
                          htmlFor={`primary-${doc.id}`}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{doc.title}</span>
                          </div>
                          <div className="text-xs text-muted-foreground mt-1 flex justify-between">
                            <span>{doc.type}</span>
                            <span>{doc.date}</span>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </RadioGroup>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">
                Select Related Documents
              </h3>
              <ScrollArea className="h-[200px] pr-4">
                <div className="space-y-2">
                  {filteredDocuments
                    .filter((doc) => doc.id !== primaryDocId)
                    .map((doc) => (
                      <div
                        key={`related-${doc.id}`}
                        className="flex items-center space-x-2 p-2 rounded-md hover:bg-secondary/50"
                      >
                        <Checkbox
                          id={`related-${doc.id}`}
                          checked={selectedDocs.includes(doc.id)}
                          onCheckedChange={(checked) =>
                            handleRelatedDocChange(doc.id, checked === true)
                          }
                        />
                        <Label
                          htmlFor={`related-${doc.id}`}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{doc.title}</span>
                          </div>
                          <div className="text-xs text-muted-foreground mt-1 flex justify-between">
                            <span>{doc.type}</span>
                            <span>{doc.date}</span>
                          </div>
                        </Label>
                      </div>
                    ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}
        {showOrganization && (
          <DocumentOrganizationPanel document={getSelectedDocumentMetadata()} />
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Cancel</Button>
        <Button
          onClick={handleCompare}
          disabled={!primaryDocId || selectedDocs.length === 0}
        >
          Compare Documents
        </Button>
      </CardFooter>
    </Card>
  );
}
