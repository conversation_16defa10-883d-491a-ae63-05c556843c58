"use client";

import Link from "next/link";
import Image from "next/image";
import { <PERSON>u, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/app/theme-toggle";
import { useUIContext } from "@/lib/ui/ui-context"; // Import the new UIContext
import { useAuth } from "@/lib/auth/auth-context";

export function GlobalHeader() {
  const { setIsMobileSidebarOpen } = useUIContext();
  const { logout } = useAuth();

  return (
    <header className="sticky top-0 z-30 flex h-14 shrink-0 items-center justify-between border-b bg-background px-4 sm:px-6 overflow-hidden">
      <div className="flex items-center gap-2 sm:gap-4">
        <Button
          variant="ghost"
          size="icon"
          className="sm:hidden" // Hidden on sm and larger screens
          onClick={() => setIsMobileSidebarOpen(true)}
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Main Menu</span>
        </Button>
        <Link href="/dashboard" className="flex items-center h-full">
          <div className="relative w-32 h-8 md:w-40 md:h-10">
            <Image
              src="/logos/light-512.png"
              alt="Docgic Logo Light"
              fill
              sizes="(max-width: 768px) 8rem, 10rem"
              className="object-contain block dark:hidden"
              priority
            />
            <Image
              src="/logos/trans-512.png"
              alt="Docgic Logo Dark"
              fill
              sizes="(max-width: 768px) 8rem, 10rem"
              className="object-contain hidden dark:block"
              priority
            />
          </div>
        </Link>
        {/* Primary navigation links can be added here if needed later */}
      </div>
      
      {/* Central Title (Optional - can be context-specific in page layouts) */}
      {/* <div className="text-sm font-medium hidden md:block">Application Title</div> */}

      <div className="flex items-center gap-4">
        <ThemeToggle />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="overflow-hidden rounded-full">
              <User className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile">Profile</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/subscription">Subscription</Link>
            </DropdownMenuItem>
            {/* Add Feedback link if it's to be kept in user menu */}
            <DropdownMenuItem asChild>
              <Link href="/feedback">Feedback</Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout} className="text-red-600 cursor-pointer">
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
