"use client";

import { useEffect, useState } from 'react';
import Link from "next/link";
import Image from "next/image";
import { <PERSON>u, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/app/theme-toggle";
import { useResearchContext } from "@/lib/legal-research/research-context";
import { useAuth } from "@/lib/auth/auth-context";

export function LegalResearchHeader() {
  const { setIsMobileSidebarOpen } = useResearchContext();
  const { logout } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [isDark, setIsDark] = useState(false);

  // Set mounted to true once component mounts on client-side
  useEffect(() => {
    setMounted(true);
    // Check for dark mode preference
    const isDarkMode = document.documentElement.classList.contains('dark');
    setIsDark(isDarkMode);
  }, []);

  // Don't render anything on the server
  if (!mounted) {
    return (
      <header className="sticky top-0 z-30 flex h-14 shrink-0 items-center justify-between border-b bg-background px-4 sm:px-6 overflow-hidden">
        <div className="flex items-center gap-2 sm:gap-4">
          <Button variant="ghost" size="icon" className="sm:hidden opacity-0">
            <Menu className="h-5 w-5" />
          </Button>
          <div className="w-32 h-8 md:w-40 md:h-10 bg-muted rounded animate-pulse" />
        </div>
      </header>
    );
  }

  return (
    <header className="sticky top-0 z-30 flex h-14 shrink-0 items-center justify-between border-b bg-background px-4 sm:px-6 overflow-hidden">
      <div className="flex items-center gap-2 sm:gap-4">
        <Button
          variant="ghost"
          size="icon"
          className="sm:hidden"
          onClick={() => setIsMobileSidebarOpen(true)}
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
        <Link href="/" className="flex items-center h-full">
          <div className="relative w-32 h-8 md:w-40 md:h-10">
            <Image
              src="/logos/light-512.png"
              alt="Docgic Logo"
              fill
              sizes="(max-width: 768px) 8rem, 10rem"
              className={`object-contain transition-opacity duration-200 ${!isDark ? 'opacity-100' : 'opacity-0'}`}
              priority
            />
            <Image
              src="/logos/trans-512.png"
              alt="Docgic Logo"
              fill
              sizes="(max-width: 768px) 8rem, 10rem"
              className={`object-contain transition-opacity duration-200 ${isDark ? 'opacity-100' : 'opacity-0'}`}
              priority
            />
          </div>
        </Link>
      </div>
      
      <div className="text-sm font-medium hidden md:block">Legal Research</div>
      
      <div className="flex items-center gap-4">
        <ThemeToggle />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="overflow-hidden rounded-full">
              <User className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile">Profile</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/subscription">Subscription</Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout} className="text-red-600 cursor-pointer">
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
