"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useMediaQuery } from "@/hooks/use-media-query"
import { ResizablePanel } from "@/components/ui/resizable-panel"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import { ResearchMessageComponent } from "./research-message"
import { ResearchInput } from "./research-input"
import { useResearchContext } from "@/lib/legal-research/research-context"
import {
  Loader2,
  PlusCircle,
  Search,
  MessageSquare,
  Scale,
  ChevronDown,
  ChevronRight,
  Menu,
  Trash2,
  MoreHorizontal,
} from "lucide-react"
import {
  getStandaloneRoutesWithActive,
  getRouteCategoriesWithActive,
  ROUTE_CATEGORIES,
} from "@/lib/navigation/navigation-config"
import { format } from "date-fns"
import { useRouter, usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { useResearchSessions, useDeleteResearchSession } from "@/hooks/use-legal-research"
import { LegalResearchHeader } from "./legal-research-header"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useQueryClient } from "@tanstack/react-query"

export function ResearchChatContainer() {
  const {
    currentSession,
    messages,
    isLoading,
    createSession,
    loadSession,
    clearSession,
    askFollowUp,
    isMobileSidebarOpen,
    setIsMobileSidebarOpen,
    sendResearchQuery,
  } = useResearchContext()

  const { data: sessionsData, isLoading: loadingSessions } = useResearchSessions({
    limit: 20,
  })
  
  const deleteSessionMutation = useDeleteResearchSession()

  const [showWelcome, setShowWelcome] = useState(true)
  const [isLoadingSession, setIsLoadingSession] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Navigation state
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    "your documents": false,
    "ai that actually works": false,
    "real legal tools": false,
  })

  const toggleCategory = (category: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }))
  }

  useEffect(() => {
    if (currentSession) {
      setShowWelcome(false)
    }
    // Don't automatically show welcome when session is cleared - let the New button handle it
  }, [currentSession])

  const handleNewButtonClick = () => {
    // Navigate to a dedicated route to handle clearing the session state
    router.push("/legal-research/new")
  }

  const handleLoadSession = async (sessionId: string) => {
    try {
      setIsLoadingSession(true)
      // Load session first to prevent snapping
      await loadSession(sessionId)
      await router.push(`/legal-research/sessions/${sessionId}`)
    } catch (error) {
      console.error("Failed to load session:", error)
    } finally {
      setIsLoadingSession(false)
    }
  }

  const handleFollowUpClick = async (question: string) => {
    try {
      await askFollowUp(question)
    } catch (error) {
      console.error("Failed to ask follow-up:", error)
    }
  }

  const handleDeleteSession = async (sessionId: string) => {
    try {
      await deleteSessionMutation.mutateAsync(sessionId)
      
      // If we're currently viewing the deleted session, redirect to main page
      if (currentSession?.sessionId === sessionId) {
        clearSession()
        router.push("/legal-research")
      }
    } catch (error) {
      console.error("Failed to delete session:", error)
    }
  }

  const researchSessions = sessionsData?.data.sessions || []

  const sessionSidebar = (
    <div className="h-full flex flex-col bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-r border-border/40">
      <div className="flex-1 flex flex-col min-h-0 pt-14 md:pt-0">
          {/* Main Navigation Section - Independently Scrollable */}
          <div className="flex-1 min-h-0 max-h-[60vh] border-b border-border/40">
            <ScrollArea className="h-full px-3">
            <nav className="space-y-1 px-1 pr-3">
              <div className="space-y-1">
                  {getStandaloneRoutesWithActive(pathname).map((route) => (
                <Link
                      key={route.href}
                      href={route.href}
                  className={cn(
                    "flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
                        route.active
                      ? "bg-primary/10 text-primary border border-primary/20"
                      : "text-muted-foreground hover:bg-muted/50 hover:text-foreground",
                  )}
                >
                      <route.icon className="h-4 w-4 mr-3" />
                      {route.label}
                </Link>
                  ))}
              </div>
              <div className="my-4 border-t border-border/40"></div>
              <div className="space-y-2">
                  {getRouteCategoriesWithActive(pathname, ROUTE_CATEGORIES).map((category) => (
                    <Collapsible 
                      key={category.title}
                      open={expandedCategories[category.title.toLowerCase()]} 
                      onOpenChange={() => toggleCategory(category.title.toLowerCase())} 
                      className="w-full"
                    >
                  <CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2.5 text-sm font-medium hover:bg-muted/50 text-muted-foreground transition-all duration-200">
                    <div className="flex items-center gap-3">
                          <category.icon className="h-4 w-4" />
                          <span>{category.title}</span>
                    </div>
                        {expandedCategories[category.title.toLowerCase()] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pl-7 space-y-1 mt-1">
                        {category.routes.map((route) => (
                    <Link
                            key={route.href}
                            href={route.href}
                      className="flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted/50 text-muted-foreground hover:text-foreground"
                    >
                            {route.label}
                    </Link>
                        ))}
                  </CollapsibleContent>
                </Collapsible>
                  ))}
              </div>
            </nav>
          </ScrollArea>
        </div>

          {/* Recent Research Section - Independently Scrollable */}
          <div className="flex-1 flex flex-col min-h-0">
            {/* Recent Research Header */}
            <div className="flex items-center justify-between px-6 py-3 border-b border-border/40 flex-shrink-0">
            <div className="flex items-center text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              <Search className="h-3.5 w-3.5 mr-2" />
              <span>Recent Research</span>
            </div>
            <Button variant="ghost" size="sm" onClick={handleNewButtonClick} className="h-7">
              <PlusCircle className="w-3.5 h-3.5 mr-1" />
              New
            </Button>
          </div>
            
            {/* Scrollable Research Sessions */}
            <div className="flex-1 min-h-0">
          {loadingSessions ? (
                <div className="px-6 py-3 space-y-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-full rounded-md" />
              ))}
            </div>
          ) : (
                <ScrollArea className="h-full">
                  <div className="space-y-1 px-3 py-2">
                {researchSessions.map((session) => (
                  <SessionHistoryItem
                    key={session.sessionId}
                    session={session}
                    isActive={currentSession?.sessionId === session.sessionId}
                    onClick={() => handleLoadSession(session.sessionId)}
                    onDelete={() => handleDeleteSession(session.sessionId)}
                    isDeleting={deleteSessionMutation.isPending}
                    isLoadingSession={isLoadingSession}
                  />
                ))}
              </div>
            </ScrollArea>
          )}
            </div>
        </div>
      </div>
    </div>
  )

  const chatWindowAndInput = (
    <div className="h-full w-full flex flex-col bg-gradient-to-b from-background to-background/95">
      <div className="flex-1 min-h-0 overflow-y-auto">
        {isLoadingSession ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4 mx-auto">
                <Search className="w-6 h-6 text-primary animate-spin" />
              </div>
              <p className="text-sm text-muted-foreground">Loading session...</p>
            </div>
          </div>
        ) : (
          <ResearchChatWindow
            showWelcome={showWelcome}
            onFollowUpClick={handleFollowUpClick}
          />
        )}
      </div>
      {!showWelcome && !isLoadingSession && !isLoading && (
        <div className="shrink-0">
          <ResearchInput />
        </div>
      )}
    </div>
  )

  const isMobile = useMediaQuery("(max-width: 768px)")

  return (
    <div className="h-full bg-background text-foreground">
      {isMobile ? (
        <div className="flex h-full flex-col">
          <Sheet open={isMobileSidebarOpen} onOpenChange={setIsMobileSidebarOpen}>
            <SheetContent side="left" className="p-0 w-80 max-w-[85vw]">
              {sessionSidebar}
            </SheetContent>
          </Sheet>
          <LegalResearchHeader />
          <div className="flex-1 min-h-0">{chatWindowAndInput}</div>
        </div>
      ) : (
        <ResizablePanel
          leftPanel={sessionSidebar}
          rightPanel={chatWindowAndInput}
          initialLeftWidth={320}
          minLeftWidth={280}
          maxLeftWidth={480}
        />
      )}
    </div>
  )
}

interface SessionHistoryItemProps {
  session: any
  isActive: boolean
  onClick: () => void
  onDelete: () => void
  isDeleting: boolean
  isLoadingSession: boolean
}

function SessionHistoryItem({ session, isActive, onClick, onDelete, isDeleting, isLoadingSession }: SessionHistoryItemProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowDeleteDialog(true)
  }

  const handleConfirmDelete = () => {
    onDelete()
    setShowDeleteDialog(false)
  }

  return (
    <>
      <div
        className={cn(
          "group flex items-center w-[280px] px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
          isActive
            ? "bg-primary/10 text-primary"
            : "text-muted-foreground hover:bg-muted/50 hover:text-foreground",
        )}
      >
        <button
          onClick={onClick}
          className="flex-1 text-left min-w-0 pr-2"
          disabled={isDeleting || isLoadingSession}
        >
          <span className="block truncate">
            {session.title || "Untitled Session"}
          </span>
        </button>
        
        <div className="flex-shrink-0">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-destructive/10 opacity-0 group-hover:opacity-100 transition-opacity"
                disabled={isDeleting}
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              <DropdownMenuItem
                onClick={handleDeleteClick}
                className="text-destructive focus:text-destructive focus:bg-destructive/10"
                disabled={isDeleting}
              >
                <Trash2 className="h-3 w-3 mr-2" />
                {isDeleting ? "Deleting..." : "Delete"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Research Session</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{session.title || "Untitled Session"}"? 
              This action cannot be undone and will permanently remove all research queries and results from this session.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Session
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

interface ResearchChatWindowProps {
  showWelcome: boolean
  onFollowUpClick: (question: string) => void
}

function ResearchChatWindow({ showWelcome, onFollowUpClick }: ResearchChatWindowProps) {
  const { messages, isLoading, isSearching, currentSession } = useResearchContext()

  if (showWelcome) {
    return <WelcomeScreen />
  }

  // Show loading state when we have a session but no messages yet (session is loading)
  if (currentSession && messages.length === 0 && isLoading) {
    return (
      <ScrollArea className="h-full" style={{ display: "block" }}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="space-y-8 py-4">
            {/* Simulate loading messages with skeletons */}
            {[...Array(2)].map((_, i) => (
              <div key={i} className="flex items-start space-x-4">
                <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
                <div className="space-y-3 flex-1">
                  <Skeleton className="h-4 w-full max-w-[400px]" />
                  <Skeleton className="h-4 w-full max-w-[350px]" />
                  <Skeleton className="h-20 w-full rounded-lg" />
                  <div className="flex space-x-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </ScrollArea>
    )
  }

  return (
    <ScrollArea className="h-full" style={{ display: "block" }}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6">
              <Search className="w-8 h-8 text-primary" />
            </div>
            <h2 className="text-2xl sm:text-3xl font-semibold mb-4 text-foreground">Ready for legal research</h2>
            <p className="text-base sm:text-lg text-muted-foreground max-w-md">
              Ask any legal question to get comprehensive research with sources and AI analysis.
            </p>
          </div>
        ) : (
          <div className="space-y-8 py-4">
            {[...messages].reverse().map((message, index) => (
              <ResearchMessageComponent
                key={message.id}
                message={message}
                isLastMessage={index === messages.length - 1}
                onFollowUpClick={onFollowUpClick}
              />
            ))}
            {/* Show inline loading indicator when processing new query */}
            {isSearching && (
              <div className="flex items-start space-x-4 animate-pulse">
                <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0">
                  <Search className="w-4 h-4 text-primary animate-spin" />
                </div>
                <div className="space-y-3 flex-1">
                  <div className="flex space-x-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <Skeleton className="h-20 w-full rounded-lg" />
                  <div className="flex space-x-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </ScrollArea>
  )
}

function WelcomeScreen() {
  const [isCreatingSession, setIsCreatingSession] = useState(false)
  const suggestedQuestions = [
    "What are the latest developments in AI copyright law?",
    "Summarize the key arguments in *Chevron v. Natural Resources Defense Council*.",
    "Draft a standard non-disclosure agreement clause for a software development project.",
    "Compare the data privacy regulations in California (CCPA/CPRA) and Europe (GDPR).",
  ]

  const { sendResearchQuery, createSession } = useResearchContext()
  const router = useRouter()
  const queryClient = useQueryClient()

  const handleQuestionClick = async (question: string) => {
    if (isCreatingSession) return
    
    try {
      setIsCreatingSession(true)
      
      // Send the query directly - session will be auto-created
      await sendResearchQuery(
        question,
        {
          includeSynthesis: true,
        },
        undefined,
        async (newSession) => {
          // Navigate to the new session
          await router.push(`/legal-research/sessions/${newSession.sessionId}`)
          // Invalidate sessions to refresh the sidebar
          await queryClient.invalidateQueries({ queryKey: ["legal-research", "sessions"] })
        }
      )
      
    } catch (error) {
      console.error("Failed to start research with question:", error)
    } finally {
      setIsCreatingSession(false)
    }
  }

  return (
    <div className="h-full flex items-center justify-center">
      <div className="text-center max-w-2xl mx-auto px-4 my-6">
        <h1 className="text-3xl sm:text-4xl font-bold my-4 text-foreground">Docgic Legal Research</h1>
        <p className="text-base sm:text-lg text-muted-foreground mb-10">
          Your AI-powered assistant for comprehensive legal analysis.
        </p>
        <div className="space-y-6">
          {/* Research Input */}
          <div className="w-full max-w-2xl">
            <ResearchInput 
              placeholder="Ask any legal research question to get started..."
              disabled={isCreatingSession}
            />
          </div>

          <div className="mt-10 pt-8 border-t border-border/40 w-full">
            <h3 className="text-lg font-semibold mb-4 text-foreground">Suggested Questions</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {suggestedQuestions.map((q, i) => (
                <Card
                  key={i}
                  className={`p-4 text-left transition-colors ${
                    isCreatingSession 
                      ? 'opacity-50 cursor-not-allowed' 
                      : 'hover:bg-muted/50 cursor-pointer'
                  }`}
                  onClick={() => !isCreatingSession && handleQuestionClick(q)}
                >
                  <p className="text-sm font-medium">{q}</p>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
