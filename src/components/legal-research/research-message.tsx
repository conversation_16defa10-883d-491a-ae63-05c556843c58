"use client"

import type React from "react"

import { useState, useCallback, useMemo, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  User,
  BookOpen,
  Scale,
  FileText,
  Newspaper,
  CreditCard,
  Copy,
  Clock,
  Sparkles,
  TrendingUp,
  Plus,
  ExternalLink,
  X,
} from "lucide-react"
import { cn } from "@/lib/utils"
import type { SearchResult, ResearchMessage } from "@/lib/types/legal-research"

interface ResearchMessageProps {
  message: ResearchMessage
  isLastMessage?: boolean
  onFollowUpClick?: (question: string) => void
  onSourceClick?: (sourceId: string) => void
}

export function ResearchMessageComponent({
  message,
  isLastMessage = false,
  onFollowUpClick,
  onSourceClick,
}: ResearchMessageProps) {
  const [activeTab, setActiveTab] = useState("analysis")
  const [copiedText, setCopiedText] = useState<string | null>(null)

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText("copied")
      setTimeout(() => setCopiedText(null), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }, [])

  if (message.role === "system") {
    return null
  }

  if (message.role === "user") {
    return <UserMessage message={message} />
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <AssistantMessage
        message={message}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        onSourceClick={onSourceClick}
        onFollowUpClick={onFollowUpClick}
        onCopyText={copyToClipboard}
        copiedText={copiedText}
      />
    </div>
  )
}

function UserMessage({ message }: { message: ResearchMessage }) {
  return (
    <div className="flex items-start space-x-3 sm:space-x-4 justify-end">
      <div className="max-w-[90%] sm:max-w-[85%] md:max-w-[70%]">
        <div className="rounded-2xl rounded-tr-md px-3 py-2 sm:px-4 sm:py-3 shadow-sm overflow-hidden break-words bg-muted">
          <p className="text-sm leading-relaxed">{message.content}</p>
          {message.queryType && (
            <div className="mt-2 flex items-center space-x-2 text-xs text-muted-foreground">
              {message.queryType === "research" ? "Research Query" : "Follow-up"}
            </div>
          )}
        </div>
      </div>
      <div className="flex-shrink-0">
        <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-primary/10 flex items-center justify-center">
          <User className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
        </div>
      </div>
    </div>
  )
}

interface AssistantMessageProps {
  message: ResearchMessage
  activeTab: string
  onTabChange: (tab: string) => void
  onSourceClick?: (sourceId: string) => void
  onFollowUpClick?: (question: string) => void
  onCopyText: (text: string) => void
  copiedText: string | null
}

function AssistantMessage({
  message,
  activeTab,
  onTabChange,
  onSourceClick,
  onFollowUpClick,
  onCopyText,
  copiedText,
}: AssistantMessageProps) {
  const sourceCount = message.searchResults?.sources?.length || 0

  return (
    <div className="flex items-start space-x-3 sm:space-x-4">
      <div className="flex-shrink-0">
        <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-primary/10 flex items-center justify-center text-xs">
          DR
        </div>
      </div>
      <div className="flex-1 space-y-4 sm:space-y-6 max-w-none min-w-0">
        <div className="grid grid-cols-3 gap-2">
          <Button
            variant={activeTab === 'analysis' ? 'secondary' : 'ghost'}
            onClick={() => onTabChange('analysis')}
            className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3"
          >
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden sm:inline">Docgic Research</span>
            <span className="sm:hidden">Analysis</span>
          </Button>
          <Button
            variant={activeTab === 'sources' ? 'secondary' : 'ghost'}
            onClick={() => onTabChange('sources')}
            className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3"
          >
            <BookOpen className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>Sources</span>
            {sourceCount > 0 && (
              <Badge variant="default" className="ml-1 text-xs h-4 px-1">
                {sourceCount}
              </Badge>
            )}
          </Button>
          <Button
            variant={activeTab === 'steps' ? 'secondary' : 'ghost'}
            onClick={() => onTabChange('steps')}
            className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm px-2 sm:px-3"
          >
            <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
            <span>Steps</span>
          </Button>
        </div>

        <div className="mt-4 sm:mt-6">
          {activeTab === 'analysis' && (
            <AnalysisView
              message={message}
              onSourceClick={onSourceClick}
              onFollowUpClick={onFollowUpClick}
              onCopyText={onCopyText}
              copiedText={copiedText}
            />
          )}
          {activeTab === 'sources' && (
            <SourcesView searchResults={message.searchResults} onSourceClick={onSourceClick} />
          )}
          {activeTab === 'steps' && <StepsView message={message} />}
        </div>

        {/* Mobile Action Buttons */}
        <div className="sm:hidden">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCopyText(message.aiSynthesis?.legalAnalysis?.text || "")}
            className="w-full h-9 px-3 text-xs justify-start"
          >
            <Copy className="w-3 h-3 mr-2" />
            Copy
          </Button>
        </div>

        {/* Desktop Action Buttons */}
        <div className="hidden sm:flex items-center justify-end pt-4 border-t border-gray-200 dark:border-gray-800">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCopyText(message.aiSynthesis?.legalAnalysis?.text || "")}
            className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <Copy className="w-3 h-3" />
          </Button>
        </div>

        {/* Follow-up Questions */}
        {message.followUpSuggestions && message.followUpSuggestions.length > 0 && (
          <FollowUpSection suggestions={message.followUpSuggestions} onFollowUpClick={onFollowUpClick} />
        )}

        {/* Credits and Metadata */}
        {(message.creditsUsed || message.timestamp) && (
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-xs text-muted-foreground pt-2 border-t border-border/20 space-y-2 sm:space-y-0">
            <div className="flex items-center space-x-3 sm:space-x-4">
              {message.creditsUsed && (
                <div className="flex items-center space-x-1">
                  <CreditCard className="w-3 h-3" />
                  <span>Credits: {message.creditsUsed}</span>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function AnalysisView({
  message,
  onSourceClick,
  onFollowUpClick,
  onCopyText,
  copiedText,
}: {
  message: ResearchMessage
  onSourceClick?: (sourceId: string) => void
  onFollowUpClick?: (question: string) => void
  onCopyText: (text: string) => void
  copiedText: string | null
}) {
  // Create a mapping of sources for citation references
  const sourceMap = useMemo(() => {
    if (!message.searchResults?.sources) return new Map()
    const map = new Map()
    message.searchResults.sources.forEach((source, index) => {
      map.set(source.id, index + 1)
    })
    return map
  }, [message.searchResults?.sources])

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Source Cards - Responsive Layout */}
      {message.searchResults && message.searchResults.sources.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {message.searchResults.sources.slice(0, 4).map((source, index) => (
            <SourceCard
              key={source.id}
              source={source}
              index={index + 1}
              onClick={() => onSourceClick?.(source.id)}
              compact
            />
          ))}
        </div>
      )}

      {/* Main Analysis Content */}
      {message.aiSynthesis && (
        <div className="prose prose-gray dark:prose-invert max-w-none prose-sm sm:prose-base">
          {/* Executive Summary */}
          {message.aiSynthesis.legalAnalysis?.text && (
            <div className="space-y-3 sm:space-y-4">
              <div className="leading-relaxed text-sm sm:text-base break-words">
                <CitationText
                  text={message.aiSynthesis.legalAnalysis.text}
                  sourceUrls={message.aiSynthesis.legalAnalysis.sourceUrls}
                  sourceMap={sourceMap}
                  sources={message.searchResults?.sources || []}
                  onCitationClick={onSourceClick}
                  onFollowUpClick={onFollowUpClick}
                />
              </div>
            </div>
          )}

          {/* Key Findings */}
          {message.aiSynthesis.keyFindings && message.aiSynthesis.keyFindings.length > 0 && (
            <div className="mt-6 sm:mt-8">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 sm:mb-4">
                Key Legal Findings
              </h3>
              <div className="space-y-3 sm:space-y-4">
                {message.aiSynthesis.keyFindings.map((finding, index) => (
                  <div key={index} className="border-l-4 pl-3 sm:pl-4">
                    <div className="leading-relaxed text-sm sm:text-base break-words">
                      <CitationText
                        text={finding.finding}
                        sourceUrls={finding.sourceUrls}
                        sourceMap={sourceMap}
                        sources={message.searchResults?.sources || []}
                        onCitationClick={onSourceClick}
                        onFollowUpClick={onFollowUpClick}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Practice Implications */}
          {message.aiSynthesis.practiceImplications && message.aiSynthesis.practiceImplications.length > 0 && (
            <div className="mt-6 sm:mt-8">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 sm:mb-4">
                Practice Implications
              </h3>
              <div className="space-y-2 sm:space-y-3">
                {message.aiSynthesis.practiceImplications.map((implication, index) => (
                  <div key={index} className="flex items-start space-x-2 sm:space-x-3 ">
                    <div className="flex-shrink-0 mt-2 sm:mt-3">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    </div>
                    <p className="leading-relaxed text-sm sm:text-base break-words m-0">
                      {implication}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Jurisdictional Analysis */}
          {message.aiSynthesis.jurisdictionalNotes && (
            <div className="mt-6 sm:mt-8">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 sm:mb-4">
                Jurisdictional Analysis
              </h3>
              <p className="leading-relaxed text-sm sm:text-base break-words">
                {message.aiSynthesis.jurisdictionalNotes}
              </p>
            </div>
          )}

          {/* Recent Developments */}
          {message.aiSynthesis.recentDevelopments && (
            <div className="mt-6 sm:mt-8">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 sm:mb-4">
                Recent Developments
              </h3>
              <p className="leading-relaxed text-sm sm:text-base break-words">
                {message.aiSynthesis.recentDevelopments}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

function SourcesView({
  searchResults,
  onSourceClick,
}: {
  searchResults?: { totalSources: number; sources: SearchResult[] }
  onSourceClick?: (sourceId: string) => void
}) {
  if (!searchResults || !searchResults.sources.length) {
    return (
      <div className="text-center py-6 sm:py-8">
        <BookOpen className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-500 dark:text-gray-400">No sources available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {searchResults.sources.map((source, index) => (
        <SourceCard
          key={source.id}
          source={source}
          index={index + 1}
          onClick={() => onSourceClick?.(source.id)}
          detailed
        />
      ))}
    </div>
  )
}

function StepsView({ message }: { message: ResearchMessage }) {
  const steps = [
    {
      title: "Query Analysis",
      description: "Analyzed your legal research question and identified key terms and concepts",
      completed: true,
    },
    {
      title: "Source Discovery",
      description: `Found ${message.searchResults?.totalSources || 0} relevant legal sources across multiple databases`,
      completed: true,
    },
    {
      title: "Content Analysis",
      description: "Analyzed source content for relevance, authority, and legal significance",
      completed: true,
    },
    {
      title: "AI Synthesis",
      description: "Generated comprehensive legal analysis with key findings and implications",
      completed: !!message.aiSynthesis,
    },
  ]

  return (
    <div className="space-y-3 sm:space-y-4">
      {steps.map((step, index) => (
        <div key={index} className="flex items-start space-x-3 sm:space-x-4">
          <div className="flex-shrink-0">
            <div
              className={cn(
                "w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs sm:text-sm font-medium",
                step.completed
                  ? "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300"
                  : "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400",
              )}
            >
              {index + 1}
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm sm:text-base">{step.title}</h4>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 leading-relaxed">
              {step.description}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}

interface SourceCardProps {
  source: SearchResult
  index: number
  onClick?: () => void
  compact?: boolean
  detailed?: boolean
}

function SourceCard({ source, index, onClick, compact = false, detailed = false }: SourceCardProps) {
  const getSourceIcon = (type: string) => {
    switch (type) {
      case "case_law":
        return <Scale className="w-3 h-3 sm:w-4 sm:h-4" />
      case "statute":
      case "regulation":
        return <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
      case "news":
        return <Newspaper className="w-3 h-3 sm:w-4 sm:h-4" />
      default:
        return <BookOpen className="w-3 h-3 sm:w-4 sm:h-4" />
    }
  }

  const getSourceTypeLabel = (type: string) => {
    switch (type) {
      case "case_law":
        return "Case Law"
      case "statute":
        return "Statute"
      case "regulation":
        return "Regulation"
      case "news":
        return "News"
      case "article":
        return "Article"
      default:
        return "Legal Resource"
    }
  }

  if (compact) {
    return (
      <div
        className="rounded-lg p-3 sm:p-4 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors active:bg-gray-200 dark:active:bg-gray-700"
        onClick={onClick}
      >
        <div className="flex items-start space-x-2 sm:space-x-3">
          <div className="flex-shrink-0">
            <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 flex items-center justify-center">
              {getSourceIcon(source.type)}
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2 leading-tight break-all">
              {source.title}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
              {getSourceTypeLabel(source.type)} • {new URL(source.url).hostname}
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (detailed) {
    return (
      <div className="rounded-lg p-4 sm:p-6 hover:shadow-md transition-all duration-200 active:shadow-lg">
        <div className="flex items-start space-x-3 sm:space-x-4">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 flex items-center justify-center">
              {getSourceIcon(source.type)}
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{index}.</span>
              <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                {getSourceTypeLabel(source.type)}
              </span>
            </div>
            <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-2 truncate">
              {new URL(source.url).hostname}
            </p>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 leading-tight text-sm sm:text-base">
              <a
                href={source.url}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors active:text-blue-700 dark:active:text-blue-300"
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(source.url, "_blank", "noopener,noreferrer")
                }}
              >
                <p className="text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2 leading-tight break-all">
                  {source.title}
                </p>
              </a>
            </h3>
            {source.snippet && (
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-relaxed line-clamp-3 sm:line-clamp-none break-words">
                {source.snippet}
              </p>
            )}
            {source.citation && Array.isArray(source.citation) && source.citation.length > 0 && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 font-mono bg-gray-50 dark:bg-gray-900/50 px-2 py-1 rounded line-clamp-2">
                {source.citation.join(", ")}
              </p>
            )}
          </div>
          {/* Placeholder for thumbnail - smaller on mobile */}
          <div className="flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 dark:bg-gray-800 rounded-lg"></div>
        </div>
      </div>
    )
  }

  return null
}

function FollowUpSection({
  suggestions,
  onFollowUpClick,
}: {
  suggestions: string[]
  onFollowUpClick?: (question: string) => void
}) {
  return (
    <div className="border-t pt-4 sm:pt-6">
      <div className="flex items-center space-x-2 mb-3 sm:mb-4">
        <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 dark:text-gray-400" />
        <h4 className="font-semibold text-sm text-gray-900 dark:text-gray-100">Related</h4>
      </div>
      <div className="space-y-2">
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            onClick={() => onFollowUpClick?.(suggestion)}
            className="w-full text-left p-3 rounded-lg border transition-colors group"
          >
            <div className="flex items-start justify-between space-x-3">
              <span className="text-xs sm:text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 leading-relaxed">
                {suggestion}
              </span>
              <Plus className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 flex-shrink-0 mt-0.5" />
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

// Replace the existing formatTextWithCitations function with this improved version
function formatTextWithCitations(
  text: string,
  sourceUrls?: string[],
  sourceMap?: Map<string, number>,
  sources?: SearchResult[],
  onCitationClick?: (sourceId: string) => void,
): { formattedText: string; citations: Array<{ number: number; source: SearchResult }> } {
  if (!sourceUrls || !sourceMap || !sources) {
    return { formattedText: text, citations: [] }
  }

  let formattedText = text
  const citations: Array<{ number: number; source: SearchResult }> = []

  // Add citations at the end of relevant sentences
  sourceUrls.forEach((url) => {
    const source = sources.find((s) => s.url === url)
    if (source) {
      const citationNumber = sourceMap.get(source.id)
      if (citationNumber) {
        citations.push({ number: citationNumber, source })
        formattedText += ` [${citationNumber}]`
      }
    }
  })

  return { formattedText, citations }
}

// Mobile-optimized Citation Preview Component
function CitationPreview({
  citation,
  position,
  onClose,
}: {
  citation: { number: number; source: SearchResult }
  position: { x: number; y: number }
  onClose: () => void
}) {
  const [adjustedPosition, setAdjustedPosition] = useState(position)
  const previewRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (previewRef.current) {
      const rect = previewRef.current.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      let newX = position.x
      let newY = position.y - 10

      // Adjust horizontal position if preview goes off screen
      if (newX + rect.width > viewportWidth - 16) {
        newX = viewportWidth - rect.width - 16
      }
      if (newX < 16) {
        newX = 16
      }

      // Adjust vertical position if preview goes off screen
      if (newY < 16) {
        newY = position.y + 30 // Show below instead of above
      }

      setAdjustedPosition({ x: newX, y: newY })
    }
  }, [position])

  const getSourceIcon = (type: string) => {
    switch (type) {
      case "case_law":
        return <Scale className="w-3 h-3 sm:w-4 sm:h-4" />
      case "statute":
      case "regulation":
        return <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
      case "news":
        return <Newspaper className="w-3 h-3 sm:w-4 sm:h-4" />
      default:
        return <BookOpen className="w-3 h-3 sm:w-4 sm:h-4" />
    }
  }

  const getSourceTypeLabel = (type: string) => {
    switch (type) {
      case "case_law":
        return "Case Law"
      case "statute":
        return "Statute"
      case "regulation":
        return "Regulation"
      case "news":
        return "News"
      case "article":
        return "Article"
      default:
        return "Legal Resource"
    }
  }

  return (
    <>
      {/* Mobile backdrop */}
      <div className="fixed inset-0 z-40 bg-black/20 sm:hidden" onClick={onClose} />

      <div
        ref={previewRef}
        className="fixed z-50 bg-background dark:bg-background border rounded-lg shadow-lg max-w-[calc(100vw-2rem)] sm:max-w-sm"
        style={{
          left: adjustedPosition.x,
          top: adjustedPosition.y,
          transform: window.innerWidth < 640 ? "none" : "translateY(-100%)",
        }}
      >
        {/* Mobile close button */}
        <div className="sm:hidden flex justify-end p-2">
          <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
            <X className="w-4 h-4" />
          </button>
        </div>

        <div className="p-3 sm:p-4">
          <div className="flex items-start space-x-2 sm:space-x-3">
            <div className="flex-shrink-0">
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                {getSourceIcon(citation.source.type)}
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1 sm:mb-2">
                <span className="text-xs font-medium text-gray-900 dark:text-gray-100">[{citation.number}]</span>
                <Badge variant="secondary" className="text-xs h-4 px-1.5">
                  {getSourceTypeLabel(citation.source.type)}
                </Badge>
              </div>
              <h4 className="font-medium text-xs sm:text-sm text-gray-900 dark:text-gray-100 line-clamp-2 mb-2 leading-tight">
                {citation.source.title}
              </h4>
              {citation.source.snippet && (
                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3 mb-2 leading-relaxed">
                  {citation.source.snippet}
                </p>
              )}
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <ExternalLink className="w-3 h-3 mr-1 flex-shrink-0" />
                <span className="truncate">{new URL(citation.source.url).hostname}</span>
              </div>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              <span className="sm:hidden">Tap</span>
              <span className="hidden sm:inline">Click</span> citation to ask follow-up question
            </p>
          </div>
        </div>
      </div>
    </>
  )
}

// Mobile-optimized CitationText component
function CitationText({
  text,
  sourceUrls,
  sourceMap,
  sources,
  onCitationClick,
  onFollowUpClick,
}: {
  text: string
  sourceUrls?: string[]
  sourceMap?: Map<string, number>
  sources?: SearchResult[]
  onCitationClick?: (sourceId: string) => void
  onFollowUpClick?: (question: string) => void
}) {
  const [hoveredCitation, setHoveredCitation] = useState<{
    citation: { number: number; source: SearchResult }
    position: { x: number; y: number }
  } | null>(null)

  const [tappedCitation, setTappedCitation] = useState<{
    citation: { number: number; source: SearchResult }
    position: { x: number; y: number }
  } | null>(null)

  const { formattedText, citations } = formatTextWithCitations(text, sourceUrls, sourceMap, sources, onCitationClick)

  // Split text by citation markers and render with clickable citations
  const parts = formattedText.split(/(\[\d+\])/)

  const handleCitationMouseEnter = (citation: { number: number; source: SearchResult }, event: React.MouseEvent) => {
    // Only show hover on desktop
    if (window.innerWidth >= 640) {
      const rect = (event.target as HTMLElement).getBoundingClientRect()
      setHoveredCitation({
        citation,
        position: {
          x: rect.left + rect.width / 2,
          y: rect.top,
        },
      })
    }
  }

  const handleCitationMouseLeave = () => {
    setHoveredCitation(null)
  }

  const handleCitationClick = (citation: { number: number; source: SearchResult }, event: React.MouseEvent) => {
    event.preventDefault()

    // Generate a follow-up question about the source
    const generateFollowUpQuestion = (source: SearchResult) => {
      const sourceTitle = source.title
      const sourceType = source.type
      
      // Create a contextual follow-up question based on the source
      if (sourceType === "case_law") {
        return `Tell me more about the ${sourceTitle} case and its legal implications.`
      } else if (sourceType === "statute") {
        return `Can you explain the ${sourceTitle} statute in more detail and how it applies?`
      } else if (sourceType === "regulation") {
        return `What are the key provisions of ${sourceTitle} and how do they affect compliance?`
      } else if (sourceType === "news") {
        return `Can you provide more context about this news article "${sourceTitle}" and its legal significance?`
      } else {
        return `Can you provide more details about "${sourceTitle}" and its relevance to this legal analysis?`
      }
    }

    // On mobile, show preview first, then send follow-up on second tap
    if (window.innerWidth < 640) {
      if (tappedCitation?.citation.number === citation.number) {
        // Second tap - send follow-up question
        const followUpQuestion = generateFollowUpQuestion(citation.source)
        onFollowUpClick?.(followUpQuestion)
        setTappedCitation(null)
      } else {
        // First tap - show preview
        const rect = (event.target as HTMLElement).getBoundingClientRect()
        setTappedCitation({
          citation,
          position: {
            x: rect.left + rect.width / 2,
            y: rect.top,
          },
        })
      }
    } else {
      // Desktop - send follow-up question directly
      const followUpQuestion = generateFollowUpQuestion(citation.source)
      onFollowUpClick?.(followUpQuestion)
    }
  }

  const closeCitationPreview = () => {
    setTappedCitation(null)
    setHoveredCitation(null)
  }

  return (
    <span className="relative">
      {parts.map((part, index) => {
        const citationMatch = part.match(/\[(\d+)\]/)
        if (citationMatch) {
          const citationNumber = Number.parseInt(citationMatch[1])
          const citation = citations.find((c) => c.number === citationNumber)

          if (!citation) return <span key={index}>{part}</span>

          return (
            <sup
              key={index}
              className={cn(
                "text-blue-600 dark:text-blue-400 cursor-pointer hover:underline transition-colors ml-0.5 px-1 py-0.5 rounded text-xs sm:text-sm",
                "hover:bg-blue-100 dark:hover:bg-blue-900/20 active:bg-blue-200 dark:active:bg-blue-900/40",
                "touch-manipulation select-none",
                tappedCitation?.citation.number === citation.number && "bg-blue-100 dark:bg-blue-900/20",
              )}
              onMouseEnter={(e) => handleCitationMouseEnter(citation, e)}
              onMouseLeave={handleCitationMouseLeave}
              onClick={(e) => handleCitationClick(citation, e)}
              title={`${citation.source.title} - ${window.innerWidth < 640 ? "Tap to preview, tap again to ask follow-up" : "Click to ask follow-up question"}`}
            >
              {citationNumber}
            </sup>
          )
        }
        return <span key={index}>{part}</span>
      })}

      {/* Render citation preview for desktop hover */}
      {hoveredCitation && (
        <CitationPreview
          citation={hoveredCitation.citation}
          position={hoveredCitation.position}
          onClose={closeCitationPreview}
        />
      )}

      {/* Render citation preview for mobile tap */}
      {tappedCitation && (
        <CitationPreview
          citation={tappedCitation.citation}
          position={tappedCitation.position}
          onClose={closeCitationPreview}
        />
      )}
    </span>
  )
}
