"use client";

import React, { useState } from 'react';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Lock, 
  AlertTriangle,
  MoreHorizontal,
  Edit3
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { PrivilegedContent } from '@/lib/types/privilege-log';
import { PrivilegeStatus } from '@/lib/types/privilege-log';
import { 
  PRIVILEGE_STATUS_CONFIG, 
  getConfidenceColor, 
  getConfidenceLabel 
} from '@/lib/types/privilege-log';

interface PrivilegeContentItemProps {
  content: PrivilegedContent;
  onReview: (contentId: string, status: PrivilegeStatus, reason?: string, applyRedaction?: boolean) => Promise<void>;
  onRedact: (contentId: string, reason: string, redactionText?: string) => Promise<void>;
  loading?: boolean;
}

export function PrivilegeContentItem({ 
  content, 
  onReview, 
  onRedact, 
  loading = false 
}: PrivilegeContentItemProps) {
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [showRedactDialog, setShowRedactDialog] = useState(false);
  const [reviewReason, setReviewReason] = useState('');
  const [redactionReason, setRedactionReason] = useState('');
  const [redactionText, setRedactionText] = useState('[REDACTED]');
  const [pendingStatus, setPendingStatus] = useState<PrivilegeStatus | null>(null);

  const statusConfig = PRIVILEGE_STATUS_CONFIG[content.status];
  const confidenceColor = getConfidenceColor(content.confidenceScore);
  const confidenceLabel = getConfidenceLabel(content.confidenceScore);

  const handleReview = async (status: PrivilegeStatus, applyRedaction = false) => {
    setPendingStatus(status);
    try {
      await onReview(content.id, status, reviewReason || undefined, applyRedaction);
      setShowReviewDialog(false);
      setReviewReason('');
    } finally {
      setPendingStatus(null);
    }
  };

  const handleRedact = async () => {
    try {
      await onRedact(content.id, redactionReason, redactionText);
      setShowRedactDialog(false);
      setRedactionReason('');
      setRedactionText('[REDACTED]');
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const formatPrivilegeType = (type: string): string => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getActionButtons = () => {
    if (content.status === 'redacted') {
      return (
        <Badge variant="secondary" className="gap-1">
          <Lock className="h-3 w-3" />
          Redacted
        </Badge>
      );
    }

    return (
      <div className="flex gap-2">
        {content.status === 'detected' && (
          <>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleReview(PrivilegeStatus.CONFIRMED)}
              disabled={loading}
              className="gap-1"
            >
              <CheckCircle className="h-3 w-3" />
              Confirm
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleReview(PrivilegeStatus.REJECTED)}
              disabled={loading}
              className="gap-1"
            >
              <XCircle className="h-3 w-3" />
              Reject
            </Button>
          </>
        )}
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="sm" variant="outline" disabled={loading}>
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setShowReviewDialog(true)}>
              <Eye className="h-4 w-4 mr-2" />
              Review with Reason
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowRedactDialog(true)}>
              <Lock className="h-4 w-4 mr-2" />
              Apply Redaction
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  };

  return (
    <>
      <Card className="mb-4">
        <CardContent className="p-4">
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-sm">
                  {formatPrivilegeType(content.privilegeType)}
                </span>
                <Badge className={statusConfig.color} variant="outline">
                  {statusConfig.icon} {statusConfig.label}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={`${confidenceColor} border-current`}>
                  {(content.confidenceScore * 100).toFixed(0)}% {confidenceLabel}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {content.detectionMethod.toUpperCase()}
                </Badge>
              </div>
            </div>

            {/* Content */}
            <div className="bg-muted/30 p-3 rounded-md border-l-4 border-l-primary/30">
              <p className="text-sm leading-relaxed">
                "{content.content}"
              </p>
            </div>

            {/* Position Info */}
            <div className="text-xs text-muted-foreground">
              Position: {content.startPosition} - {content.endPosition}
              {content.reviewedBy && (
                <span className="ml-4">
                  Reviewed by: {content.reviewedBy}
                  {content.reviewedAt && (
                    <span className="ml-2">
                      on {new Date(content.reviewedAt).toLocaleDateString()}
                    </span>
                  )}
                </span>
              )}
            </div>

            {/* Redaction Info */}
            {content.redactionApplied && content.redactionReason && (
              <div className="bg-gray-50 p-2 rounded text-xs">
                <strong>Redaction Reason:</strong> {content.redactionReason}
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end pt-2 border-t">
              {getActionButtons()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Review Privileged Content</DialogTitle>
            <DialogDescription>
              Provide your review decision and optional reasoning for this privileged content item.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm">"{content.content}"</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="review-reason">Reason (Optional)</Label>
              <Textarea
                id="review-reason"
                placeholder="Provide reasoning for your review decision..."
                value={reviewReason}
                onChange={(e) => setReviewReason(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setShowReviewDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleReview(PrivilegeStatus.REJECTED)}
              disabled={loading || pendingStatus === PrivilegeStatus.REJECTED}
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject
            </Button>
            <Button
              onClick={() => handleReview(PrivilegeStatus.CONFIRMED)}
              disabled={loading || pendingStatus === PrivilegeStatus.CONFIRMED}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Redaction Dialog */}
      <Dialog open={showRedactDialog} onOpenChange={setShowRedactDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Apply Redaction</DialogTitle>
            <DialogDescription>
              Configure the redaction for this privileged content item.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-muted/30 p-3 rounded-md">
              <p className="text-sm">"{content.content}"</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="redaction-text">Redaction Text</Label>
              <Textarea
                id="redaction-text"
                value={redactionText}
                onChange={(e) => setRedactionText(e.target.value)}
                className="min-h-[60px]"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="redaction-reason">Reason for Redaction</Label>
              <Textarea
                id="redaction-reason"
                placeholder="Explain why this content is being redacted..."
                value={redactionReason}
                onChange={(e) => setRedactionReason(e.target.value)}
                className="min-h-[80px]"
                required
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRedactDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleRedact}
              disabled={loading || !redactionReason.trim()}
            >
              <Lock className="h-4 w-4 mr-2" />
              Apply Redaction
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
