"use client";

import type React from "react";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
	ChevronDown,
	ChevronRight,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { useState } from "react";
import { useUIContext } from "@/lib/ui/ui-context";
import { DevOnlyWrapper } from "@/components/dev-only-wrapper";
import {
	STANDALONE_ROUTES,
	ROUTE_CATEGORIES,
	DEV_ROUTE_CATEGORIES,
	getStandaloneRoutesWithActive,
	getRouteCategoriesWithActive,
	type RouteItem,
	type RouteCategoryWithActive,
} from "@/lib/navigation/navigation-config";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Badge } from "@/components/ui/badge";

export function Sidebar({ className }: React.HTMLAttributes<HTMLDivElement>) {
	const pathname = usePathname();
	const { isMobileSidebarOpen, setIsMobileSidebarOpen } = useUIContext();
	const { subscription } = useSubscription();
	const isProUser = subscription?.tier && subscription.tier !== "law_student";
	const [expandedCategories, setExpandedCategories] = useState<
		Record<string, boolean>
	>({
		"your documents": true,
		"ai that actually works": true,
		"real legal tools": true,
		"compare like a pro": true,
		"🚧 beta features": true,
	});

	const toggleCategory = (category: string) => {
		setExpandedCategories((prev) => ({
			...prev,
			[category]: !prev[category],
		}));
	};

	// Get routes with active state from shared config
	const standaloneRoutes = getStandaloneRoutesWithActive(pathname);
	const routeCategories = getRouteCategoriesWithActive(pathname, ROUTE_CATEGORIES);
	const devOnlyCategories = getRouteCategoriesWithActive(pathname, DEV_ROUTE_CATEGORIES);

	const renderLink = (route: RouteItem) => {
		const isDisabled = route.pro && !isProUser;
		const content = (
			<>
				<route.icon className="h-4 w-4 mr-3" />
				{route.label}
				{route.pro && (
					<Badge variant="secondary" className="ml-2 text-xs px-1.5 py-0.5">
						PRO
					</Badge>
				)}
			</>
		);

		if (isDisabled) {
			return (
				<div
					key={route.href}
					className={cn(
						"flex items-center px-3 py-2 rounded-lg text-sm font-medium opacity-50 cursor-not-allowed",
						"text-muted-foreground"
					)}
				>
					{content}
				</div>
			);
		}
		return (
			<Link
				key={route.href}
				href={route.href}
				className={cn(
					"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
					route.active
						? "bg-secondary dark:bg-[#252525] text-foreground"
						: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
				)}
			>
				{content}
			</Link>
		);
	};

	const renderMobileLink = (route: RouteItem, onClickEnabled: boolean = true) => {
		const isDisabled = route.pro && !isProUser;
		const content = (
			<>
				<route.icon className="h-4 w-4 mr-3" />
				{route.label}
				{route.pro && (
					<Badge variant="secondary" className="ml-2 text-xs px-1.5 py-0.5">
						PRO
					</Badge>
				)}
			</>
		);

		if (isDisabled) {
			return (
				<div key={route.href} className="flex items-center px-3 py-2 rounded-lg text-sm font-medium opacity-50 cursor-not-allowed text-muted-foreground">
					{content}
				</div>
			);
		}
		return (
			<Link
				key={route.href}
				href={route.href}
				onClick={() => onClickEnabled && setIsMobileSidebarOpen(false)}
				className={cn(
					"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
					route.active
						? "bg-secondary dark:bg-[#252525] text-foreground"
						: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
				)}
			>
				{content}
			</Link>
		);
	};

	return (
		<>
			{/* Mobile Sidebar controlled by GlobalHeader */}
			<Sheet open={isMobileSidebarOpen} onOpenChange={setIsMobileSidebarOpen}>
				<SheetContent side="left" className="p-0 bg-card dark:bg-[#1a1a1a] w-64 sm:w-72">
					<MobileSidebar
						standaloneRoutes={standaloneRoutes}
						routeCategories={routeCategories}
						devOnlyCategories={devOnlyCategories}
						expandedCategories={expandedCategories}
						toggleCategory={toggleCategory}
						setOpen={setIsMobileSidebarOpen}
					/>
				</SheetContent>
			</Sheet>

			{/* Desktop Sidebar */}
			<aside
				className={cn(
					"hidden sm:flex h-[calc(100vh-3.5rem)] flex-col bg-card dark:bg-[#1a1a1a] border-r w-72 fixed top-14 left-0 z-10",
					className
				)}
			>
				{/* The header content (logo, theme toggle) is now in GlobalHeader */}
				<ScrollArea className="flex-1 px-3">
					<div className="space-y-1 py-2">
						{/* Standalone Routes */}
						{standaloneRoutes.map((route) => renderMobileLink(route))}

						<div className="my-2 border-t border-border/40"></div>

						{/* Production Routes */}
						{routeCategories.map((category) => (
							<div key={category.title} className="mb-2">
								<button
									onClick={() => toggleCategory(category.title.toLowerCase())}
									className={cn(
										"w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors",
										"text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
									)}
								>
									<div className="flex items-center">
										<category.icon className="h-4 w-4 mr-3" />
										{category.title}
									</div>
									{expandedCategories[category.title.toLowerCase()] ? (
										<ChevronDown className="h-4 w-4" />
									) : (
										<ChevronRight className="h-4 w-4" />
									)}
								</button>

								{expandedCategories[category.title.toLowerCase()] && (
									<div className="ml-4 pl-2 border-l border-border/40 mt-1 space-y-1">
										{category.routes.map((route) => renderMobileLink(route))}
									</div>
								)}
							</div>
						))}

						{/* Dev-only Routes */}
						<DevOnlyWrapper>
							<div className="my-2 border-t border-border/40"></div>
							{devOnlyCategories.map((category) => (
								<div key={category.title} className="mb-2">
									<button
										onClick={() => toggleCategory(category.title.toLowerCase())}
										className={cn(
											"w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors",
											"text-orange-600 hover:bg-orange-50 dark:text-orange-400 dark:hover:bg-orange-950/20"
										)}
									>
										<div className="flex items-center">
											<category.icon className="h-4 w-4 mr-3" />
											{category.title}
										</div>
										{expandedCategories[category.title.toLowerCase()] ? (
											<ChevronDown className="h-4 w-4" />
										) : (
											<ChevronRight className="h-4 w-4" />
										)}
									</button>

									{expandedCategories[category.title.toLowerCase()] && (
										<div className="ml-4 pl-2 border-l border-orange-200 dark:border-orange-800 mt-1 space-y-1">
											{category.routes.map((route) => renderMobileLink(route))}
										</div>
									)}
								</div>
							))}
						</DevOnlyWrapper>
					</div>
				</ScrollArea>
			</aside>
		</>
	);
}



interface MobileSidebarProps {
	standaloneRoutes: RouteItem[];
	routeCategories: RouteCategoryWithActive[];
	devOnlyCategories: RouteCategoryWithActive[];
	expandedCategories: Record<string, boolean>;
	toggleCategory: (category: string) => void;
	setOpen: (open: boolean) => void;
}

function MobileSidebar({
	standaloneRoutes,
	routeCategories,
	devOnlyCategories,
	expandedCategories,
	toggleCategory,
	setOpen,
}: MobileSidebarProps) {
	const { subscription } = useSubscription();
	const isProUser = subscription?.tier && subscription.tier !== "law_student";
	const pathname = usePathname();

	const renderMobileLinkInner = (route: RouteItem) => {
		const isDisabled = route.pro && !isProUser;
		const content = (
			<>
				<route.icon className="h-4 w-4 mr-3" />
				{route.label}
				{route.pro && (
					<Badge variant="secondary" className="ml-2 text-xs px-1.5 py-0.5">PRO</Badge>
				)}
			</>
		);

		if (isDisabled) {
			return (
				<div key={route.href} className="flex items-center px-3 py-2 rounded-lg text-sm font-medium opacity-50 cursor-not-allowed text-muted-foreground">
					{content}
				</div>
			);
		}

		return (
			<Link key={route.href} href={route.href} onClick={() => setOpen(false)} className={cn("flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors", route.active?"bg-secondary dark:bg-[#252525] text-foreground":"text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground")}>{content}</Link>
		);
	};

	return (
		<div className="flex h-full flex-col">
			<ScrollArea className="flex-1 px-3">
				<div className="space-y-1 py-2">
					{/* Standalone Routes */}
					{standaloneRoutes.map((route) => renderMobileLinkInner(route))}

					<div className="my-2 border-t border-border/40"></div>

					{/* Categorized Routes */}
					{routeCategories.map((category) => (
						<div key={category.title} className="mb-2">
							<button
								onClick={() => toggleCategory(category.title.toLowerCase())}
								className={cn(
									"w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors",
									"text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
								)}
							>
								<div className="flex items-center">
									<category.icon className="h-4 w-4 mr-3" />
									{category.title}
								</div>
								{expandedCategories[category.title.toLowerCase()] ? (
									<ChevronDown className="h-4 w-4" />
								) : (
									<ChevronRight className="h-4 w-4" />
								)}
							</button>

							{expandedCategories[category.title.toLowerCase()] && (
								<div className="ml-4 pl-2 border-l border-border/40 mt-1 space-y-1">
									{category.routes.map((route) => renderMobileLinkInner(route))}
								</div>
							)}
						</div>
					))}

					{/* Dev-only Routes */}
					<DevOnlyWrapper>
						<div className="my-2 border-t border-border/40"></div>
						{devOnlyCategories.map((category) => (
							<div key={category.title} className="mb-2">
								<button
									onClick={() => toggleCategory(category.title.toLowerCase())}
									className={cn(
										"w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors",
										"text-orange-600 hover:bg-orange-50 dark:text-orange-400 dark:hover:bg-orange-950/20"
									)}
								>
									<div className="flex items-center">
										<category.icon className="h-4 w-4 mr-3" />
										{category.title}
									</div>
									{expandedCategories[category.title.toLowerCase()] ? (
										<ChevronDown className="h-4 w-4" />
									) : (
										<ChevronRight className="h-4 w-4" />
									)}
								</button>

								{expandedCategories[category.title.toLowerCase()] && (
									<div className="ml-4 pl-2 border-l border-orange-200 dark:border-orange-800 mt-1 space-y-1">
										{category.routes.map((route) => renderMobileLinkInner(route))}
									</div>
								)}
							</div>
						))}
					</DevOnlyWrapper>
				</div>
			</ScrollArea>
		</div>
	);
}
