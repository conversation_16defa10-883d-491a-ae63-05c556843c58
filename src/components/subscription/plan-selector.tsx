"use client";

import { useState } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import {
	SUBSCRIPTION_PLANS,
	SUBSCRIPTION_FEATURES,
	SubscriptionTier,
	TIER_CREDIT_ALLOCATIONS,
} from "@/lib/types/subscription";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	CheckIcon,
	XIcon,
	ArrowRightIcon,
	CreditCardIcon,
	AlertTriangleIcon,
	Coins,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
	Table,
	TableBody,
	TableCaption,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

interface PlanSelectorProps {
	currentTier?: SubscriptionTier;
}

export function PlanSelector({ currentTier = "law_student" }: PlanSelectorProps) {
	const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(
		null
	);
	const [isLoading, setIsLoading] = useState(false);
	const {
		createCheckoutSession,
		downgradeToFreeTier,
		isInTrial,
		getRemainingTrialDays,
	} = useSubscription();
	const { toast } = useToast();

	const inTrial = isInTrial();
	const trialDays = getRemainingTrialDays();

	const handleSelectPlan = async (tier: SubscriptionTier) => {
		if (tier === currentTier) {
			toast({
				title: "Already subscribed",
				description: `You are already on the ${
					tier.charAt(0).toUpperCase() + tier.slice(1)
				} plan.`,
			});
			return;
		}

		try {
			setIsLoading(true);
			setSelectedTier(tier);

			// If downgrading to law student tier, use the direct API endpoint
			if (tier === "law_student") {
			  await downgradeToFreeTier();
			  toast({
			    title: "Subscription downgraded",
			    description:
			      "Your subscription has been downgraded to the law student tier.",
				});
				// Reload the page to show updated subscription details
				window.location.reload();
				return;
			}

			// For paid plans, use the Stripe checkout
			const checkoutUrl = await createCheckoutSession(tier);
			window.location.href = checkoutUrl;
		} catch (error) {
			console.error("Error processing subscription change:", error);
			toast({
				variant: "destructive",
				title: tier === "law_student" ? "Downgrade failed" : "Checkout failed",
				description: `Failed to ${
				  tier === "law_student" ? "downgrade subscription" : "create checkout session"
				}. Please try again.`,
			});
		} finally {
			setIsLoading(false);
		}
	};

	const renderPlanButton = (plan: (typeof SUBSCRIPTION_PLANS)[0]) => {
		const isCurrentPlan = plan.id === currentTier;
		const isSelected = plan.id === selectedTier;
		const isDowngradeToFree = plan.id === "law_student" && currentTier !== "law_student";

		if (isDowngradeToFree) {
			return (
				<AlertDialog>
					<AlertDialogTrigger asChild>
						<Button className="w-full" variant="outline" disabled={isLoading}>
							{isLoading && isSelected ? (
								<>Processing...</>
							) : (
								<>
									Downgrade to Law Student
									<ArrowRightIcon className="ml-2 h-4 w-4" />
								</>
							)}
						</Button>
					</AlertDialogTrigger>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>Downgrade to Law Student Tier</AlertDialogTitle>
							<AlertDialogDescription>
								Are you sure you want to downgrade to the law student tier? This will
								take effect immediately and you will lose access to premium
								features.
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>Cancel</AlertDialogCancel>
							<AlertDialogAction
								onClick={() => handleSelectPlan("law_student")}
								className="bg-amber-500 hover:bg-amber-600 text-white"
							>
								{isLoading && isSelected ? "Processing..." : "Downgrade Now"}
							</AlertDialogAction>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
			);
		}

		return (
			<Button
				className="w-full"
				variant={isCurrentPlan ? "outline" : "default"}
				disabled={isLoading || (isCurrentPlan && !inTrial)}
				onClick={() => handleSelectPlan(plan.id)}
			>
				{isLoading && isSelected ? (
					<>Processing...</>
				) : isCurrentPlan ? (
					<>Current Plan</>
				) : (
					<>
						{plan.price === 0 ? "Downgrade" : "Upgrade"}
						<ArrowRightIcon className="ml-2 h-4 w-4" />
					</>
				)}
			</Button>
		);
	};

	return (
		<div className="space-y-8">
			{inTrial && (
				<div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
					<div className="flex items-start">
						<AlertTriangleIcon className="h-5 w-5 text-amber-500 mt-0.5" />
						<div className="ml-3">
							<h3 className="text-sm font-medium text-amber-800 dark:text-amber-300">
								Pro Trial Active
							</h3>
							<div className="mt-1 text-sm text-amber-700 dark:text-amber-400">
								<p>
									You are currently on a Pro trial that will expire in{" "}
									{trialDays} days. Upgrade now to keep access to all Pro
									features.
								</p>
							</div>
						</div>
					</div>
				</div>
			)}

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{SUBSCRIPTION_PLANS.map((plan) => {
					const isCurrentPlan = plan.id === currentTier;
					const isSelected = plan.id === selectedTier;

					return (
						<Card
							key={plan.id}
							className={cn(
								"relative overflow-hidden transition-all",
								isCurrentPlan && "border-primary",
								isSelected && "ring-2 ring-primary"
							)}
						>
							{isCurrentPlan && (
								<Badge className="absolute top-4 right-4 bg-primary">
									Current Plan
								</Badge>
							)}

							<CardHeader>
								<CardTitle>{plan.name}</CardTitle>
								<CardDescription>{plan.description}</CardDescription>
							</CardHeader>

							<CardContent className="space-y-4">
								<div className="text-3xl font-bold">
									${plan.price}
									<span className="text-sm font-normal text-muted-foreground">
										/month
									</span>
								</div>

								<div className="space-y-2">
									<div className="flex items-center">
										<CheckIcon className="h-4 w-4 text-green-500 mr-2" />
										<span>{plan.limits.documentLimit} documents</span>
									</div>
									<div className="flex items-center">
										<CheckIcon className="h-4 w-4 text-green-500 mr-2" />
										<span>Unlimited AI analysis</span>
									</div>
									<div className="flex items-center">
										<Coins className="h-4 w-4 text-blue-500 mr-2" />
										<span>
											{TIER_CREDIT_ALLOCATIONS[plan.id] || 0} credits/month for AI features
										</span>
									</div>
									<div className="flex items-center">
										<CheckIcon className="h-4 w-4 text-green-500 mr-2" />
										<span>Unlimited CRUD operations (FREE)</span>
									</div>
								</div>
							</CardContent>

							<CardFooter>{renderPlanButton(plan)}</CardFooter>
						</Card>
					);
				})}
			</div>

			{/* Plan Comparison Table */}
			<div className="mt-12">
				<h3 className="text-xl font-semibold mb-4">Plan Comparison</h3>

				{/* Key Plan Details */}
				<div className="rounded-md border mb-6">
					<Table>
						<TableCaption>
							Pricing and credit allocation comparison
						</TableCaption>
						<TableHeader>
							<TableRow>
								<TableHead className="w-[40%]">Plan Details</TableHead>
								<TableHead className="text-center">Law Student</TableHead>
								<TableHead className="text-center">Lawyer</TableHead>
								<TableHead className="text-center">Law Firm</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							<TableRow>
								<TableCell className="font-medium">Monthly Price</TableCell>
								<TableCell className="text-center">
									<Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
										FREE
									</Badge>
								</TableCell>
								<TableCell className="text-center">
									<Badge variant="default" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
										$29.99
									</Badge>
								</TableCell>
								<TableCell className="text-center">
									<Badge variant="default" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
										$99.99
									</Badge>
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="font-medium">
									<div className="flex items-center gap-2">
										<Coins className="h-4 w-4" />
										Monthly Credits
									</div>
								</TableCell>
								<TableCell className="text-center font-semibold">50</TableCell>
								<TableCell className="text-center font-semibold">500</TableCell>
								<TableCell className="text-center font-semibold">2,000</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="font-medium">Document Limit</TableCell>
								<TableCell className="text-center">10/month</TableCell>
								<TableCell className="text-center">200/month</TableCell>
								<TableCell className="text-center">Unlimited</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="font-medium">Best For</TableCell>
								<TableCell className="text-center text-sm">Students & Learning</TableCell>
								<TableCell className="text-center text-sm">Individual Lawyers</TableCell>
								<TableCell className="text-center text-sm">Law Firms & Teams</TableCell>
							</TableRow>
						</TableBody>
					</Table>
				</div>

				{/* Feature Comparison Table */}
				<h4 className="text-lg font-semibold mb-4">Feature Availability</h4>
				<div className="rounded-md border">
					<Table>
						<TableCaption>
							Detailed feature comparison between subscription tiers
						</TableCaption>
						<TableHeader>
							<TableRow>
								<TableHead className="w-[40%]">Feature</TableHead>
								<TableHead className="text-center">Law Student</TableHead>
								<TableHead className="text-center">Lawyer</TableHead>
								<TableHead className="text-center">Law Firm</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{SUBSCRIPTION_FEATURES.map((feature) => (
								<TableRow key={feature.id}>
									<TableCell className="font-medium">
										<div className="font-medium">{feature.name}</div>
										<div className="text-sm text-muted-foreground">
											{feature.description}
										</div>
									</TableCell>
									<TableCell className="text-center">
										{feature.tiers.includes("law_student") ? (
											<div className="flex items-center justify-center">
												<CheckIcon className="h-5 w-5 text-green-500" />
											</div>
										) : (
											<div className="flex items-center justify-center">
												<XIcon className="h-5 w-5 text-gray-300 dark:text-gray-600" />
											</div>
										)}
									</TableCell>
									<TableCell className="text-center">
										{feature.tiers.includes("lawyer") ? (
											<div className="flex items-center justify-center">
												<CheckIcon className="h-5 w-5 text-green-500" />
											</div>
										) : (
											<div className="flex items-center justify-center">
												<XIcon className="h-5 w-5 text-gray-300 dark:text-gray-600" />
											</div>
										)}
									</TableCell>
									<TableCell className="text-center">
										{feature.tiers.includes("law_firm") ? (
											<div className="flex items-center justify-center">
												<CheckIcon className="h-5 w-5 text-green-500" />
											</div>
										) : (
											<div className="flex items-center justify-center">
												<XIcon className="h-5 w-5 text-gray-300 dark:text-gray-600" />
											</div>
										)}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>

			<div className="mt-8 text-center text-sm text-muted-foreground">
				<p className="flex items-center justify-center">
					<CreditCardIcon className="h-4 w-4 mr-2" />
					Secure payment processing by Stripe
				</p>
			</div>
		</div>
	);
}
