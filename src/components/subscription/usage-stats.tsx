"use client";

import { Subscription, SUBSCRIPTION_PLANS } from "@/lib/types/subscription";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { FileTextIcon, BarChart2Icon, AlertTriangleIcon } from "lucide-react";
import { format } from "date-fns";

interface UsageStatsProps {
  subscription: Subscription | null;
}

export function UsageStats({ subscription }: UsageStatsProps) {
  if (!subscription) {
    return null;
  }

  const plan = SUBSCRIPTION_PLANS.find(p => p.id === subscription.tier);
  if (!plan) return null;

  const { usageStats } = subscription;

  const documentLimit = plan.limits.documentLimit === "unlimited" ? Infinity : plan.limits.documentLimit;
  // 🎯 Analysis limits removed - now unlimited within credit allocation

  const documentPercentage = documentLimit === Infinity ? 0 : Math.min(Math.round((usageStats.documentsProcessed / documentLimit) * 100), 100);
  // Analysis is now unlimited within credit allocation - no percentage needed

  const documentWarning = documentPercentage >= 80;
  // No analysis warning needed since analysis is unlimited within credit allocation
  
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMMM d, yyyy 'at' h:mm a");
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Usage Statistics</CardTitle>
          <CardDescription>
            Your current usage and limits for the {plan.name} plan
            {subscription.trialTier && (
              <span className="ml-1 text-amber-500 font-medium">
                (Pro Trial)
              </span>
            )}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FileTextIcon className="h-5 w-5 mr-2 text-blue-500" />
                <span className="font-medium">Documents</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {usageStats.documentsProcessed} / {documentLimit === Infinity ? "Unlimited" : documentLimit}
              </div>
            </div>
            
            <Progress value={documentPercentage} className="h-2" />
            
            {documentWarning && (
              <div className="flex items-start text-sm text-amber-600 dark:text-amber-400">
                <AlertTriangleIcon className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <p>
                  You are approaching your document limit. 
                  {subscription.tier === 'law_student' && " Consider upgrading to the Lawyer plan for a higher limit."}
                </p>
              </div>
            )}
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <BarChart2Icon className="h-5 w-5 mr-2 text-green-500" />
                <span className="font-medium">AI Analysis</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Unlimited within credit allocation
              </div>
            </div>

            <div className="flex items-start text-sm text-green-600 dark:text-green-400">
              <BarChart2Icon className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
              <p>
                🎯 AI analysis is now unlimited! Credits are only consumed for AI-powered features.
                All document viewing, editing, and management operations are completely FREE.
              </p>
            </div>
          </div>
          
          <div className="pt-4 text-xs text-muted-foreground">
            Last updated: {formatDate(usageStats.lastUpdated)}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Feature Access</CardTitle>
          <CardDescription>Features available in your current plan</CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {SUBSCRIPTION_PLANS.find(p => p.id === subscription.tier)?.features.map(featureId => {
              const hasFeature = subscription.features.includes(featureId) ||
                (subscription.trialTier &&
                 SUBSCRIPTION_PLANS.find(p => p.id === subscription.trialTier)?.features.includes(featureId));
              
              return (
                <div 
                  key={featureId} 
                  className="flex items-center p-2 rounded-md border"
                >
                  <div className={`h-2 w-2 rounded-full mr-3 ${hasFeature ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
                  <span className={`text-sm ${!hasFeature && 'text-muted-foreground'}`}>
                    {featureId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                  </span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
