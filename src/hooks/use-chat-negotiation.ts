import { useState, useCallback, useEffect } from 'react';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import type {
  NegotiationSession,
  CreateSessionRequest,
  SendMoveRequest,
  SendMoveResponse,
  ChatNegotiationMessage,
  NegotiationSessionState,
  ExtractDataRequest,
  ExtractDataResponse,
  GetSessionsResponse,
  ConversationHistoryResponse
} from '@/lib/types/chat-negotiation';

interface UseChatNegotiationOptions {
  sessionId?: string;
  autoLoad?: boolean;
}

export function useChatNegotiation(options: UseChatNegotiationOptions = {}) {
  const [state, setState] = useState<NegotiationSessionState>({
    session: null,
    messages: [],
    isLoading: false,
    error: null,
    currentMessage: '',
    isTyping: false,
  });

  // Helper function to get session ID consistently
  const getSessionId = (session: NegotiationSession): string => {
    return session._id || session.id || session.negotiationSessionId;
  };

  const [sessions, setSessions] = useState<{
    data: NegotiationSession[];
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    isLoading: boolean;
    error: string | null;
  }>({
    data: [],
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
    isLoading: false,
    error: null,
  });

  // Create a new negotiation session
  const createSession = useCallback(async (request: CreateSessionRequest): Promise<NegotiationSession> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const session = await chatNegotiationService.createSession(request);
      setState(prev => ({
        ...prev,
        session,
        messages: [],
        isLoading: false,
      }));
      return session;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to create session',
      }));
      throw error;
    }
  }, []);

  // Load session details and conversation history
  const loadSession = useCallback(async (sessionId: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // Load session and messages in parallel
      const [session, conversationHistory] = await Promise.all([
        chatNegotiationService.getSession(sessionId),
        chatNegotiationService.getMessages(sessionId, {
          limit: 50,
          includeMetadata: true
        }).catch(error => {
          console.warn('Failed to load conversation history:', error);
          return null; // Return null if messages API fails, don't fail the whole load
        })
      ]);

      // Debug: Check what we're getting from the API
      console.log('Loaded conversation history:', conversationHistory);
      console.log('Messages from API:', conversationHistory?.messages);
      
      // Transform messages to match our ChatNegotiationMessage interface
      const transformedMessages: ChatNegotiationMessage[] = conversationHistory?.messages?.map((msg: any) => ({
        id: msg.id,
        sessionId: sessionId,
        role: msg.sender as 'user' | 'ai', // Map sender to role
        content: msg.content,
        timestamp: msg.timestamp,
        extractedData: msg.extractedData,
        suggestions: msg.aiResponseMetadata?.suggestions || msg.suggestions,
        processingTime: msg.processingTimeMs || msg.processingTime,
        relationshipImpact: msg.relationshipImpact,
        scoreImpact: msg.scoreImpact,
        processingTimeMs: msg.processingTimeMs,
        confidence: msg.confidence,
        detectedStrategy: msg.detectedStrategy,
        sentiment: msg.sentiment,
        aiResponseMetadata: msg.aiResponseMetadata
      })) || [];
      
      console.log('Transformed messages:', transformedMessages);
      
      setState(prev => ({
        ...prev,
        session,
        messages: transformedMessages,
        isLoading: false,
      }));
      return session;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to load session',
      }));
      throw error;
    }
  }, []);

  // Load more messages (for pagination)
  const loadMoreMessages = useCallback(async (sessionId: string, offset: number = 0) => {
    if (!sessionId) return;

    try {
      const conversationHistory = await chatNegotiationService.getMessages(sessionId, {
        limit: 20,
        offset,
        includeMetadata: true
      });

      // Transform messages to match our ChatNegotiationMessage interface
      const transformedMessages: ChatNegotiationMessage[] = conversationHistory.messages?.map((msg: any) => ({
        id: msg.id,
        sessionId: sessionId,
        role: msg.sender as 'user' | 'ai', // Map sender to role
        content: msg.content,
        timestamp: msg.timestamp,
        extractedData: msg.extractedData,
        suggestions: msg.aiResponseMetadata?.suggestions || msg.suggestions,
        processingTime: msg.processingTimeMs || msg.processingTime,
        relationshipImpact: msg.relationshipImpact,
        scoreImpact: msg.scoreImpact,
        processingTimeMs: msg.processingTimeMs,
        confidence: msg.confidence,
        detectedStrategy: msg.detectedStrategy,
        sentiment: msg.sentiment,
        aiResponseMetadata: msg.aiResponseMetadata
      })) || [];

      setState(prev => ({
        ...prev,
        messages: offset === 0 ? transformedMessages : [...transformedMessages, ...prev.messages],
      }));

      return conversationHistory;
    } catch (error: any) {
      console.error('Failed to load more messages:', error);
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to load messages',
      }));
      throw error;
    }
  }, []);

  // Send a negotiation move
  const sendMove = useCallback(async (request: SendMoveRequest): Promise<SendMoveResponse> => {
    if (!state.session) {
      const error = new Error('No active session');
      setState(prev => ({ ...prev, error: error.message }));
      throw error;
    }

    // Prevent multiple simultaneous sends
    if (state.isTyping) {
      const error = new Error('Already sending a message');
      setState(prev => ({ ...prev, error: error.message }));
      throw error;
    }

    setState(prev => ({ ...prev, isTyping: true, error: null }));

    try {
      const sessionId = getSessionId(state.session);
      const response = await chatNegotiationService.sendMove(sessionId, request);
      
      // Create message objects for the conversation
      const userMessage: ChatNegotiationMessage = {
        id: `user-${Date.now()}`,
        sessionId: sessionId,
        role: 'user',
        content: response.userMessage.content,
        timestamp: response.userMessage.timestamp,
        extractedData: {
          offer: response.userMessage.extractedData.offer || { price: undefined, currency: undefined, terms: [] },
          strategy: response.userMessage.extractedData.strategy,
          sentiment: response.userMessage.extractedData.sentiment,
          confidence: response.userMessage.extractedData.confidence,
          extractedEntities: [],
          processingTime: response.processingTime,
        },
      };

      const aiMessage: ChatNegotiationMessage = {
        id: `ai-${Date.now()}`,
        sessionId: sessionId,
        role: 'ai',
        content: response.aiResponse.content,
        timestamp: response.aiResponse.timestamp,
        suggestions: response.aiResponse.suggestions,
        processingTime: response.processingTime,
      };

      // Update session with latest data
      const updatedSession: NegotiationSession = {
        ...state.session,
        currentRound: response.sessionUpdate.currentRound,
        extractedTerms: response.sessionUpdate.extractedTerms,
        relationshipMetrics: response.sessionUpdate.relationshipMetrics,
        score: response.sessionUpdate.score,
        totalMessages: state.session.totalMessages + 2,
        updatedAt: new Date().toISOString(),
        lastActivityAt: new Date().toISOString(),
      };

      setState(prev => ({
        ...prev,
        session: updatedSession,
        messages: [...prev.messages, userMessage, aiMessage],
        isTyping: false,
        currentMessage: '',
      }));

      return response;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isTyping: false,
        error: error.message || 'Failed to send move',
      }));
      throw error;
    }
  }, [state.session]);

  // Extract data from message (for real-time feedback)
  const extractData = useCallback(async (request: ExtractDataRequest): Promise<ExtractDataResponse> => {
    try {
      return await chatNegotiationService.extractData(request);
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to extract data',
      }));
      throw error;
    }
  }, []);

  // Load user sessions
  const loadSessions = useCallback(async (params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }) => {
    setSessions(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const response = await chatNegotiationService.getUserSessions(params);
      setSessions(prev => ({
        ...prev,
        data: params?.offset ? [...prev.data, ...response.sessions] : response.sessions,
        total: response.total,
        limit: response.limit,
        offset: response.offset,
        hasMore: response.sessions.length === response.limit && response.offset + response.limit < response.total,
        isLoading: false,
      }));
      return response;
    } catch (error: any) {
      setSessions(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to load sessions',
      }));
      throw error;
    }
  }, []);

  // Pause session
  const pauseSession = useCallback(async () => {
    if (!state.session) return;

    try {
      const updatedSession = await chatNegotiationService.pauseSession(getSessionId(state.session));
      setState(prev => ({ ...prev, session: updatedSession }));
      return updatedSession;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to pause session',
      }));
      throw error;
    }
  }, [state.session]);

  // Resume session
  const resumeSession = useCallback(async () => {
    if (!state.session) return;

    try {
      const updatedSession = await chatNegotiationService.resumeSession(getSessionId(state.session));
      setState(prev => ({ ...prev, session: updatedSession }));
      return updatedSession;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to resume session',
      }));
      throw error;
    }
  }, [state.session]);

  // Complete session
  const completeSession = useCallback(async () => {
    if (!state.session) return;

    try {
      const updatedSession = await chatNegotiationService.completeSession(getSessionId(state.session));
      setState(prev => ({ ...prev, session: updatedSession }));
      return updatedSession;
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to complete session',
      }));
      throw error;
    }
  }, [state.session]);

  // Get suggestions
  const getSuggestions = useCallback(async (context?: {
    currentMessage?: string;
    situation?: string;
  }) => {
    if (!state.session) return null;

    try {
      return await chatNegotiationService.getSuggestions(getSessionId(state.session), context);
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to get suggestions',
      }));
      throw error;
    }
  }, [state.session]);

  // Update current message
  const setCurrentMessage = useCallback((message: string) => {
    setState(prev => ({ ...prev, currentMessage: message }));
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
    setSessions(prev => ({ ...prev, error: null }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      session: null,
      messages: [],
      isLoading: false,
      error: null,
      currentMessage: '',
      isTyping: false,
    });
  }, []);

  // Auto-load session if sessionId provided
  useEffect(() => {
    if (options.sessionId && options.autoLoad !== false && !state.isLoading && !state.session) {
      loadSession(options.sessionId).catch(error => {
        console.error('Failed to auto-load session:', error);
        // Don't throw to prevent infinite loops
      });
    }
  }, [options.sessionId, options.autoLoad, state.isLoading, state.session]);

  return {
    // State
    ...state,
    sessions,
    
    // Actions
    createSession,
    loadSession,
    loadMoreMessages,
    sendMove,
    extractData,
    loadSessions,
    pauseSession,
    resumeSession,
    completeSession,
    getSuggestions,
    setCurrentMessage,
    clearError,
    reset,
    
    // Computed values
    canSendMessage: state.session?.status === 'active' && !state.isTyping && state.currentMessage.trim().length > 0,
    isActive: state.session?.status === 'active',
    isPaused: state.session?.status === 'paused',
    isCompleted: state.session?.status === 'completed',
  };
}

export default useChatNegotiation;