import { useState, useEffect, useCallback } from 'react';
import { documentService, type DocumentMetadata } from '@/lib/services/document-service';

export function useDocuments() {
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchDocuments = useCallback(async () => {
    try {
      setLoading(true);
      const response = await documentService.getDocuments();
      setDocuments(response.items);
      setError(null);
      return response.items;
    } catch (err) {
      console.error('Error fetching documents:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch documents'));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Memoize the refetch function to prevent recreating it on every render
  const refetch = useCallback(async () => {
    return fetchDocuments();
  }, [fetchDocuments]);

  useEffect(() => {
    fetchDocuments();
    // Note: If you want to implement real-time updates in the future,
    // you can uncomment the code below and implement the setupDocumentWebSocket method
  }, [fetchDocuments]);

  return { documents, loading, error, refetch };
}
