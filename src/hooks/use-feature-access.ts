import { useSubscription } from "@/lib/subscription/subscription-context";

export function useFeatureAccess() {
  const { hasFeature, isInTrial, subscription } = useSubscription();

  const canAccessFeature = (featureId: string): boolean => {
    return hasFeature(featureId);
  };

  const checkDocumentLimit = (): boolean => {
    if (!subscription) return false;
    
    const { tier, usageStats } = subscription;
    const plan = SUBSCRIPTION_PLANS.find(p => p.id === tier);
    
    if (!plan) return false;
    
    // If in trial and trial tier is Lawyer, use Lawyer limits
    if (isInTrial() && subscription.trialTier === 'lawyer') {
      const trialPlan = SUBSCRIPTION_PLANS.find(p => p.id === 'lawyer');
      if (trialPlan) {
        const limit = trialPlan.limits.documentLimit;
        return typeof limit === 'number' ? usageStats.documentsProcessed < limit : true;
      }
    }
    
    const limit = plan.limits.documentLimit;
    return typeof limit === 'number' ? usageStats.documentsProcessed < limit : true;
  };

  // 🎯 Analysis limits have been REMOVED - now unlimited within credit allocation
  // All AI analysis operations are unlimited as long as user has credits
  const checkAnalysisLimit = (): boolean => {
    // Analysis is now unlimited for all tiers within their credit allocation
    // Credits are the only limiting factor for AI-powered features
    return true;
  };

  return {
    canAccessFeature,
    checkDocumentLimit,
    checkAnalysisLimit,
  };
}

// Import here to avoid circular dependency
import { SUBSCRIPTION_PLANS } from "@/lib/types/subscription";
