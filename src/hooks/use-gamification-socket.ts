"use client";

import { useRef, useCallback, useState } from 'react';
import type { Socket } from 'socket.io-client';
import { Achievement, LevelUpdate, PressureEvent } from '@/lib/services/gamification-service';
import { useUser } from '@/hooks/use-user';

export interface GamificationSocketEvents {
  achievement_unlocked: (achievement: Achievement) => void;
  level_up: (levelUpdate: LevelUpdate) => void;
  xp_gained: (data: { amount: number; source: string; total: number }) => void;
  
  // Session events
  pressure_event: (event: PressureEvent) => void;
  session_score_update: (data: { score: number; breakdown: Record<string, unknown> }) => void;
  relationship_updated: (data: { characterId: string; changes: Record<string, unknown> }) => void;
  
  // Social events
  leaderboard_updated: (data: { timeframe: string; userRank: number }) => void;
  challenge_update: (data: { challengeId: string; progress: Record<string, unknown> }) => void;
  
  // Connection events
  connect: () => void;
  disconnect: () => void;
  error: (error: Error) => void;
}

export interface UseGamificationSocketOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export function useGamificationSocket(options: UseGamificationSocketOptions = {}) {
  const { autoConnect, reconnectAttempts, reconnectDelay } = options;
  const socketRef = useRef<Socket | null>(null);
  
  // Store config for when needed
  useRef<UseGamificationSocketOptions>({ autoConnect, reconnectAttempts, reconnectDelay });
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [error, setError] = useState<string | null>(null);

  // Event listeners storage
  const eventListenersRef = useRef<Map<keyof GamificationSocketEvents, Array<(...args: any[]) => void>>>(new Map());

  // Initialize socket connection
  const connect = useCallback(() => {
    setConnectionState('connected');
    setIsConnected(true);
    setError(null);
  }, []);

  const disconnect = useCallback(() => {
    setConnectionState('disconnected');
    setIsConnected(false);
  }, []);

  const joinSession = useCallback((sessionId: string) => {
    console.log('Mock: Joining session', sessionId);
  }, []);

  const leaveSession = useCallback((sessionId: string) => {
    console.log('Mock: Leaving session', sessionId);
  }, []);

  const requestLiveScore = useCallback((sessionId: string, moveData: Record<string, unknown>) => {
    console.log('Mock: Requesting live score', sessionId, moveData);
  }, []);

  const acknowledgePressureEvent = useCallback((sessionId: string, eventId: string) => {
    console.log('Mock: Acknowledging pressure event', sessionId, eventId);
  }, []);

  const on = useCallback(<K extends keyof GamificationSocketEvents>(
    event: K,
    listener: GamificationSocketEvents[K]
  ) => {
    console.log('Mock: Adding event listener', event);
  }, []);

  const off = useCallback(<K extends keyof GamificationSocketEvents>(
    event: K,
    _listener: GamificationSocketEvents[K]
  ) => {
    console.log('Mock: Removing event listener', event);
  }, []);

  return {
    isConnected,
    connectionState,
    error,
    connect,
    disconnect,
    joinSession,
    leaveSession,
    requestLiveScore,
    acknowledgePressureEvent,
    on,
    off,
    clearError: () => setError(null),
    getSocket: () => null,
  };
}

// Convenience hooks for specific events
export function useAchievementNotifications() {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [levelUpdates, setLevelUpdates] = useState<LevelUpdate[]>([]);

  return {
    achievements,
    levelUpdates,
    clearAchievements: () => setAchievements([]),
    clearLevelUpdates: () => setLevelUpdates([]),
  };
}

export function usePressureEvents() {
  const [events, setEvents] = useState<PressureEvent[]>([]);

  return {
    events,
    clearEvents: () => setEvents([]),
    acknowledgeEvent: (sessionId: string, eventId: string) => {
      console.log('Mock: Acknowledging pressure event', sessionId, eventId);
    },
  };
}
