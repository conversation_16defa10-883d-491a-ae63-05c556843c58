"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  gamificationService, 
  UserGamificationProfile, 
  Achievement, 
  AICharacter, 
  CharacterRelationship, 
  LeaderboardResponse,
  LevelUpdate,
  SessionGamificationUpdate
} from '@/lib/services/gamification-service';
import { useUser } from '@/hooks/use-user';

export interface UseGamificationOptions {
  autoFetch?: boolean;
  enableRealTimeUpdates?: boolean;
}

export function useGamification(options: UseGamificationOptions = {}) {
  const { autoFetch = true, enableRealTimeUpdates = false } = options;
  const { user } = useUser();
  
  // State
  const [profile, setProfile] = useState<UserGamificationProfile | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [characters, setCharacters] = useState<AICharacter[]>([]);
  const [relationships, setRelationships] = useState<Record<string, CharacterRelationship>>({});
  const [leaderboard, setLeaderboard] = useState<LeaderboardResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch user profile
  const fetchProfile = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const userProfile = await gamificationService.getUserProfile(user.id);
      setProfile(userProfile);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch profile');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch achievements
  const fetchAchievements = useCallback(async (filters?: { category?: string; rarity?: string }) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await gamificationService.getUserAchievements(user.id, filters);
      setAchievements(result.achievements);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch achievements');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch characters
  const fetchCharacters = useCallback(async (filters?: { difficulty?: number; unlocked?: boolean }) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const characterList = await gamificationService.getCharacters(user.id, filters);
      setCharacters(characterList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch characters');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch character relationships
  const fetchRelationships = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const relationshipData = await gamificationService.getCharacterRelationships(user.id);
      setRelationships(relationshipData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch relationships');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch leaderboard
  const fetchLeaderboard = useCallback(async (
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'weekly',
    scope: 'global' | 'organization' | 'industry' = 'global',
    scopeId?: string
  ) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const leaderboardData = await gamificationService.getLeaderboard(
        timeframe, 
        scope, 
        scopeId, 
        50, 
        user.id
      );
      setLeaderboard(leaderboardData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch leaderboard');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Award experience
  const awardExperience = useCallback(async (
    amount: number, 
    source: string, 
    metadata?: any
  ): Promise<LevelUpdate | null> => {
    if (!user?.id) return null;
    
    try {
      const levelUpdate = await gamificationService.awardExperience(user.id, amount, source, metadata);
      
      // Update profile if level changed
      if (levelUpdate.leveledUp) {
        await fetchProfile();
      }
      
      return levelUpdate;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to award experience');
      return null;
    }
  }, [user?.id, fetchProfile]);

  // Check achievements
  const checkAchievements = useCallback(async (
    sessionId: string, 
    sessionData: any
  ): Promise<Achievement[]> => {
    if (!user?.id) return [];
    
    try {
      const unlockedAchievements = await gamificationService.checkAchievements(
        user.id, 
        sessionId, 
        sessionData
      );
      
      // Refresh achievements if any were unlocked
      if (unlockedAchievements.length > 0) {
        await fetchAchievements();
      }
      
      return unlockedAchievements;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check achievements');
      return [];
    }
  }, [user?.id, fetchAchievements]);

  // Update session gamification
  const updateSessionGamification = useCallback(async (
    sessionId: string, 
    moveData: any
  ): Promise<SessionGamificationUpdate | null> => {
    if (!user?.id) return null;
    
    try {
      const update = await gamificationService.updateSessionGamification(sessionId, moveData);
      
      // Update local state if needed
      if (update.levelUpdate?.leveledUp) {
        await fetchProfile();
      }
      
      if (update.achievementsUnlocked.length > 0) {
        await fetchAchievements();
      }
      
      return update;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update session gamification');
      return null;
    }
  }, [user?.id, fetchProfile, fetchAchievements]);

  // Initialize session gamification
  const initializeSessionGamification = useCallback(async (
    sessionId: string, 
    characterId: string
  ) => {
    if (!user?.id) return null;
    
    try {
      return await gamificationService.initializeSessionGamification(sessionId, user.id, characterId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize session gamification');
      return null;
    }
  }, [user?.id]);

  // Finalize session gamification
  const finalizeSessionGamification = useCallback(async (sessionId: string) => {
    if (!user?.id) return null;
    
    try {
      const result = await gamificationService.finalizeSessionGamification(sessionId);
      
      // Refresh data
      await Promise.all([
        fetchProfile(),
        fetchAchievements(),
        fetchRelationships()
      ]);
      
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to finalize session gamification');
      return null;
    }
  }, [user?.id, fetchProfile, fetchAchievements, fetchRelationships]);

  // Share achievement
  const shareAchievement = useCallback(async (
    achievementId: string, 
    platform: string, 
    message?: string
  ) => {
    if (!user?.id) return;
    
    try {
      await gamificationService.shareAchievement(user.id, achievementId, platform, message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to share achievement');
    }
  }, [user?.id]);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetch && user?.id) {
      Promise.all([
        fetchProfile(),
        fetchAchievements(),
        fetchCharacters(),
        fetchRelationships(),
        fetchLeaderboard()
      ]);
    }
  }, [autoFetch, user?.id, fetchProfile, fetchAchievements, fetchCharacters, fetchRelationships, fetchLeaderboard]);

  return {
    // State
    profile,
    achievements,
    characters,
    relationships,
    leaderboard,
    loading,
    error,
    
    // Actions
    fetchProfile,
    fetchAchievements,
    fetchCharacters,
    fetchRelationships,
    fetchLeaderboard,
    awardExperience,
    checkAchievements,
    updateSessionGamification,
    initializeSessionGamification,
    finalizeSessionGamification,
    shareAchievement,
    
    // Computed values
    userLevel: profile?.level,
    unlockedCharacters: characters.filter(c => (c as any).unlocked),
    recentAchievements: achievements.filter(a => (a as any).unlocked).slice(0, 5),
    userRank: leaderboard?.userRank,
    
    // Utilities
    clearError: () => setError(null),
    refresh: () => {
      if (user?.id) {
        Promise.all([
          fetchProfile(),
          fetchAchievements(),
          fetchCharacters(),
          fetchRelationships(),
          fetchLeaderboard()
        ]);
      }
    }
  };
}
