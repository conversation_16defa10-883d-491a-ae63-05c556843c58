import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { legalResearchService } from "@/lib/services/legal-research-service";
import { LegalResearchError } from "@/lib/types/legal-research";
import { useSubscription } from "@/lib/subscription/subscription-context";
import type {
  ResearchSession,
  ResearchQueryRequest,
  ResearchResponse,
  FollowUpRequest,
  FollowUpResponse,
  CreateSessionRequest,
  SessionListParams,
  AnalyticsParams,
} from "@/lib/types/legal-research";

// Query Keys
export const legalResearchKeys = {
  all: ['legal-research'] as const,
  sessions: () => [...legalResearchKeys.all, 'sessions'] as const,
  sessionsList: (params?: SessionListParams) => [...legalResearchKeys.sessions(), 'list', params] as const,
  session: (sessionId: string) => [...legalResearchKeys.sessions(), sessionId] as const,
  analytics: (params: AnalyticsParams) => [...legalResearchKeys.all, 'analytics', params] as const,
  jurisdictions: () => [...legalResearchKeys.all, 'jurisdictions'] as const,
  practiceAreas: () => [...legalResearchKeys.all, 'practice-areas'] as const,
};

// Session Management Hooks

/**
 * Hook to list research sessions
 */
export function useResearchSessions(params?: SessionListParams) {
  return useQuery({
    queryKey: legalResearchKeys.sessionsList(params),
    queryFn: () => legalResearchService.listSessions(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get a specific research session
 */
export function useResearchSession(sessionId: string) {
  return useQuery({
    queryKey: legalResearchKeys.session(sessionId),
    queryFn: () => legalResearchService.getSession(sessionId),
    enabled: !!sessionId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to create a new research session
 */
export function useCreateResearchSession() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: CreateSessionRequest) => 
      legalResearchService.createSession(request),
    onSuccess: (newSession) => {
      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: legalResearchKeys.sessions() });
      
      // Add to cache
      queryClient.setQueryData(
        legalResearchKeys.session(newSession.sessionId),
        newSession
      );

      toast({
        title: "Research Session Created",
        description: `Created "${newSession.title}" session`,
      });
    },
    onError: (error: LegalResearchError) => {
      toast({
        title: "Failed to Create Session",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to update a research session
 */
export function useUpdateResearchSession() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ sessionId, updates }: { sessionId: string; updates: Partial<CreateSessionRequest> }) =>
      legalResearchService.updateSession(sessionId, updates),
    onSuccess: (updatedSession) => {
      // Update cache
      queryClient.setQueryData(
        legalResearchKeys.session(updatedSession.sessionId),
        updatedSession
      );

      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: legalResearchKeys.sessions() });

      toast({
        title: "Session Updated",
        description: "Research session updated successfully",
      });
    },
    onError: (error: LegalResearchError) => {
      toast({
        title: "Failed to Update Session",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to delete a research session
 */
export function useDeleteResearchSession() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (sessionId: string) => legalResearchService.deleteSession(sessionId),
    onSuccess: (_, sessionId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: legalResearchKeys.session(sessionId) });
      
      // Invalidate sessions list
      queryClient.invalidateQueries({ queryKey: legalResearchKeys.sessions() });

      toast({
        title: "Session Deleted",
        description: "Research session deleted successfully",
      });
    },
    onError: (error: LegalResearchError) => {
      toast({
        title: "Failed to Delete Session",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Research Query Hooks

/**
 * Hook to perform a research query
 */
export function usePerformResearch() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: ResearchQueryRequest) => 
      legalResearchService.performResearch(request),
    onSuccess: (response, request) => {
      // Update session cache if sessionId is provided
      if (request.sessionId) {
        queryClient.invalidateQueries({ 
          queryKey: legalResearchKeys.session(request.sessionId) 
        });
      }

      // Invalidate sessions list to update query counts
      queryClient.invalidateQueries({ queryKey: legalResearchKeys.sessions() });
    },
    onError: (error: LegalResearchError) => {
      if (error.code === 'INSUFFICIENT_CREDITS') {
        toast({
          title: "Insufficient Credits",
          description: "You don't have enough credits for this research query. Please purchase more credits or upgrade your plan.",
          variant: "destructive",
        });
      } else if (error.code === 'FEATURE_NOT_AVAILABLE') {
        toast({
          title: "Feature Not Available",
          description: "This feature is not available in your current subscription tier. Please upgrade to access AI synthesis.",
          variant: "destructive",
        });
      } else if (error.code === 'RATE_LIMIT_EXCEEDED') {
        toast({
          title: "Rate Limit Exceeded",
          description: `You've reached your hourly query limit. Please try again later.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Research Failed",
          description: error.message,
          variant: "destructive",
        });
      }
    },
  });
}

/**
 * Hook to ask a follow-up question
 */
export function useAskFollowUp() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: FollowUpRequest) => 
      legalResearchService.askFollowUp(request),
    onSuccess: (response, request) => {
      // Update session cache
      queryClient.invalidateQueries({ 
        queryKey: legalResearchKeys.session(request.sessionId) 
      });

      // Invalidate sessions list to update query counts
      queryClient.invalidateQueries({ queryKey: legalResearchKeys.sessions() });
    },
    onError: (error: LegalResearchError) => {
      toast({
        title: "Follow-up Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Analytics Hook

/**
 * Hook to get research analytics (Pro+ tiers only)
 */
export function useResearchAnalytics(params: AnalyticsParams) {
  return useQuery({
    queryKey: legalResearchKeys.analytics(params),
    queryFn: () => legalResearchService.getAnalytics(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry if feature is not available
      if (error instanceof LegalResearchError && error.code === 'FEATURE_NOT_AVAILABLE') {
        return false;
      }
      return failureCount < 3;
    },
  });
}

// Utility Hooks

/**
 * Hook to get available jurisdictions
 */
export function useJurisdictions() {
  return useQuery({
    queryKey: legalResearchKeys.jurisdictions(),
    queryFn: () => legalResearchService.getJurisdictions(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to get available practice areas
 */
export function usePracticeAreas() {
  return useQuery({
    queryKey: legalResearchKeys.practiceAreas(),
    queryFn: () => legalResearchService.getPracticeAreas(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}
/**
 * Hook to check credits for research operations
 */
export function useCheckResearchCredits() {
  const { checkCreditsForFeature, useCreditsForFeature } = useSubscription();
  
  const mutation = useMutation({
    mutationFn: (featureName: string) => checkCreditsForFeature(featureName),
  });

  const processCredits = useCallback(async (featureName: string) => {
    const hasCredits = await checkCreditsForFeature(featureName);
    if (hasCredits) {
      // Note: useCreditsForFeature should be called at component level, not in callback
      // This is a placeholder - actual credit usage should be handled in the component
    }
    return { hasCredits };
  }, [checkCreditsForFeature]);

  const handleQueryCredits = useCallback(async (includeSynthesis: boolean) => {
    try {
      const featureName = includeSynthesis ? 'legal_research_synthesis' : 'legal_research_basic';
      return await checkCreditsForFeature(featureName);
    } catch (_err) {
      return false;
    }
  }, [checkCreditsForFeature]);

  return {
    ...mutation,
    processCredits,
    handleQueryCredits,
    useCreditsForFeature, // Expose this for component-level usage
  };
}

