import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import type { NegotiationEvents, NegotiationSession, SendMoveResponse } from '@/lib/types/chat-negotiation';

interface UseNegotiationWebSocketOptions {
  sessionId?: string;
  enabled?: boolean;
  onSessionCreated?: (session: NegotiationSession) => void;
  onMoveSent?: (response: SendMoveResponse) => void;
  onAiTyping?: (data: { sessionId: string }) => void;
  onSessionUpdated?: (session: Partial<NegotiationSession>) => void;
  onError?: (error: Event) => void;
  onReconnect?: () => void;
}

export function useNegotiationWebSocket(options: UseNegotiationWebSocketOptions = {}) {
  const {
    sessionId,
    enabled = true,
    onSessionCreated,
    onMoveSent,
    onAiTyping,
    onSessionUpdated,
    onError,
    onReconnect,
  } = options;

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const totalAttemptsRef = useRef(0); // Track total connection attempts
  
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [lastError, setLastError] = useState<string | null>(null);

  const maxReconnectAttempts = 3; // Reduced to prevent infinite loops
  const baseReconnectDelay = 1000; // 1 second
  const maxTotalAttempts = 5; // Maximum total connection attempts ever

  // Memoize the WebSocket URL to prevent unnecessary reconnections
  const wsUrl = useMemo(() => {
    if (typeof window === 'undefined') return null;
    
    // For development, disable WebSocket connections to prevent errors
    // This should be replaced with proper WebSocket server configuration
    if (process.env.NODE_ENV === 'development') {
      console.warn('WebSocket connections disabled in development mode');
      return null;
    }
    
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/ws/chat-negotiation${sessionId ? `?sessionId=${sessionId}` : ''}`;
  }, [sessionId]);

  const connect = useCallback(() => {
    if (!enabled || !wsUrl || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    // Extra safety check - only connect if we have a valid sessionId and are on the right page
    if (!sessionId || typeof window === 'undefined') {
      console.warn('WebSocket connection attempted without valid sessionId or in server environment');
      return;
    }

    // Prevent excessive connection attempts
    if (totalAttemptsRef.current >= maxTotalAttempts) {
      console.warn('Maximum WebSocket connection attempts reached, disabling further attempts');
      setConnectionState('error');
      setLastError('WebSocket connection failed after maximum attempts');
      return;
    }

    totalAttemptsRef.current++;

    try {
      setConnectionState('connecting');
      setLastError(null);

      console.log('Attempting WebSocket connection to:', wsUrl, `(attempt ${totalAttemptsRef.current}/${maxTotalAttempts})`);
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('WebSocket connected');
        setConnectionState('connected');
        reconnectAttemptsRef.current = 0;
        
        // Send authentication if needed
        const token = localStorage.getItem('auth_token');
        if (token) {
          ws.send(JSON.stringify({
            type: 'auth',
            token: token
          }));
        }

        onReconnect?.();
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'session:created':
              onSessionCreated?.(data.payload);
              break;
            case 'move:sent':
              onMoveSent?.(data.payload);
              break;
            case 'ai:typing':
              onAiTyping?.(data.payload);
              break;
            case 'session:updated':
              onSessionUpdated?.(data.payload);
              break;
            case 'error':
              console.error('WebSocket error:', data.payload);
              setLastError(data.payload.message || 'WebSocket error');
              break;
            default:
              console.log('Unknown WebSocket message type:', data.type);
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setConnectionState('disconnected');
        wsRef.current = null;

        // Attempt to reconnect if it wasn't a clean close and we haven't hit limits
        if (enabled &&
            event.code !== 1000 &&
            reconnectAttemptsRef.current < maxReconnectAttempts &&
            totalAttemptsRef.current < maxTotalAttempts) {
          const delay = baseReconnectDelay * Math.pow(2, reconnectAttemptsRef.current);
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptsRef.current++;
            connect();
          }, delay);
        } else if (totalAttemptsRef.current >= maxTotalAttempts) {
          console.warn('Maximum total WebSocket connection attempts reached');
          setConnectionState('error');
          setLastError('WebSocket connection failed after maximum attempts');
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionState('error');
        setLastError('WebSocket connection error - server may not be configured');
        onError?.(error);
        
        // Prevent further connection attempts on error
        if (wsRef.current) {
          wsRef.current.close();
          wsRef.current = null;
        }
      };

      wsRef.current = ws;
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionState('error');
      setLastError('Failed to create WebSocket connection');
    }
  }, [enabled, wsUrl, onSessionCreated, onMoveSent, onAiTyping, onSessionUpdated, onError, onReconnect]);

  const disconnect = useCallback(() => {
    console.log('Disconnecting WebSocket...');
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      try {
        if (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING) {
          wsRef.current.close(1000, 'Component unmounting');
        }
      } catch (error) {
        console.warn('Error closing WebSocket:', error);
      }
      wsRef.current = null;
    }

    setConnectionState('disconnected');
    reconnectAttemptsRef.current = 0;
  }, []);

  const sendMessage = useCallback((type: keyof NegotiationEvents, payload: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type, payload }));
      return true;
    }
    return false;
  }, []);

  const forceReconnect = useCallback(() => {
    disconnect();
    reconnectAttemptsRef.current = 0;
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  // Connect when enabled and sessionId changes
  useEffect(() => {
    // Only connect if we're explicitly enabled AND have a valid sessionId AND wsUrl exists
    if (enabled && sessionId && wsUrl && typeof window !== 'undefined') {
      // Check if we're actually on a session page to prevent unnecessary connections
      const isSessionPage = window.location.pathname.includes('/sessions/');
      
      if (isSessionPage && totalAttemptsRef.current < maxTotalAttempts) {
        // Add a small delay to prevent rapid reconnections
        const timer = setTimeout(() => {
          connect();
        }, 500); // Increased delay
        
        return () => {
          clearTimeout(timer);
          disconnect();
        };
      }
    }
    
    // Always disconnect if conditions are not met
    if (!enabled || !sessionId || !wsUrl) {
      disconnect();
    }
    
    return () => {
      // Cleanup on unmount or dependency change
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, [enabled, sessionId, wsUrl]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connectionState,
    lastError,
    isConnected: connectionState === 'connected',
    isConnecting: connectionState === 'connecting',
    hasError: connectionState === 'error',
    sendMessage,
    connect,
    disconnect,
    forceReconnect,
  };
}

export default useNegotiationWebSocket;