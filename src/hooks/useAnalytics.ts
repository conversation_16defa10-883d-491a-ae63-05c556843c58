// src/hooks/useAnalytics.ts
import { posthog } from '../providers/PostHogProvider';

export const useAnalytics = () => {
  const trackEvent = (eventName: string, properties?: Record<string, any>) => {
    if (posthog && typeof posthog.capture === 'function') {
      posthog.capture(eventName, properties);
    } else {
      console.warn('PostHog not initialized, event not tracked:', eventName, properties);
    }
  };

  const identifyUser = (userId: string, properties?: Record<string, any>) => {
    if (posthog && typeof posthog.identify === 'function') {
      posthog.identify(userId, properties);
    } else {
      console.warn('PostHog not initialized, user not identified:', userId, properties);
    }
  };

  return {
    trackEvent,
    identifyUser,
  };
};
