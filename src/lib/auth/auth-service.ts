import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { setCookie, getCookie, deleteCookie } from "cookies-next";
import { apiClient, COOKIE_OPTIONS, AUTH_CONFIG } from "@/lib/config";
import { posthog } from '../../providers/PostHogProvider'; // Import PostHog instance
import { AxiosError } from "axios";

interface LoginRequest {
  email: string;
  password: string;
  mfaToken?: string;
}

interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

interface AuthUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  roles: string[];
  organizationId: string;
  organizationName?: string;
  mfaEnabled: boolean;
  emailVerified?: boolean;
  isGoogleUser?: boolean;
  picture?: string;
  lastLoginAt?: string;
  createdAt?: string;
  subscriptionTier?: string;
  subscriptionStatus?: string;
  twoFactorEnabled?: boolean;
  twoFactorVerified?: boolean;
}

interface AuthResponse {
  token: string;
  user: AuthUser;
}

interface TokenRefreshResponse {
  token: string;
}

interface MFAStartResponse {
  tempToken: string;
  methods: Array<"app" | "sms">;
}

interface MFAValidateRequest {
  tempToken: string;
  code: string;
  method: "app" | "sms";
}

export class AuthError extends Error {
  constructor(
    message: string,
    public code:
      | "INVALID_CREDENTIALS"
      | "MFA_REQUIRED"
      | "INVALID_TOKEN"
      | "TOKEN_EXPIRED"
      | "NETWORK_ERROR"
      | "EMAIL_EXISTS"
      | "INVALID_REQUEST",
    public status?: number
  ) {
    super(message);
    this.name = "AuthError";
  }
}

// API Functions
async function registerRequest(data: RegisterRequest): Promise<AuthResponse> {
  try {
    const response = await apiClient.post<AuthResponse>(
      `/auth/register`,
      data,
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    const result = response.data;

    // Store auth data in cookies
    setCookie(AUTH_CONFIG.accessTokenKey, result.token, COOKIE_OPTIONS);
    setCookie(
      AUTH_CONFIG.organizationKey,
      result.user.organizationId,
      COOKIE_OPTIONS
    );
    setCookie(AUTH_CONFIG.userKey, JSON.stringify(result.user), COOKIE_OPTIONS);

    if (posthog && typeof posthog.identify === 'function' && typeof posthog.capture === 'function' && result.user) {
      posthog.identify(result.user.id, {
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        organizationId: result.user.organizationId,
        organizationName: result.user.organizationName,
        subscriptionTier: result.user.subscriptionTier,
        roles: result.user.roles,
      });
      posthog.capture('user_registered', { userId: result.user.id, organizationId: result.user.organizationId });
    } else if (!posthog || typeof posthog.identify !== 'function' || typeof posthog.capture !== 'function') {
      console.warn('PostHog not fully initialized or methods missing; skipping event tracking in auth-service for registerRequest.');
    }

    return result;
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.response?.status === 409) {
        throw new AuthError(
          error.response.data?.message || "Email already exists",
          "EMAIL_EXISTS",
          error.response.status
        );
      }
      throw new AuthError(
        error.response?.data?.message || "Registration failed",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

async function loginRequest(data: LoginRequest): Promise<AuthResponse> {
  try {
    const response = await apiClient.post<AuthResponse>(`/auth/login`, data, {
      headers: { "Content-Type": "application/json" },
    });

    const result = response.data;

    // Debug logs for token storage
    console.log("[Auth Debug] Token received from backend:", result.token);
    console.log("[Auth Debug] Storing token in localStorage...");

    // Store auth data in cookies and localStorage
    localStorage.setItem("token", result.token);
    setCookie(AUTH_CONFIG.accessTokenKey, result.token, COOKIE_OPTIONS);
    setCookie(
      AUTH_CONFIG.organizationKey,
      result.user.organizationId,
      COOKIE_OPTIONS
    );
    setCookie(AUTH_CONFIG.userKey, JSON.stringify(result.user), COOKIE_OPTIONS);

    // Debug logs for cookie storage
    console.log("[Auth Debug] Cookies set:", {
      token: getCookie(AUTH_CONFIG.accessTokenKey),
      organization: getCookie(AUTH_CONFIG.organizationKey),
      user: getCookie(AUTH_CONFIG.userKey),
    });

    if (posthog && result.user) {
      posthog.identify(result.user.id, {
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        organizationId: result.user.organizationId,
        organizationName: result.user.organizationName,
        subscriptionTier: result.user.subscriptionTier,
        roles: result.user.roles,
      });
      posthog.capture('user_logged_in', { userId: result.user.id, loginMethod: data.mfaToken ? 'mfa' : 'password' });
    }

    return result;
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        throw new AuthError(
          error.response.data?.message || "Invalid credentials",
          "INVALID_CREDENTIALS",
          error.response.status
        );
      }
      if (
        error.response?.status === 403 &&
        error.response.data?.code === "MFA_REQUIRED"
      ) {
        throw new AuthError(
          "MFA verification required",
          "MFA_REQUIRED",
          error.response.status
        );
      }
      throw new AuthError(
        error.response?.data?.message || "Login failed",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

async function refreshTokenRequest(): Promise<TokenRefreshResponse> {
  try {
    console.log("[Auth Debug] Attempting token refresh...");
    const response = await apiClient.post<TokenRefreshResponse>(
      `/auth/refresh`,
      {},
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    const result = response.data;
    setCookie(AUTH_CONFIG.accessTokenKey, result.token, COOKIE_OPTIONS);
    return result;
  } catch (error) {
    console.log("[Auth Debug] Token refresh failed:", error);
    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        if (error.response.data?.message?.includes("jwt expired")) {
          clearAuthData();
          if (
            typeof window !== "undefined" &&
            !window.location.pathname.includes(AUTH_CONFIG.routes.login)
          ) {
            window.location.href = `${AUTH_CONFIG.routes.login}?return_to=${AUTH_CONFIG.routes.chat}`;
          }
          throw new AuthError(
            "Authentication session expired",
            "TOKEN_EXPIRED",
            error.response.status
          );
        }
      }
      throw new AuthError(
        "Failed to refresh token",
        "INVALID_TOKEN",
        error.response?.status
      );
    }
    throw error;
  }
}

async function startMFARequest(email: string): Promise<MFAStartResponse> {
  try {
    const response = await apiClient.post<MFAStartResponse>(
      `/auth/mfa/start`,
      { email },
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AuthError(
        error.response?.data?.message || "Failed to start MFA process",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

async function logoutRequest(): Promise<void> {
  console.log("Logout request initiated");
  const token = getCookie(AUTH_CONFIG.accessTokenKey);
  if (!token) {
    console.log("No token found, clearing auth data");
    clearAuthData(); // Clear any remaining data
    return; // Already logged out
  }

  try {
    console.log("Making logout API call to /auth/logout");
    // The apiClient automatically adds the Authorization header
    await apiClient.post(`/auth/logout`, {});
    console.log("Logout API call successful");
    clearAuthData(); // Clear all auth cookies
  } catch (error) {
    // Still clear cookies even if the request fails
    console.error("Logout request failed:", error);
    clearAuthData();
    // Don't throw the error - we still want to clear local data
  }
}

async function validateMFARequest(
  data: MFAValidateRequest
): Promise<AuthResponse> {
  try {
    const response = await apiClient.post<AuthResponse>(
      `/auth/mfa/validate`,
      data,
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    const result = response.data;

    // Store auth data in cookies
    setCookie(AUTH_CONFIG.accessTokenKey, result.token, COOKIE_OPTIONS);
    setCookie(
      AUTH_CONFIG.organizationKey,
      result.user.organizationId,
      COOKIE_OPTIONS
    );
    setCookie(AUTH_CONFIG.userKey, JSON.stringify(result.user), COOKIE_OPTIONS);

    if (posthog && typeof posthog.identify === 'function' && typeof posthog.capture === 'function' && result.user) {
      posthog.identify(result.user.id, {
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        organizationId: result.user.organizationId,
        organizationName: result.user.organizationName,
        subscriptionTier: result.user.subscriptionTier,
        roles: result.user.roles,
      });
      posthog.capture('user_mfa_validated', { userId: result.user.id, method: data.method });
    } else if (!posthog || typeof posthog.identify !== 'function' || typeof posthog.capture !== 'function') {
      console.warn('PostHog not fully initialized or methods missing; skipping event tracking in auth-service for validateMFARequest.');
    }

    return result;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AuthError(
        "Invalid MFA code",
        "INVALID_CREDENTIALS",
        error.response?.status
      );
    }
    throw error;
  }
}

async function fetchUserProfile(): Promise<AuthUser> {
  // Check if we have a token
  const token = getCookie(AUTH_CONFIG.accessTokenKey);

  if (!token) {
    throw new AuthError(
      "No access token found",
      "INVALID_TOKEN"
    );
  }

  try {
    console.log("[Auth Debug] Fetching user profile...");
    const response = await apiClient.get<AuthUser>(`/auth/profile`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const result = response.data;

    // Update stored user data with the latest profile information
    setCookie(AUTH_CONFIG.userKey, JSON.stringify(result), COOKIE_OPTIONS);

    return result;
  } catch (error) {
    console.log("[Auth Debug] Profile fetch error:", error);
    if (error instanceof AxiosError) {
      console.log("[Auth Debug] Response status:", error.response?.status);
      console.log("[Auth Debug] Response data:", error.response?.data);
    }
    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        if (error.response.data?.message?.includes("jwt expired")) {
          clearAuthData();
          if (
            typeof window !== "undefined" &&
            !window.location.pathname.includes(AUTH_CONFIG.routes.login)
          ) {
            window.location.href = `${AUTH_CONFIG.routes.login}?return_to=${AUTH_CONFIG.routes.chat}`;
          }
          throw new AuthError(
            "Authentication session expired",
            "TOKEN_EXPIRED",
            error.response.status
          );
        }
      }
      throw new AuthError(
        "Failed to fetch user profile",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

// Email verification functions
async function verifyEmailRequest(
  token: string
): Promise<{ success: boolean; message: string }> {
  try {
    const response = await apiClient.get<{ success: boolean; message: string }>(
      `/auth/verify-email?token=${token}`
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AuthError(
        error.response?.data?.message || "Email verification failed",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

async function resendVerificationEmailRequest(
  email: string
): Promise<{ success: boolean; message: string }> {
  try {
    const response = await apiClient.post<{
      success: boolean;
      message: string;
    }>(
      `/auth/resend-verification`,
      { email },
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.response?.status === 404) {
        throw new AuthError(
          error.response.data?.message || "User not found",
          "NETWORK_ERROR",
          error.response.status
        );
      }
      throw new AuthError(
        error.response?.data?.message || "Failed to resend verification email",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

// Profile update function
interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  picture?: string;
}

async function updateProfileRequest(data: UpdateProfileRequest): Promise<AuthUser> {
  // Check if we have a token
  const token = getCookie(AUTH_CONFIG.accessTokenKey);

  if (!token) {
    throw new AuthError(
      "No access token found",
      "INVALID_TOKEN"
    );
  }

  try {
    const response = await apiClient.put<AuthUser>(`/auth/profile`, data, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const result = response.data;

    // Update stored user data with the latest profile information
    setCookie(AUTH_CONFIG.userKey, JSON.stringify(result), COOKIE_OPTIONS);

    return result;
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.response?.status === 400) {
        throw new AuthError(
          error.response.data?.message || "Invalid profile update request",
          "INVALID_REQUEST",
          error.response.status
        );
      }
      if (error.response?.status === 401) {
        clearAuthData();
        if (
          typeof window !== "undefined" &&
          !window.location.pathname.includes(AUTH_CONFIG.routes.login)
        ) {
          window.location.href = `${AUTH_CONFIG.routes.login}?return_to=${window.location.pathname}`;
        }
        throw new AuthError(
          "Authentication session expired",
          "TOKEN_EXPIRED",
          error.response.status
        );
      }
      throw new AuthError(
        "Failed to update profile",
        "NETWORK_ERROR",
        error.response?.status
      );
    }
    throw error;
  }
}

// Query Hooks
function useRegister() {
  const queryClient = useQueryClient();

  return useMutation<AuthResponse, AuthError, RegisterRequest>({
    mutationFn: registerRequest,
    onSuccess: (data) => {
      queryClient.setQueryData(["user"], data.user);
    },
  });
}

function useLogin() {
  const queryClient = useQueryClient();

  return useMutation<AuthResponse, AuthError, LoginRequest>({
    mutationFn: loginRequest,
    onSuccess: (data) => {
      queryClient.setQueryData(["user"], data.user);
    },
  });
}

function useRefreshToken() {
  return useMutation<TokenRefreshResponse, AuthError>({
    mutationFn: refreshTokenRequest,
  });
}

function useMFAStart() {
  return useMutation<MFAStartResponse, AuthError, string>({
    mutationFn: startMFARequest,
  });
}

function useMFAValidate() {
  const queryClient = useQueryClient();

  return useMutation<AuthResponse, AuthError, MFAValidateRequest>({
    mutationFn: validateMFARequest,
    onSuccess: (data) => {
      queryClient.setQueryData(["user"], data.user);
    },
  });
}

function useLogout() {
  const queryClient = useQueryClient();

  return useMutation<void, Error>({
    mutationFn: logoutRequest,
    onSuccess: () => {
      queryClient.clear(); // Clear all query cache
      window.location.href = `${AUTH_CONFIG.routes.login}?return_to=${AUTH_CONFIG.routes.chat}`; // Redirect to login page with return path
    },
  });
}

function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation<AuthUser, Error, UpdateProfileRequest>({
    mutationFn: (data: UpdateProfileRequest) => updateProfileRequest(data),
    onSuccess: (data: AuthUser) => {
      // Update the user data in the cache
      queryClient.setQueryData(["user"], data);
      queryClient.setQueryData(["profile"], data);
    },
  });
}

// Email verification hooks
function useVerifyEmail() {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean; message: string }, AuthError, string>({
    mutationFn: (token: string) => verifyEmailRequest(token),
    onSuccess: () => {
      // Invalidate user query to refresh user data with updated verification status
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
}

function useResendVerificationEmail() {
  return useMutation<{ success: boolean; message: string }, AuthError, string>({
    mutationFn: resendVerificationEmailRequest,
  });
}

// User data fetch hook
function useUser() {
  return useQuery<AuthUser | null>({
    queryKey: ["user"],
    queryFn: async () => {
      // Debug logs for token validation
      console.log("[Auth Debug] Checking access token:", hasAccessToken());
      const token = getCookie(AUTH_CONFIG.accessTokenKey);
      console.log("[Auth Debug] Current token from cookie:", token);

      try {
        // Try to fetch fresh profile if we have a token
        if (hasAccessToken()) {
          return await fetchUserProfile();
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
      }

      // Fall back to stored user data
      const userStr = getCookie(AUTH_CONFIG.userKey)?.toString();
      if (!userStr) return null;
      return JSON.parse(userStr);
    },
  });
}

// Profile fetch hook
function useProfile() {
  return useQuery<AuthUser>({
    queryKey: ["profile"],
    queryFn: fetchUserProfile,
    enabled: hasAccessToken(), // Only run if we have a token
  });
}

// Helper to check if access token exists
function hasAccessToken(): boolean {
  return !!getCookie(AUTH_CONFIG.accessTokenKey);
}

// Helper to get stored tokens and tenant ID
function getStoredAuth() {
  return {
    accessToken: getCookie(AUTH_CONFIG.accessTokenKey)?.toString(),
    organizationId: getCookie(AUTH_CONFIG.organizationKey)?.toString(),
  };
}

// Helper to clear auth data
function clearAuthData() {
  localStorage.removeItem("token");
  deleteCookie(AUTH_CONFIG.accessTokenKey);
  deleteCookie(AUTH_CONFIG.organizationKey);
  deleteCookie(AUTH_CONFIG.userKey);
}

export {
  useRegister,
  useLogin,
  useRefreshToken,
  useMFAStart,
  useMFAValidate,
  useLogout,
  useVerifyEmail,
  useResendVerificationEmail,
  useUser,
  useProfile,
  useUpdateProfile,
  hasAccessToken,
  getStoredAuth,
  clearAuthData,
};
