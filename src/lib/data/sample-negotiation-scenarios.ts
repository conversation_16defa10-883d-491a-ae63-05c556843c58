import type { NegotiationScenario } from '@/lib/types/chat-negotiation';

export const sampleNegotiationScenarios: NegotiationScenario[] = [
  {
    id: '1',
    title: 'Software License Agreement',
    description: 'Negotiate terms for a enterprise software licensing deal with a tech company.',
    difficulty: 3,
    category: 'Technology',
    estimatedDuration: 15,
    objectives: [
      'Secure favorable pricing terms',
      'Negotiate payment schedule',
      'Define support and maintenance terms',
      'Establish data security requirements'
    ],
    context: {
      background: 'Your company needs enterprise software for 500 employees. The vendor has quoted $100,000 annually with standard terms.',
      yourRole: 'Procurement Manager at a mid-size manufacturing company',
      counterpartRole: 'Sales Director at a software company',
      stakes: 'This software is critical for your digital transformation initiative. Budget constraints require careful negotiation.'
    },
    isUnlocked: true
  },
  {
    id: '2',
    title: 'Merger & Acquisition Deal',
    description: 'Navigate complex negotiations for acquiring a smaller competitor company.',
    difficulty: 8,
    category: 'Corporate',
    estimatedDuration: 30,
    objectives: [
      'Agree on company valuation',
      'Structure the deal (cash vs. equity)',
      'Define transition timeline',
      'Negotiate key personnel retention',
      'Address regulatory compliance'
    ],
    context: {
      background: 'You represent a growing tech company looking to acquire a smaller competitor with valuable IP and talent.',
      yourRole: 'Chief Business Development Officer',
      counterpartRole: 'CEO and Founder of target company',
      stakes: 'This acquisition could accelerate your market position by 2-3 years and provide access to key patents.'
    },
    isUnlocked: true
  },
  {
    id: '3',
    title: 'Commercial Real Estate Lease',
    description: 'Secure favorable terms for a new office space in a prime downtown location.',
    difficulty: 5,
    category: 'Real Estate',
    estimatedDuration: 20,
    objectives: [
      'Negotiate competitive rent rates',
      'Secure tenant improvement allowance',
      'Establish favorable lease terms',
      'Include expansion options'
    ],
    context: {
      background: 'Your growing startup needs to move from a co-working space to a dedicated office for 50 employees.',
      yourRole: 'Head of Operations at a tech startup',
      counterpartRole: 'Commercial Real Estate Broker',
      stakes: 'The lease terms will significantly impact your burn rate and ability to scale the team.'
    },
    isUnlocked: true
  },
  {
    id: '4',
    title: 'International Trade Agreement',
    description: 'Negotiate import/export terms with an overseas manufacturing partner.',
    difficulty: 6,
    category: 'International',
    estimatedDuration: 25,
    objectives: [
      'Establish pricing and payment terms',
      'Define quality standards and inspection',
      'Negotiate shipping and logistics',
      'Address currency exchange risks',
      'Set up dispute resolution process'
    ],
    context: {
      background: 'You need to establish a supply chain partnership with a manufacturer in Southeast Asia for your consumer products.',
      yourRole: 'Supply Chain Director',
      counterpartRole: 'Export Manager at manufacturing company',
      stakes: 'This partnership could reduce manufacturing costs by 30% but requires careful risk management.'
    },
    isUnlocked: false
  },
  {
    id: '5',
    title: 'Executive Compensation Package',
    description: 'Negotiate compensation and benefits for a senior executive position.',
    difficulty: 4,
    category: 'HR',
    estimatedDuration: 18,
    objectives: [
      'Agree on base salary and bonus structure',
      'Negotiate equity compensation',
      'Define benefits and perks',
      'Establish performance metrics',
      'Address severance terms'
    ],
    context: {
      background: 'You are being recruited for a VP of Engineering role at a fast-growing SaaS company.',
      yourRole: 'Experienced Engineering Executive (candidate)',
      counterpartRole: 'Head of Talent Acquisition',
      stakes: 'This is a career-defining opportunity that requires balancing risk and reward in the compensation package.'
    },
    isUnlocked: true
  },
  {
    id: '6',
    title: 'Partnership Joint Venture',
    description: 'Structure a strategic partnership between two companies in different markets.',
    difficulty: 7,
    category: 'Strategic',
    estimatedDuration: 35,
    objectives: [
      'Define partnership structure and governance',
      'Establish revenue sharing model',
      'Allocate risks and responsibilities',
      'Create intellectual property framework',
      'Set partnership duration and exit terms'
    ],
    context: {
      background: 'Your fintech company wants to partner with a traditional bank to offer embedded financial services.',
      yourRole: 'Head of Partnerships',
      counterpartRole: 'Director of Innovation at regional bank',
      stakes: 'This partnership could open up a new customer segment worth $50M+ in potential revenue.'
    },
    isUnlocked: false
  }
];

// Helper function to get scenarios by difficulty
export const getScenariosByDifficulty = (difficulty: 'beginner' | 'intermediate' | 'advanced') => {
  const difficultyMap = {
    beginner: [1, 2, 3],
    intermediate: [4, 5, 6],
    advanced: [7, 8, 9, 10]
  };
  
  return sampleNegotiationScenarios.filter(scenario => 
    difficultyMap[difficulty].includes(scenario.difficulty)
  );
};

// Helper function to get scenarios by category
export const getScenariosByCategory = (category: string) => {
  return sampleNegotiationScenarios.filter(scenario => 
    scenario.category.toLowerCase() === category.toLowerCase()
  );
};

// Helper function to get unlocked scenarios
export const getUnlockedScenarios = () => {
  return sampleNegotiationScenarios.filter(scenario => scenario.isUnlocked);
};