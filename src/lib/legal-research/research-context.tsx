"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { legalResearchService } from "@/lib/services/legal-research-service";
import {
  LegalResearchError
} from "@/lib/types/legal-research";
import type {
  ResearchSession,
  ResearchMessage,
  ResearchOptions,
  RESEARCH_FEATURE_COSTS,
} from "@/lib/types/legal-research";
import { useCheckResearchCredits } from "@/hooks/use-legal-research";

interface ResearchContextType {
  // Session state
  currentSession: ResearchSession | null;
  messages: ResearchMessage[];
  isLoading: boolean;
  error: Error | null;

  // Session management
  createSession: (title?: string) => Promise<ResearchSession | undefined>;
  loadSession: (sessionId: string) => Promise<void>;
  clearSession: () => void;

  // Research operations
  sendResearchQuery: (
    query: string,
    options?: ResearchOptions,
    sessionOverride?: ResearchSession,
    onSessionCreated?: (session: ResearchSession) => void,
  ) => Promise<void>;
  askFollowUp: (question: string) => Promise<void>;

  // UI state
  isSearching: boolean;
  isSynthesizing: boolean;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  isMobileSidebarOpen: boolean;
  setIsMobileSidebarOpen: (isOpen: boolean) => void;

  // Credit management
  checkCreditsForQuery: (includeSynthesis: boolean) => Promise<boolean>;
  
  // Error handling
  setError: (error: Error | null) => void;
}

const ResearchContext = createContext<ResearchContextType | undefined>(undefined);

export function useResearchContext() {
  const context = useContext(ResearchContext);
  if (context === undefined) {
    throw new Error("useResearchContext must be used within a ResearchProvider");
  }
  return context;
}

export function ResearchProvider({ children }: { children: React.ReactNode }) {
  // Core state
  const [currentSession, setCurrentSession] = useState<ResearchSession | null>(null);
  const [messages, setMessages] = useState<ResearchMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // UI state
  const [isSearching, setIsSearching] = useState(false);
  const [isSynthesizing, setIsSynthesizing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Use custom research credits hook
  const { processCredits, handleQueryCredits } = useCheckResearchCredits();

  /**
   * Create a new research session
   */
  const createSession = useCallback(async (title?: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const sessionTitle =
        title || `Research Session ${new Date().toLocaleDateString()}`;
      const session = await legalResearchService.createSession({
        title: sessionTitle,
        tags: [],
        isShared: false,
      });

      setCurrentSession(session);
      setMessages([]);
      return session;
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error("Failed to create session");
      setError(error);
      return undefined;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Load an existing research session
   */
  const loadSession = useCallback(async (sessionId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const session = await legalResearchService.getSession(sessionId);
      setCurrentSession(session);

      // Convert session queries to messages
      const sessionMessages: ResearchMessage[] = [];

      if (session.queries) {
        for (const query of session.queries) {
          // Add user message
          sessionMessages.push({
            id: `${query.queryId}-user`,
            role: 'user',
            content: query.query || query.question || '',
            timestamp: query.timestamp,
            queryType: query.type === 'initial' ? 'research' : 'followup',
            queryId: query.queryId,
            sessionId: session.sessionId,
          });

          // Add assistant response with full data
          sessionMessages.push({
            id: `${query.queryId}-assistant`,
            role: 'assistant',
            content: query.aiSynthesis?.legalAnalysis.text || query.resultSummary,
            timestamp: query.timestamp,
            searchResults: query.searchResults,
            aiSynthesis: query.aiSynthesis,
            followUpSuggestions: query.followUpSuggestions,
            creditsUsed: query.creditsUsed,
            queryType: query.type === 'initial' ? 'research' : 'followup',
            queryId: query.queryId,
            sessionId: session.sessionId,
          });
        }
      }

      setMessages(sessionMessages.reverse()); // Most recent first
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to load session");
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Clear current session and messages
   */
  const clearSession = useCallback(() => {
    setCurrentSession(null);
    setMessages([]);
    setError(null);
    setIsSearching(false);
    setIsSynthesizing(false);
  }, []);

  /**
   * Check if user has sufficient credits for a query
   */
  const checkCreditsForQuery = useCallback(async (includeSynthesis: boolean): Promise<boolean> => {
    return await handleQueryCredits(includeSynthesis);
  }, [handleQueryCredits]);

  /**
   * Send a research query
   */
  const sendResearchQuery = useCallback(
    async (
      query: string,
      options?: ResearchOptions,
      sessionOverride?: ResearchSession,
      onSessionCreated?: (session: ResearchSession) => void,
    ) => {
      let sessionToUse = sessionOverride || currentSession;
      let wasSessionCreated = false;
      
      // Auto-create session if none exists
      if (!sessionToUse) {
        const sessionTitle = `Research: ${query.substring(0, 50)}${query.length > 50 ? '...' : ''}`;
        sessionToUse = await createSession(sessionTitle);
        if (!sessionToUse) {
          throw new Error("Failed to create research session");
        }
        wasSessionCreated = true;
        onSessionCreated?.(sessionToUse);
      }

      // Check credits first
      const hasCredits = await checkCreditsForQuery(
        options?.includeSynthesis ?? true,
      );
      if (!hasCredits) {
        throw new LegalResearchError(
          "Insufficient credits for this research query",
          "INSUFFICIENT_CREDITS",
        );
      }

      // Create optimistic user message
      const optimisticMessage: ResearchMessage = {
        id: `temp-${Date.now()}`,
        content: query,
        role: "user",
        timestamp: new Date().toISOString(),
        queryType: "research",
        sessionId: sessionToUse.sessionId,
      };

      // Add optimistic message immediately
      setMessages((prev) => [optimisticMessage, ...prev]);

      try {
        setIsLoading(true);
        setIsSearching(true);
        if (options?.includeSynthesis) {
          setIsSynthesizing(true);
        }

        // Send the research query
        const response = await legalResearchService.performResearch({
          query,
          options,
          sessionId: sessionToUse.sessionId,
        });

        const featureName = options?.includeSynthesis
          ? "legal_research_synthesis"
          : "legal_research_basic";
        await processCredits(featureName);

        // Create assistant response message
        const assistantMessage: ResearchMessage = {
          id: response.data.queryId,
          role: "assistant",
          content:
            response.data.aiSynthesis?.legalAnalysis.text ||
            "Research completed",
          timestamp: response.data.metadata.timestamp,
          searchResults: response.data.searchResults,
          aiSynthesis: response.data.aiSynthesis,
          followUpSuggestions: response.data.followUpSuggestions,
          creditsUsed: response.data.metadata.creditsUsed,
          queryType: "research",
          queryId: response.data.queryId,
          sessionId: response.data.sessionId,
        };

        // Update messages - remove optimistic and add real messages
        setMessages((prev) => [
          assistantMessage,
          {
            ...optimisticMessage,
            id: `${response.data.queryId}-user`,
            queryId: response.data.queryId,
          },
          ...prev.filter((msg) => msg.id !== optimisticMessage.id),
        ]);

        // Update current session
        setCurrentSession((prev) => {
          const updatedSession = prev && prev.sessionId === response.data.sessionId
            ? {
                ...prev,
                queryCount: prev.queryCount + 1,
                totalCreditsUsed:
                  prev.totalCreditsUsed + response.data.metadata.creditsUsed,
                updatedAt: response.data.metadata.timestamp,
              }
            : sessionToUse;
          
          return updatedSession;
        });
      } catch (err) {
        const error =
          err instanceof Error
            ? err
            : new Error("Failed to send research query");
        setError(error);

        // Remove optimistic message on failure
        setMessages((prev) =>
          prev.filter((msg) => msg.id !== optimisticMessage.id),
        );
      } finally {
        setIsLoading(false);
        setIsSearching(false);
        setIsSynthesizing(false);
      }
    },
    [currentSession, checkCreditsForQuery, processCredits],
  );

  /**
   * Ask a follow-up question
   */
  const askFollowUp = useCallback(async (question: string) => {
    if (!currentSession) {
      throw new Error("No active research session");
    }

    // Check credits for follow-up
    const hasCredits = await checkCreditsForQuery(false); // Follow-ups are basic queries
    if (!hasCredits) {
      throw new LegalResearchError(
        "Insufficient credits for follow-up question",
        "INSUFFICIENT_CREDITS"
      );
    }

    // Create optimistic user message
    const optimisticMessage: ResearchMessage = {
      id: `temp-${Date.now()}`,
      content: question,
      role: "user",
      timestamp: new Date().toISOString(),
      queryType: "followup",
      sessionId: currentSession.sessionId,
    };

    setMessages((prev) => [optimisticMessage, ...prev]);

    try {
      setIsSearching(true);

      const response = await legalResearchService.askFollowUp({
        sessionId: currentSession.sessionId,
        question,
        options: {
          includeSynthesis: true,
          focusOnPrevious: true,
        },
      });

      await processCredits('legal_research_followup');

      // Create assistant response
      const assistantMessage: ResearchMessage = {
        id: response.data.queryId,
        role: "assistant",
        content: response.data.aiSynthesis?.legalAnalysis.text || "Follow-up completed",
        timestamp: response.data.metadata.timestamp,
        searchResults: response.data.searchResults,
        aiSynthesis: response.data.aiSynthesis,
        followUpSuggestions: response.data.followUpSuggestions,
        creditsUsed: response.data.metadata.creditsUsed,
        queryType: "followup",
        queryId: response.data.queryId,
        sessionId: response.data.sessionId,
      };

      // Update messages
      setMessages((prev) => [
        assistantMessage,
        {
          ...optimisticMessage,
          id: `${response.data.queryId}-user`,
          queryId: response.data.queryId,
        },
        ...prev.filter((msg) => msg.id !== optimisticMessage.id),
      ]);

      // Update session
      setCurrentSession((prev) => prev ? {
        ...prev,
        queryCount: prev.queryCount + 1,
        totalCreditsUsed: prev.totalCreditsUsed + response.data.metadata.creditsUsed,
        updatedAt: response.data.metadata.timestamp,
      } : null);

    } catch (err) {
      setMessages((prev) => prev.filter((msg) => msg.id !== optimisticMessage.id));
      
      const error = err instanceof Error ? err : new Error("Failed to ask follow-up question");
      setError(error);
      throw error;
    } finally {
      setIsSearching(false);
    }
  }, [currentSession, checkCreditsForQuery, processCredits]);

  const value: ResearchContextType = {
    currentSession,
    messages,
    isLoading,
    error,
    createSession,
    loadSession,
    clearSession,
    sendResearchQuery,
    askFollowUp,
    isSearching,
    isSynthesizing,
    showFilters,
    setShowFilters,
    isMobileSidebarOpen,
    setIsMobileSidebarOpen,
    checkCreditsForQuery,
    setError,
  };

  return (
    <ResearchContext.Provider value={value}>
      {children}
    </ResearchContext.Provider>
  );
}
