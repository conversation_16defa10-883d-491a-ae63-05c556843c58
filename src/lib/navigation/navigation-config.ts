import {
  FileText,
  MessageSquare,
  BarChart,
  Search,
  GitCompare,
  LineChart,
  Users,
  Shield,
  BookOpen,
  Sparkles,
  Upload,
  Database,
  Gavel,
  GraduationCap,
  Zap,
  Settings,
} from "lucide-react";
import type React from "react";

export interface RouteItem {
  label: string;
  href: string;
  icon: React.ElementType;
  active?: boolean;
  pro?: boolean;
}

export interface RouteCategory {
  title: string;
  icon: React.ElementType;
  routes: Omit<RouteItem, 'active'>[];
  defaultOpen?: boolean;
}

// Standalone routes - main navigation
export const STANDALONE_ROUTES: Omit<RouteItem, 'active'>[] = [
  {
    label: "Your Command Center",
    icon: BarChart,
    href: "/dashboard",
  },
  {
    label: "Chat With Docs",
    icon: MessageSquare,
    href: "/chat",
  },
  {
    label: "Legal Research",
    icon: Search,
    href: "/legal-research",
  },
  {
    label: "Your Billable Hours Report",
    icon: Line<PERSON>hart,
    href: "/analytics",
  },
];

// Production route categories
export const ROUTE_CATEGORIES: RouteCategory[] = [
  {
    title: "Your Documents",
    icon: FileText,
    defaultOpen: true,
    routes: [
      {
        label: "Document Library",
        href: "/documents",
        icon: Database,
      },
      {
        label: "Upload Documents",
        href: "/documents/upload",
        icon: Upload,
      },
    
      {
        label: "Bulk Document Analysis",
        href: "/analyze/multiple",
        icon: BarChart,
        pro: true,
      },
    ],
  },
  {
    title: "Real Legal Tools",
    icon: Gavel,
    defaultOpen: true,
    routes: [
      {
        label: "Case Law Search",
        href: "/case-search",
        icon: Search,
      },
      {
        label: "Precedent Analysis",
        href: "/precedent-analysis",
        icon: GraduationCap,
        pro: true,
      },
      {
        label: "Deposition Analysis",
        href: "/depositions",
        icon: Users,
        pro: true,
      },
    ],
  },
  {
    title: "Compare Like a Pro",
    icon: GitCompare,
    defaultOpen: true,
    routes: [
      {
        label: "Compare Analysis",
        href: "/document-comparison",
        icon: GitCompare,
      },
      {
        label: "Compare Document Sections",
        href: "/compare/sections",
        icon: GitCompare,
        pro: true,
      },
      {
        label: "Advanced Compare",
        href: "/compare/enhanced",
        icon: GitCompare,
        pro: true,
      },
    ],
  },
];

// Dev-only route categories
export const DEV_ROUTE_CATEGORIES: RouteCategory[] = [
  {
    title: "🚧 Beta Features",
    icon: Zap,
    defaultOpen: true,
    routes: [
      {
        label: "Auto-Draft Docs",
        href: "/document-automation",
        icon: Sparkles,
      },
      {
        label: "Compliance Scanner",
        href: "/compliance-auditor",
        icon: Shield,
      },
      {
        label: "Team Workspace",
        href: "/collaboration",
        icon: Users,
      },
      {
        label: "Deal Playbooks",
        href: "/contract-playbooks",
        icon: BookOpen,
      },
      {
        label: "Negotiate Mode",
        href: "/chat-negotiation",
        icon: MessageSquare,
      },
      {
        label: "Clause Vault",
        href: "/clause-library",
        icon: FileText,
      },
    ],
  },
];

// Helper function to check if a route is active
export function isRouteActive(pathname: string, href: string): boolean {
  if (href === "/dashboard" || href === "/analytics") {
    return pathname === href;
  }
  return pathname === href || pathname.startsWith(href + "/");
}

// Helper function to get all routes with active state
export function getStandaloneRoutesWithActive(pathname: string): RouteItem[] {
  return STANDALONE_ROUTES.map(route => ({
    ...route,
    active: isRouteActive(pathname, route.href),
  }));
}

export interface RouteCategoryWithActive {
  title: string;
  icon: React.ElementType;
  routes: RouteItem[];
  defaultOpen?: boolean;
}

export function getRouteCategoriesWithActive(pathname: string, categories: RouteCategory[]): RouteCategoryWithActive[] {
  return categories.map(category => ({
    ...category,
    routes: category.routes.map(route => ({
      ...route,
      active: isRouteActive(pathname, route.href),
    })),
  }));
} 