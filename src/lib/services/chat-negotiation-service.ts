import { apiClient } from "@/lib/config";
import type {
  CreateSessionRequest,
  NegotiationSession,
  SendMoveRequest,
  SendMoveResponse,
  ExtractDataRequest,
  ExtractDataResponse,
  GetSessionsResponse,
  NegotiationApiError,
  NegotiationScenario,
  CreateScenarioRequest,
  ScenarioResponse,
  CloneScenarioRequest,
  CreateScenarioFromAnalysisRequest,
  CreateSessionFromDocumentRequest,
  DocumentContextResponse,
  GetScenariosQuery,
  DeleteScenarioResponse,
} from "@/lib/types/chat-negotiation";

export class ChatNegotiationError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = "ChatNegotiationError";
  }
}

class ChatNegotiationService {
  private baseUrl = "/chat-negotiation";

  /**
   * Create a new negotiation session
   */
  async createSession(
    request: CreateSessionRequest
  ): Promise<NegotiationSession> {
    try {
      const response = await apiClient.post<NegotiationSession>(
        `${this.baseUrl}/sessions`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to create negotiation session");
    }
  }

  /**
   * Get session details by ID
   */
  async getSession(sessionId: string): Promise<NegotiationSession> {
    try {
      const response = await apiClient.get<NegotiationSession>(
        `${this.baseUrl}/sessions/${sessionId}`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get session details");
    }
  }

  /**
   * Get all user sessions with optional filters
   */
  async getUserSessions(params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<GetSessionsResponse> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.status) queryParams.append("status", params.status);
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.offset)
        queryParams.append("offset", params.offset.toString());

      const response = await apiClient.get<GetSessionsResponse>(
        `${this.baseUrl}/sessions?${queryParams}`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get user sessions");
    }
  }

  /**
   * Send a chat move/message in the negotiation
   */
  async sendMove(
    sessionId: string,
    request: SendMoveRequest
  ): Promise<SendMoveResponse> {
    try {
      const response = await apiClient.post<SendMoveResponse>(
        `${this.baseUrl}/sessions/${sessionId}/moves`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to send negotiation move");
    }
  }

  /**
   * Extract data from a message without creating a session move
   */
  async extractData(request: ExtractDataRequest): Promise<ExtractDataResponse> {
    try {
      const response = await apiClient.post<ExtractDataResponse>(
        `${this.baseUrl}/extract-data`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to extract data from message");
    }
  }

  /**
   * Create a custom negotiation scenario
   */
  async createScenario(
    request: CreateScenarioRequest
  ): Promise<ScenarioResponse> {
    try {
      const response = await apiClient.post<ScenarioResponse>(
        `${this.baseUrl}/scenarios`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to create scenario");
    }
  }

  /**
   * Get a specific scenario by ID
   */
  async getScenario(scenarioId: string): Promise<ScenarioResponse> {
    try {
      const response = await apiClient.get<ScenarioResponse>(
        `${this.baseUrl}/scenarios/${scenarioId}`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get scenario");
    }
  }

  /**
   * Get available negotiation scenarios
   */
  async getScenarios(query?: GetScenariosQuery): Promise<ScenarioResponse[]> {
    try {
      const queryParams = new URLSearchParams();
      if (query?.industry) queryParams.append("industry", query.industry);
      if (query?.contractType)
        queryParams.append("contractType", query.contractType);
      if (query?.difficulty) queryParams.append("difficulty", query.difficulty);
      if (query?.tags) queryParams.append("tags", query.tags);
      if (query?.includeTemplates !== undefined)
        queryParams.append(
          "includeTemplates",
          query.includeTemplates.toString()
        );

      const response = await apiClient.get<ScenarioResponse[]>(
        `${this.baseUrl}/scenarios?${queryParams}`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get negotiation scenarios");
    }
  }

  /**
   * Get template scenarios
   */
  async getTemplateScenarios(): Promise<ScenarioResponse[]> {
    try {
      const response = await apiClient.get<ScenarioResponse[]>(
        `${this.baseUrl}/scenarios/templates`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get template scenarios");
    }
  }

  /**
   * Clone an existing scenario
   */
  async cloneScenario(
    scenarioId: string,
    request?: CloneScenarioRequest
  ): Promise<ScenarioResponse> {
    try {
      const response = await apiClient.post<ScenarioResponse>(
        `${this.baseUrl}/scenarios/${scenarioId}/clone`,
        request || {}
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to clone scenario");
    }
  }

  /**
   * Update an existing scenario
   */
  async updateScenario(
    scenarioId: string,
    request: Partial<CreateScenarioRequest>
  ): Promise<ScenarioResponse> {
    try {
      const response = await apiClient.put<ScenarioResponse>(
        `${this.baseUrl}/scenarios/${scenarioId}`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to update scenario");
    }
  }

  /**
   * Delete a scenario
   */
  async deleteScenario(scenarioId: string): Promise<DeleteScenarioResponse> {
    try {
      const response = await apiClient.delete<DeleteScenarioResponse>(
        `${this.baseUrl}/scenarios/${scenarioId}`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to delete scenario");
    }
  }

  /**
   * Create scenario from contract analysis
   */
  async createScenarioFromAnalysis(
    request: CreateScenarioFromAnalysisRequest
  ): Promise<ScenarioResponse> {
    try {
      const response = await apiClient.post<ScenarioResponse>(
        `${this.baseUrl}/scenarios/from-analysis`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to create scenario from analysis");
    }
  }

  /**
   * Create session from document analysis
   */
  async createSessionFromDocument(
    request: CreateSessionFromDocumentRequest
  ): Promise<NegotiationSession> {
    try {
      const response = await apiClient.post<NegotiationSession>(
        `${this.baseUrl}/sessions/from-analysis`,
        request
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to create session from document");
    }
  }

  /**
   * Get document context for session
   */
  async getDocumentContext(
    sessionId: string
  ): Promise<DocumentContextResponse> {
    try {
      const response = await apiClient.get<DocumentContextResponse>(
        `${this.baseUrl}/sessions/${sessionId}/document-context`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get document context");
    }
  }

  /**
   * Pause a negotiation session
   */
  async pauseSession(sessionId: string): Promise<NegotiationSession> {
    try {
      const response = await apiClient.patch<NegotiationSession>(
        `${this.baseUrl}/sessions/${sessionId}/pause`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to pause session");
    }
  }

  /**
   * Resume a paused negotiation session
   */
  async resumeSession(sessionId: string): Promise<NegotiationSession> {
    try {
      const response = await apiClient.patch<NegotiationSession>(
        `${this.baseUrl}/sessions/${sessionId}/resume`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to resume session");
    }
  }

  /**
   * Complete/end a negotiation session
   */
  async completeSession(sessionId: string): Promise<NegotiationSession> {
    try {
      const response = await apiClient.patch<NegotiationSession>(
        `${this.baseUrl}/sessions/${sessionId}/complete`
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to complete session");
    }
  }

  /**
   * Get session analytics and performance metrics
   */
  async getSessionAnalytics(sessionId: string): Promise<{
    performance: {
      score: number;
      rounds: number;
      duration: number;
      efficiency: number;
    };
    communication: {
      messagesCount: number;
      averageResponseTime: number;
      sentimentAnalysis: any;
    };
    negotiation: {
      relationshipMetrics: any;
      extractedTerms: any;
      progressOverTime: any[];
    };
  }> {
    try {
      const response = await apiClient.get<{
        performance: {
          score: number;
          rounds: number;
          duration: number;
          efficiency: number;
        };
        communication: {
          messagesCount: number;
          averageResponseTime: number;
          sentimentAnalysis: any;
        };
        negotiation: {
          relationshipMetrics: any;
          extractedTerms: any;
          progressOverTime: any[];
        };
      }>(`${this.baseUrl}/sessions/${sessionId}/analytics`);
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get session analytics");
    }
  }

  /**
   * Get real-time suggestions during negotiation
   */
  async getSuggestions(
    sessionId: string,
    context?: {
      currentMessage?: string;
      situation?: string;
    }
  ): Promise<{
    suggestions: string[];
    strategies: string[];
    warnings: string[];
  }> {
    try {
      const response = await apiClient.post<{
        suggestions: string[];
        strategies: string[];
        warnings: string[];
      }>(`${this.baseUrl}/sessions/${sessionId}/suggestions`, context || {});
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to get negotiation suggestions");
    }
  }

  /**
   * Get conversation history for a session
   */
  async getMessages(
    sessionId: string,
    params?: {
      limit?: number;
      offset?: number;
      includeMetadata?: boolean;
    }
  ): Promise<{
    sessionId: string;
    messages: {
      id: string;
      content: string;
      sender: 'user' | 'ai';
      timestamp: string;
      extractedData: {
        strategy?: string;
        sentiment?: string;
        topic?: string;
        offer?: {
          price?: number;
          currency?: string;
          terms?: string[];
        };
        confidence?: number;
      };
      relationshipImpact?: {
        trustChange?: number;
        respectChange?: number;
        pressureChange?: number;
      };
      scoreImpact?: number;
      processingTimeMs?: number;
      confidence?: number;
      detectedStrategy?: string;
      sentiment?: string;
      aiResponseMetadata?: {
        suggestions?: string[];
        responseTime?: number;
      };
    }[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    sessionInfo: {
      currentRound: number;
      totalMessages: number;
      score: number;
      status: string;
      relationshipMetrics: {
        trust: number;
        respect: number;
        pressure: number;
      };
    };
  }> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.offset) queryParams.append("offset", params.offset.toString());
      if (params?.includeMetadata) queryParams.append("includeMetadata", params.includeMetadata.toString());

      const response = await apiClient.get(
        `${this.baseUrl}/sessions/${sessionId}/messages?${queryParams}`
      );
      
      // Map the API response to our internal format
      const data = response.data as any;
      if (data && data.messages) {
        // Transform messages to ensure correct role mapping
        data.messages = data.messages.map((message: any) => ({
          ...message,
          // Map 'sender' field from API to 'role' field for our interface
          role: message.sender, // API returns 'sender': 'user' | 'ai'
          sessionId: sessionId // Ensure sessionId is set
        }));
      }
      
      return data;
    } catch (error) {
      this.handleError(error, "Failed to get conversation history");
    }
  }

  /**
   * Export session data for review
   */
  async exportSession(
    sessionId: string,
    format: "json" | "pdf" | "csv" = "json"
  ): Promise<Blob> {
    try {
      const response = await apiClient.get<Blob>(
        `${this.baseUrl}/sessions/${sessionId}/export?format=${format}`,
        { responseType: "blob" }
      );
      return response.data;
    } catch (error) {
      this.handleError(error, "Failed to export session data");
    }
  }

  private handleError(error: any, defaultMessage: string): never {
    if (error.response?.data) {
      const apiError = error.response.data as NegotiationApiError;
      throw new ChatNegotiationError(
        apiError.message || defaultMessage,
        apiError.statusCode,
        apiError.error,
        apiError.details
      );
    }

    if (error.response?.status) {
      const statusMessages: Record<number, string> = {
        400: "Invalid request data",
        401: "Authentication required",
        403: "Insufficient permissions",
        404: "Session not found",
        429: "Rate limit exceeded",
        500: "Internal server error",
      };

      throw new ChatNegotiationError(
        statusMessages[error.response.status] || defaultMessage,
        error.response.status
      );
    }

    throw new ChatNegotiationError(
      defaultMessage,
      undefined,
      "UNKNOWN_ERROR",
      error
    );
  }
}

export const chatNegotiationService = new ChatNegotiationService();
