import { AxiosError } from "axios";
import { apiClient } from "../config";
import {
  ComplianceAuditResult,
  ComplianceProfile,
  ComplianceAnalytics,
  RegulatoryFramework,
  AuditRequest,
  CreateProfileRequest,
} from "../types/compliance";

export class ComplianceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = "ComplianceError";
  }
}

class ComplianceService {
  /**
   * Audit a document against regulatory frameworks
   */
  async auditDocument(request: AuditRequest): Promise<ComplianceAuditResult> {
    try {
      const response = await apiClient.post<ComplianceAuditResult>(
        `/compliance/audit`,
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 403) {
          throw new ComplianceError(
            "Compliance auditing feature not available in your subscription plan",
            "FEATURE_NOT_AVAILABLE",
            403
          );
        }
        if (error.response?.status === 422) {
          throw new ComplianceError(
            error.response.data.message || "Invalid audit request",
            "INVALID_REQUEST",
            422
          );
        }
        throw new ComplianceError(
          "Failed to audit document",
          "AUDIT_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get audit results with filtering and pagination
   */
  async getAuditResults(params?: {
    frameworks?: string[];
    status?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    results: ComplianceAuditResult[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const response = await apiClient.get<{
        results: ComplianceAuditResult[];
        total: number;
        page: number;
        limit: number;
      }>(`/compliance/audit-results`, {
        params,
      });
      return {
        results: response.data.results || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        limit: response.data.limit || 10
      };
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ComplianceError(
          "Failed to fetch audit results",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get specific audit result by ID
   */
  async getAuditResult(resultId: string): Promise<ComplianceAuditResult> {
    try {
      const response = await apiClient.get<ComplianceAuditResult>(
        `/compliance/audit-results/${resultId}`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          throw new ComplianceError(
            "Audit result not found",
            "NOT_FOUND",
            404
          );
        }
        throw new ComplianceError(
          "Failed to fetch audit result",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Create a new compliance profile
   */
  async createProfile(request: CreateProfileRequest): Promise<ComplianceProfile> {
    try {
      const response = await apiClient.post<ComplianceProfile>(
        `/compliance/profiles`,
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 422) {
          throw new ComplianceError(
            error.response.data.message || "Invalid profile data",
            "INVALID_REQUEST",
            422
          );
        }
        throw new ComplianceError(
          "Failed to create compliance profile",
          "CREATE_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get compliance profiles with filtering
   */
  async getProfiles(params?: {
    industry?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    profiles: ComplianceProfile[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const response = await apiClient.get<{
        profiles: ComplianceProfile[];
        total: number;
        page: number;
        limit: number;
      }>(`/compliance/profiles`, {
        params,
      });
      return {
        profiles: response.data.profiles || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        limit: response.data.limit || 10
      };
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ComplianceError(
          "Failed to fetch compliance profiles",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get specific compliance profile by ID
   */
  async getProfile(profileId: string): Promise<ComplianceProfile> {
    try {
      const response = await apiClient.get<ComplianceProfile>(
        `/compliance/profiles/${profileId}`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          throw new ComplianceError(
            "Compliance profile not found",
            "NOT_FOUND",
            404
          );
        }
        throw new ComplianceError(
          "Failed to fetch compliance profile",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Update a compliance profile
   */
  async updateProfile(
    profileId: string,
    updates: Partial<CreateProfileRequest>
  ): Promise<ComplianceProfile> {
    try {
      const response = await apiClient.put<ComplianceProfile>(
        `/compliance/profiles/${profileId}`,
        updates
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          throw new ComplianceError(
            "Compliance profile not found",
            "NOT_FOUND",
            404
          );
        }
        throw new ComplianceError(
          "Failed to update compliance profile",
          "UPDATE_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Delete a compliance profile
   */
  async deleteProfile(profileId: string): Promise<void> {
    try {
      await apiClient.delete(`/compliance/profiles/${profileId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          throw new ComplianceError(
            "Compliance profile not found",
            "NOT_FOUND",
            404
          );
        }
        throw new ComplianceError(
          "Failed to delete compliance profile",
          "DELETE_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get available regulatory frameworks
   */
  async getFrameworks(): Promise<{ frameworks: RegulatoryFramework[] }> {
    try {
      const response = await apiClient.get<{ frameworks: RegulatoryFramework[] }>(
        `/compliance/frameworks`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ComplianceError(
          "Failed to fetch regulatory frameworks",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get rules for a specific regulatory framework
   */
  async getFrameworkRules(regulationId: string): Promise<any> {
    try {
      const response = await apiClient.get(
        `/compliance/regulations/${regulationId}/rules`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          throw new ComplianceError(
            "Regulatory framework not found",
            "NOT_FOUND",
            404
          );
        }
        throw new ComplianceError(
          "Failed to fetch framework rules",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get compliance analytics overview
   */
  async getAnalytics(): Promise<ComplianceAnalytics> {
    try {
      const response = await apiClient.get<ComplianceAnalytics>(
        `/compliance/analytics/overview`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new ComplianceError(
          "Failed to fetch compliance analytics",
          "FETCH_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }
}

export const complianceService = new ComplianceService();
