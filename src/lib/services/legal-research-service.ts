import { apiClient } from "@/lib/config";

// Interfaces for raw API data structures before transformation
interface RawResearchQuery {
  queryId: string;
  queryText: string;
  type: 'initial' | 'followup'; // Backend type
  timestamp: string;
  searchResults?: {
    sources?: (Partial<Omit<SearchResult, 'citation'>> & { citation?: string | string[] | null; [key: string]: unknown })[];
    summary?: string;
  };
  aiSynthesis?: RawAISynthesis;
  [key: string]: unknown;
}

interface RawResearchSessionData {
  sessionId: string;
  sessionName: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  queries?: RawResearchQuery[];
  isShared?: boolean; // Added optional isShared
  [key: string]: unknown;
}
import { AxiosError } from "axios";
import { LegalResearchError } from "@/lib/types/legal-research";
import type {
  ResearchSession,
  ResearchQueryRequest,
  ResearchResponse,
  FollowUpRequest,
  FollowUpResponse,
  CreateSessionRequest,
  SessionsResponse,
  SessionListParams,
  AnalyticsParams,
  AnalyticsResponse,
  CreditCheckResult,
  AISynthesis,
  RawAISynthesis,
  SearchResult,
  KeyFinding,
  LegalAnalysis,
} from "@/lib/types/legal-research";

class LegalResearchService {
  private readonly baseUrl = "/legal-research-assistant";

  /**
   * Parse AI synthesis JSON strings from backend response
   */
  private parseAISynthesis(raw: RawAISynthesis): AISynthesis {
    try {
      const { keyFindings: rawKeyFindings, legalAnalysis: rawLegalAnalysis } = raw;

      // Helper to parse a field that might be a JSON string or a markdown-fenced JSON
      const safeParse = (field: string | Record<string, unknown> | unknown[] | null | undefined): Record<string, unknown> | unknown[] | string | null | undefined => {
        if (typeof field !== 'string') {
          return field;
        }
        try {
          // Handle markdown code fence
          const match = field.match(/```json\s*([\s\S]*?)\s*```/); // Corrected regex for JSON: /```json\\s*([\\s\\S]*?)\\s*```/
          const jsonString = match ? match[1] : field;
          return JSON.parse(jsonString);
        } catch {
          // Not a valid JSON string, return as is
          return field;
        }
      };

      const parsedKeyFindings = safeParse(rawKeyFindings);
      const parsedLegalAnalysis = safeParse(rawLegalAnalysis);

      // Validate and structure the final return object
      const finalKeyFindings: KeyFinding[] = Array.isArray(parsedKeyFindings)
        ? parsedKeyFindings.filter(
            (kf: unknown): kf is KeyFinding =>
              typeof kf === 'object' &&
              kf !== null &&
              'finding' in kf &&
              'sourceUrls' in kf
          )
        : [];

      const finalLegalAnalysis: LegalAnalysis =
        typeof parsedLegalAnalysis === 'object' &&
        parsedLegalAnalysis !== null &&
        'text' in parsedLegalAnalysis &&
        typeof parsedLegalAnalysis.text === 'string'
          ? {
              text: parsedLegalAnalysis.text,
              sourceUrls: (Array.isArray((parsedLegalAnalysis as { sourceUrls?: string[] }).sourceUrls)
                ? (parsedLegalAnalysis as { sourceUrls: string[] }).sourceUrls
                : []) as string[],
            }
          : { text: String(parsedLegalAnalysis || ''), sourceUrls: [] };

      return {
        keyFindings: finalKeyFindings,
        legalAnalysis: finalLegalAnalysis,
        citations: raw.citations || [],
        confidenceScore: raw.confidenceScore || 0,
        practiceImplications: raw.practiceImplications || [],
        jurisdictionalNotes: raw.jurisdictionalNotes || '',
        recentDevelopments: raw.recentDevelopments || '',
        metadata: raw.metadata,
      };
    } catch (error) {
      console.error('Error parsing AI synthesis:', error);
      // Fallback to safe values in case of unexpected errors
      return {
        keyFindings: [],
        legalAnalysis: { text: 'Error parsing analysis.', sourceUrls: [] },
        citations: raw.citations || [],
        confidenceScore: raw.confidenceScore || 0,
        practiceImplications: raw.practiceImplications || [],
        jurisdictionalNotes: raw.jurisdictionalNotes || '',
        recentDevelopments: raw.recentDevelopments || '',
        metadata: raw.metadata,
      };
    }
  }

  /**
   * Normalize search result citation format
   */
  private normalizeSearchResult(result: Partial<Omit<SearchResult, 'citation'>> & { id?: string; citation?: string | string[] | null; [key: string]: unknown }): SearchResult {
    // Ensure all required fields of SearchResult are present.
    return {
      ...result, // Spread result first to get existing values
      id: result.id ?? `generated-id-${Date.now()}-${Math.random().toString(16).slice(2)}`,
      title: result.title ?? 'No title provided',
      type: result.type ?? 'other',
      url: result.url ?? '#',
      snippet: result.snippet ?? 'No snippet provided',
      citation: Array.isArray(result.citation)
        ? result.citation.join(', ')
        : String(result.citation ?? 'No citation'),
      court: result.court ?? null,
      authority: result.authority ?? null,
      practiceArea: result.practiceArea ?? null,
      relevanceScore: result.relevanceScore ?? 0,
      authorityScore: result.authorityScore ?? 0,
    } as SearchResult; // Use 'as SearchResult' to assure TS, assuming the defaults cover requirements.
  }

  /**
   * Perform a comprehensive legal research query
   */
  async performResearch(request: ResearchQueryRequest): Promise<ResearchResponse> {
    try {
      const response = await apiClient.post<ResearchResponse>(
        `${this.baseUrl}/research/query`,
        request
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to perform research query");
    }
  }

  /**
   * Ask a follow-up question within an existing session
   */
  async askFollowUp(request: FollowUpRequest): Promise<FollowUpResponse> {
    try {
      const response = await apiClient.post<FollowUpResponse>(
        `${this.baseUrl}/research/follow-up`,
        request
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to ask follow-up question");
    }
  }

  /**
   * Create a new research session
   */
  async createSession(request: CreateSessionRequest): Promise<ResearchSession> {
    try {
      const response = await apiClient.post<{ success: boolean; data: ResearchSession }>(
        `${this.baseUrl}/sessions`,
        request
      );
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, "Failed to create research session");
    }
  }

  /**
   * Get a specific research session with its query history
   */
  async getSession(sessionId: string): Promise<ResearchSession> {
    try {
      const response = await apiClient.get<{ success: boolean; data: RawResearchSessionData }>(
        `${this.baseUrl}/sessions/${sessionId}`
      );

      const rawSession = response.data.data;

      // Process the session data to handle the actual API format
      const mappedQueries = rawSession.queries?.map((query: RawResearchQuery) => ({
        ...query,
        queryType: query.type === 'initial' ? 'research' : 'followup', // Map backend 'type' to frontend 'queryType'
        searchResults: query.searchResults ? {
          ...query.searchResults,
          sources: query.searchResults.sources?.map((source: Partial<Omit<SearchResult, 'citation'>> & { id?: string; citation?: string | string[] | null; [key: string]: unknown }) => this.normalizeSearchResult(source)) || [],
          totalSources: query.searchResults.sources?.length ?? 0
        } : undefined,
        aiSynthesis: query.aiSynthesis ? this.parseAISynthesis(query.aiSynthesis) : undefined,
        creditsUsed: (query as { creditsUsed?: number }).creditsUsed ?? 0,
        resultSummary: (query as { resultSummary?: string }).resultSummary ?? '',
      })) || [];

      const processedSession: ResearchSession = {
        ...rawSession, // Spread rawSession first to get base fields like sessionId, createdAt, etc.
        title: rawSession.sessionName || "Untitled Session",
        queries: mappedQueries,
        isShared: rawSession.isShared ?? false,
        queryCount: mappedQueries.length,
        totalCreditsUsed: mappedQueries.reduce((sum, q) => sum + (q.creditsUsed || 0), 0),
        tags: rawSession.tags ?? [] // Ensure tags is always an array
      };

      return processedSession;
    } catch (error) {
      throw this.handleError(error, "Failed to get research session");
    }
  }

  /**
   * List all research sessions for the organization
   */
  async listSessions(params?: SessionListParams): Promise<SessionsResponse> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.tags) queryParams.append('tags', params.tags);
      if (params?.search) queryParams.append('search', params.search);

      const url = `${this.baseUrl}/sessions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiClient.get<SessionsResponse>(url);
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to list research sessions");
    }
  }

  /**
   * Delete a research session
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/sessions/${sessionId}`);
    } catch (error) {
      throw this.handleError(error, "Failed to delete research session");
    }
  }

  /**
   * Update a research session
   */
  async updateSession(sessionId: string, updates: Partial<CreateSessionRequest>): Promise<ResearchSession> {
    try {
      const response = await apiClient.patch<{ success: boolean; data: ResearchSession }>(
        `${this.baseUrl}/sessions/${sessionId}`,
        updates
      );
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, "Failed to update research session");
    }
  }

  /**
   * Get research analytics (Pro+ tiers only)
   */
  async getAnalytics(params: AnalyticsParams): Promise<AnalyticsResponse> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('period', params.period);
      if (params.groupBy) queryParams.append('groupBy', params.groupBy);

      const response = await apiClient.get<AnalyticsResponse>(
        `${this.baseUrl}/analytics?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to get research analytics");
    }
  }

  /**
   * Check if user has sufficient credits for a research operation
   */
  async checkCredits(featureName: string): Promise<CreditCheckResult> {
    try {
      const response = await apiClient.get<CreditCheckResult>(
        `/credits/check/${featureName}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to check credits");
    }
  }

  /**
   * Use credits for a research operation
   */
  async useCredits(featureName: string, transactionId?: string): Promise<void> {
    try {
      await apiClient.post(
        `/credits/use`,
        { featureName, transactionId }
      );
    } catch (error) {
      throw this.handleError(error, "Failed to use credits");
    }
  }

  /**
   * Get available jurisdictions for filtering
   */
  async getJurisdictions(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ jurisdictions: string[] }>(
        `${this.baseUrl}/jurisdictions`
      );
      return response.data.jurisdictions;
    } catch {
      // Return default jurisdictions if API fails
      return ['federal', 'california', 'new_york', 'texas', 'florida'];
    }
  }

  /**
   * Get available practice areas for filtering
   */
  async getPracticeAreas(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ practiceAreas: string[] }>(
        `${this.baseUrl}/practice-areas`
      );
      return response.data.practiceAreas;
    } catch {
      // Return default practice areas if API fails
      return ['privacy', 'contracts', 'employment', 'intellectual_property', 'corporate'];
    }
  }

  /**
   * Handle API errors and convert to LegalResearchError
   */
  private handleError(error: unknown, defaultMessage: string): LegalResearchError {
    if (error instanceof AxiosError) {
      const statusCode = error.response?.status;
      const errorData = error.response?.data;

      // Handle specific error codes
      if (statusCode === 402) {
        return new LegalResearchError(
          errorData?.error?.message || "Insufficient credits",
          "INSUFFICIENT_CREDITS",
          statusCode,
          errorData?.error?.details
        );
      }

      if (statusCode === 403) {
        return new LegalResearchError(
          errorData?.error?.message || "Feature not available in your subscription tier",
          "FEATURE_NOT_AVAILABLE",
          statusCode,
          errorData?.error?.details
        );
      }

      if (statusCode === 429) {
        return new LegalResearchError(
          errorData?.error?.message || "Rate limit exceeded",
          "RATE_LIMIT_EXCEEDED",
          statusCode,
          errorData?.error?.details
        );
      }

      if (statusCode === 400) {
        return new LegalResearchError(
          errorData?.error?.message || "Invalid request",
          "INVALID_REQUEST",
          statusCode,
          errorData?.error?.details
        );
      }

      return new LegalResearchError(
        errorData?.error?.message || error.message || defaultMessage,
        "API_ERROR",
        statusCode,
        errorData?.error?.details
      );
    }

    if (error instanceof Error) {
      return new LegalResearchError(error.message, "UNKNOWN_ERROR");
    }

    return new LegalResearchError(defaultMessage, "UNKNOWN_ERROR");
  }
}

// Export singleton instance
export const legalResearchService = new LegalResearchService();
export default legalResearchService;
