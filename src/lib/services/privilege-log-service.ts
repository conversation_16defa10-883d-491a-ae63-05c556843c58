import { AxiosError } from 'axios';
import { apiClient } from '@/lib/config';
import type {
  PrivilegeAnalysisResult,
  PrivilegeLogEntry,
  AnalysisOptions,
  ReviewPrivilegeRequest,
  ApplyRedactionRequest,
  BulkRedactionRequest,
  PrivilegeLogApiResponse,
  PrivilegeLogListResponse
} from '@/lib/types/privilege-log';

export class PrivilegeLogError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: unknown
  ) {
    super(message);
    this.name = 'PrivilegeLogError';
  }
}

class PrivilegeLogService {
  private baseUrl = '/documents';

  /**
   * Analyze document for privileged content
   */
  async analyzeDocument(
    documentId: string,
    options: AnalysisOptions
  ): Promise<PrivilegeAnalysisResult> {
    try {
      const response = await apiClient.post<{
        data: PrivilegeAnalysisResult;
        error?: {
          message: string;
          code: string;
          details?: unknown;
        };
      }>(`${this.baseUrl}/${documentId}/privilege-analysis`, options);
      return response.data.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as {
          error?: {
            message: string;
            code: string;
            details?: unknown;
          };
        };
        
        // Handle specific error cases
        if (error.response?.status === 403) {
          throw new PrivilegeLogError(
            'Privilege log automation feature requires PRO subscription',
            'FEATURE_NOT_AVAILABLE',
            403,
            { upgradeUrl: '/subscription/upgrade' }
          );
        }
        
        if (error.response?.status === 404) {
          throw new PrivilegeLogError(
            'Document not found or access denied',
            'DOCUMENT_NOT_FOUND',
            404
          );
        }
        
        if (error.response?.status === 400) {
          throw new PrivilegeLogError(
            errorData?.error?.message || 'Invalid analysis parameters',
            'VALIDATION_ERROR',
            400,
            errorData?.error?.details
          );
        }
        
        throw new PrivilegeLogError(
          errorData?.error?.message || 'Failed to analyze document for privilege',
          errorData?.error?.code || 'ANALYSIS_FAILED',
          error.response?.status,
          errorData?.error?.details
        );
      }
      throw error;
    }
  }

  /**
   * Get existing privilege log for a document
   */
  async getPrivilegeLog(documentId: string): Promise<PrivilegeLogEntry | null> {
    try {
      const response = await apiClient.get<{
        data: PrivilegeLogEntry;
        error?: {
          message: string;
          code: string;
          details?: unknown;
        };
      }>(`${this.baseUrl}/${documentId}/privilege-log`);
      return response.data.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 404) {
          return null; // No privilege log exists
        }
        
        const errorData = error.response?.data as {
          error?: {
            message: string;
            code: string;
            details?: unknown;
          };
        };
        throw new PrivilegeLogError(
          errorData?.error?.message || 'Failed to retrieve privilege log',
          errorData?.error?.code || 'RETRIEVAL_FAILED',
          error.response?.status,
          errorData?.error?.details
        );
      }
      throw error;
    }
  }

  /**
   * Get all privilege logs with filtering and pagination
   */
  async getPrivilegeLogs(params: {
    status?: string;
    privilegeType?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{
    items: PrivilegeLogEntry[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  }> {
    try {
      const queryString = new URLSearchParams(
        Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => [key, String(value)])
      ).toString();
      
      const response = await apiClient.get<{
        data: {
          items: PrivilegeLogEntry[];
        };
        pagination: {
          total: number;
          page: number;
          limit: number;
          pages: number;
        };
      }>(`${this.baseUrl}/privilege-logs${queryString ? `?${queryString}` : ''}`);
      
      return {
        items: response.data.data.items,
        pagination: response.data.pagination
      };
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as {
          error?: {
            message: string;
            code: string;
            details?: unknown;
          };
        };
        throw new PrivilegeLogError(
          errorData?.error?.message || 'Failed to retrieve privilege logs',
          errorData?.error?.code || 'LIST_FAILED',
          error.response?.status,
          errorData?.error?.details
        );
      }
      throw error;
    }
  }

  /**
   * Review a privileged content item
   */
  async reviewContent(
    documentId: string,
    contentId: string,
    data: ReviewPrivilegeRequest
  ): Promise<PrivilegeLogEntry> {
    try {
      const response = await apiClient.put<{
        data: PrivilegeLogEntry;
        error?: {
          message: string;
          code: string;
          details?: unknown;
        };
      }>(`${this.baseUrl}/${documentId}/privilege-content/${contentId}/review`, data);
      return response.data.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as {
          error?: {
            message: string;
            code: string;
            details?: unknown;
          };
        };
        throw new PrivilegeLogError(
          errorData?.error?.message || 'Failed to review privileged content',
          errorData?.error?.code || 'REVIEW_FAILED',
          error.response?.status,
          errorData?.error?.details
        );
      }
      throw error;
    }
  }

  /**
   * Apply redaction to a single privileged content item
   */
  async applyRedaction(
    documentId: string,
    data: ApplyRedactionRequest
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post<{
        success: boolean;
        message: string;
      }>(`${this.baseUrl}/${documentId}/redactions`, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as {
          error?: {
            message: string;
            code: string;
            details?: unknown;
          };
        };
        throw new PrivilegeLogError(
          errorData?.error?.message || 'Failed to apply redaction',
          errorData?.error?.code || 'REDACTION_FAILED',
          error.response?.status,
          errorData?.error?.details
        );
      }
      throw error;
    }
  }

  /**
   * Apply bulk redactions to multiple privileged content items
   */
  async applyBulkRedaction(
    documentId: string,
    data: BulkRedactionRequest
  ): Promise<{ success: boolean; data: { redactedCount: number }; message: string }> {
    try {
      const response = await apiClient.post<{
        success: boolean;
        data: { redactedCount: number };
        message: string;
      }>(`${this.baseUrl}/${documentId}/bulk-redactions`, data);
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as {
          error?: {
            message: string;
            code: string;
            details?: unknown;
          };
        };
        throw new PrivilegeLogError(
          errorData?.error?.message || 'Failed to apply bulk redactions',
          errorData?.error?.code || 'BULK_REDACTION_FAILED',
          error.response?.status,
          errorData?.error?.details
        );
      }
      throw error;
    }
  }

  /**
   * Check if a privilege log exists for a document (lightweight check)
   */
  async hasPrivilegeLog(documentId: string): Promise<boolean> {
    try {
      const log = await this.getPrivilegeLog(documentId);
      return log !== null;
    } catch {
      // If there's an error other than 404, we assume no log exists
      return false;
    }
  }
}

// Export singleton instance
export const privilegeLogService = new PrivilegeLogService();
