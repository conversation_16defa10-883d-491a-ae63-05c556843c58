/**
 * Team Challenges Service
 * Handles team-based competitions and collaborative challenges
 */

import { AxiosError } from "axios";
import { apiClient } from "@/lib/config";

export interface TeamChallenge {
  id: string;
  title: string;
  description: string;
  type: 'competition' | 'collaboration' | 'tournament' | 'league';
  format: 'individual_scores' | 'team_average' | 'best_performer' | 'collaborative_session';
  difficulty: number;
  maxParticipants: number;
  minParticipants: number;
  duration: ChallengeDuration;
  objectives: ChallengeObjective[];
  rewards: ChallengeRewards;
  rules: ChallengeRules;
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';
  visibility: 'public' | 'organization' | 'private';
  organizationId?: string;
  createdBy: string;
  startDate: Date;
  endDate: Date;
  registrationDeadline: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChallengeDuration {
  type: 'fixed' | 'flexible';
  startDate: Date;
  endDate: Date;
  timeZone: string;
  allowLateJoin: boolean;
  gracePeriod?: number; // minutes
}

export interface ChallengeObjective {
  id: string;
  description: string;
  type: 'score_threshold' | 'session_count' | 'improvement_rate' | 'team_collaboration';
  target: any;
  weight: number; // for scoring
  isRequired: boolean;
}

export interface ChallengeRewards {
  individual: {
    first: RewardTier;
    second: RewardTier;
    third: RewardTier;
    participation: RewardTier;
  };
  team: {
    winning_team: RewardTier;
    runner_up: RewardTier;
    participation: RewardTier;
  };
  special: SpecialReward[];
}

export interface RewardTier {
  xp: number;
  credits?: number;
  achievements?: string[];
  unlocks?: string[];
  badge?: string;
  title?: string;
}

export interface SpecialReward {
  id: string;
  name: string;
  description: string;
  criteria: any;
  reward: RewardTier;
}

export interface ChallengeRules {
  allowedCharacters?: string[];
  restrictedCharacters?: string[];
  scenarioPool?: string[];
  maxAttempts?: number;
  scoringMethod: 'best_score' | 'average_score' | 'total_score' | 'improvement';
  tieBreaker: 'time_taken' | 'session_count' | 'consistency';
  allowTeamCommunication: boolean;
  realTimeUpdates: boolean;
}

export interface Team {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  captainId: string;
  members: TeamMember[];
  organizationId?: string;
  maxMembers: number;
  isPublic: boolean;
  statistics: TeamStatistics;
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamMember {
  userId: string;
  role: 'captain' | 'member' | 'substitute';
  joinedAt: Date;
  status: 'active' | 'inactive' | 'pending';
  permissions: string[];
  statistics: MemberStatistics;
}

export interface TeamStatistics {
  totalChallenges: number;
  challengesWon: number;
  challengesCompleted: number;
  averageScore: number;
  totalXP: number;
  currentRank: number;
  winRate: number;
}

export interface MemberStatistics {
  challengesParticipated: number;
  averageScore: number;
  totalXP: number;
  contributionScore: number;
}

export interface ChallengeParticipation {
  id: string;
  challengeId: string;
  participantId: string; // userId or teamId
  participantType: 'individual' | 'team';
  status: 'registered' | 'active' | 'completed' | 'disqualified' | 'withdrawn';
  registeredAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  currentScore: number;
  bestScore: number;
  sessionsCompleted: number;
  objectivesCompleted: string[];
  rank?: number;
  teamData?: {
    teamId: string;
    memberContributions: Record<string, any>;
  };
}

export interface ChallengeSession {
  id: string;
  challengeId: string;
  participationId: string;
  sessionId: string; // Reference to negotiation session
  userId: string;
  score: number;
  objectives: Record<string, boolean>;
  startedAt: Date;
  completedAt?: Date;
  metadata: any;
}

export interface ChallengeLeaderboard {
  challengeId: string;
  type: 'individual' | 'team';
  entries: LeaderboardEntry[];
  lastUpdated: Date;
  totalParticipants: number;
}

export interface LeaderboardEntry {
  rank: number;
  participantId: string;
  participantName: string;
  participantType: 'individual' | 'team';
  score: number;
  sessionsCompleted: number;
  objectivesCompleted: number;
  lastActivity: Date;
  trend: 'up' | 'down' | 'stable';
  avatar?: string;
  organizationName?: string;
}

export interface ChallengeApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export class TeamChallengeError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: unknown
  ) {
    super(message);
    this.name = "TeamChallengeError";
  }
}

class TeamChallengesService {
  private baseUrl = "/api/team-challenges";

  private transformResponse<T>(response: any): T {
    if (response?.data) {
      return response.data;
    }
    return response;
  }

  // Challenge Management
  async getChallenges(
    filters?: {
      status?: string;
      type?: string;
      organizationId?: string;
      visibility?: string;
      userId?: string;
    }
  ): Promise<TeamChallenge[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.organizationId) params.append('organizationId', filters.organizationId);
      if (filters?.visibility) params.append('visibility', filters.visibility);
      if (filters?.userId) params.append('userId', filters.userId);

      const response = await apiClient.get<ChallengeApiResponse<{ challenges: TeamChallenge[] }>>(
        `${this.baseUrl}?${params}`
      );
      const result = this.transformResponse<{ challenges: TeamChallenge[] }>(response.data);
      return result.challenges;
    } catch (error) {
      this.handleError(error, "Failed to get challenges");
    }
  }

  async getChallenge(challengeId: string): Promise<TeamChallenge> {
    try {
      const response = await apiClient.get<ChallengeApiResponse<TeamChallenge>>(
        `${this.baseUrl}/${challengeId}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get challenge");
    }
  }

  async createChallenge(challengeData: Partial<TeamChallenge>): Promise<TeamChallenge> {
    try {
      const response = await apiClient.post<ChallengeApiResponse<TeamChallenge>>(
        `${this.baseUrl}`,
        challengeData
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to create challenge");
    }
  }

  async updateChallenge(
    challengeId: string,
    updates: Partial<TeamChallenge>
  ): Promise<TeamChallenge> {
    try {
      const response = await apiClient.put<ChallengeApiResponse<TeamChallenge>>(
        `${this.baseUrl}/${challengeId}`,
        updates
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to update challenge");
    }
  }

  async deleteChallenge(challengeId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${challengeId}`);
    } catch (error) {
      this.handleError(error, "Failed to delete challenge");
    }
  }

  // Participation Management
  async joinChallenge(
    challengeId: string,
    participantId: string,
    participantType: 'individual' | 'team'
  ): Promise<ChallengeParticipation> {
    try {
      const response = await apiClient.post<ChallengeApiResponse<ChallengeParticipation>>(
        `${this.baseUrl}/${challengeId}/join`,
        { participantId, participantType }
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to join challenge");
    }
  }

  async leaveChallenge(
    challengeId: string,
    participantId: string
  ): Promise<void> {
    try {
      await apiClient.post(
        `${this.baseUrl}/${challengeId}/leave`,
        { participantId }
      );
    } catch (error) {
      this.handleError(error, "Failed to leave challenge");
    }
  }

  async getChallengeParticipants(
    challengeId: string
  ): Promise<ChallengeParticipation[]> {
    try {
      const response = await apiClient.get<ChallengeApiResponse<{ participants: ChallengeParticipation[] }>>(
        `${this.baseUrl}/${challengeId}/participants`
      );
      const result = this.transformResponse<{ participants: ChallengeParticipation[] }>(response.data);
      return result.participants;
    } catch (error) {
      this.handleError(error, "Failed to get challenge participants");
    }
  }

  // Team Management
  async getTeams(
    filters?: {
      organizationId?: string;
      isPublic?: boolean;
      userId?: string;
    }
  ): Promise<Team[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.organizationId) params.append('organizationId', filters.organizationId);
      if (filters?.isPublic !== undefined) params.append('isPublic', filters.isPublic.toString());
      if (filters?.userId) params.append('userId', filters.userId);

      const response = await apiClient.get<ChallengeApiResponse<{ teams: Team[] }>>(
        `${this.baseUrl}/teams?${params}`
      );
      const result = this.transformResponse<{ teams: Team[] }>(response.data);
      return result.teams;
    } catch (error) {
      this.handleError(error, "Failed to get teams");
    }
  }

  async createTeam(teamData: Partial<Team>): Promise<Team> {
    try {
      const response = await apiClient.post<ChallengeApiResponse<Team>>(
        `${this.baseUrl}/teams`,
        teamData
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to create team");
    }
  }

  async joinTeam(teamId: string, userId: string): Promise<Team> {
    try {
      const response = await apiClient.post<ChallengeApiResponse<Team>>(
        `${this.baseUrl}/teams/${teamId}/join`,
        { userId }
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to join team");
    }
  }

  async leaveTeam(teamId: string, userId: string): Promise<void> {
    try {
      await apiClient.post(
        `${this.baseUrl}/teams/${teamId}/leave`,
        { userId }
      );
    } catch (error) {
      this.handleError(error, "Failed to leave team");
    }
  }

  // Leaderboards and Analytics
  async getChallengeLeaderboard(
    challengeId: string,
    type: 'individual' | 'team' = 'individual'
  ): Promise<ChallengeLeaderboard> {
    try {
      const response = await apiClient.get<ChallengeApiResponse<ChallengeLeaderboard>>(
        `${this.baseUrl}/${challengeId}/leaderboard?type=${type}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get challenge leaderboard");
    }
  }

  async getChallengeAnalytics(challengeId: string): Promise<any> {
    try {
      const response = await apiClient.get<ChallengeApiResponse<any>>(
        `${this.baseUrl}/${challengeId}/analytics`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get challenge analytics");
    }
  }

  async getTeamAnalytics(teamId: string): Promise<any> {
    try {
      const response = await apiClient.get<ChallengeApiResponse<any>>(
        `${this.baseUrl}/teams/${teamId}/analytics`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get team analytics");
    }
  }

  private handleError(error: unknown, defaultMessage: string): never {
    if (error instanceof AxiosError) {
      const message = error.response?.data?.message || defaultMessage;
      const code = error.response?.data?.code || "TEAM_CHALLENGE_ERROR";
      const statusCode = error.response?.status;
      const details = error.response?.data?.details;

      throw new TeamChallengeError(message, code, statusCode, details);
    }

    throw new TeamChallengeError(
      defaultMessage,
      "UNKNOWN_ERROR",
      undefined,
      error
    );
  }
}

export const teamChallengesService = new TeamChallengesService();
