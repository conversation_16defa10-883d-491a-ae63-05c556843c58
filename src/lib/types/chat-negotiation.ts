// Chat Negotiation API Types
export interface CreateSessionRequest {
  scenarioId: string;
  aiPersonality: {
    characterId: string;
    aggressiveness: number;
    flexibility: number;
    riskTolerance: number;
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
  metadata?: {
    source?: string;
    version?: string;
    [key: string]: any;
  };
}

export interface NegotiationSession {
  _id: string;
  id?: string; // For backward compatibility
  userId: string;
  negotiationSessionId: string;
  chatSessionId: string;
  scenarioId: string;
  sourceDocumentId?: string;
  sourceAnalysisId?: string;
  status: 'active' | 'completed' | 'paused';
  currentRound: number;
  extractedTerms?: ExtractedTerms;
  relationshipMetrics: RelationshipMetrics;
  score: number;
  aiPersonality: AiPersonality;
  organizationId: string;
  totalMessages: number;
  aiResponseTime: number;
  createdAt: string;
  updatedAt: string;
  lastActivityAt: string;
  __v?: number;
}

export interface ExtractedTerms {
  price?: number;
  currency?: string;
  terms?: string[];
}

export interface RelationshipMetrics {
  trust: number;
  respect: number;
  pressure: number;
}

export interface AiPersonality {
  characterId: string;
  aggressiveness: number;
  flexibility: number;
  riskTolerance: number;
  communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
}

export interface SendMoveRequest {
  content: string;
  extractedData?: {
    offer?: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    strategy?: string;
    sentiment?: string;
    confidence?: number;
  };
  context?: {
    userConfidence?: number;
    timeSpent?: number;
    [key: string]: any;
  };
}

export interface SendMoveResponse {
  userMessage: {
    content: string;
    extractedData: {
      offer?: {
        price?: number;
        currency?: string;
        terms?: string[];
      };
      strategy: string;
      sentiment: string;
      confidence: number;
    };
    timestamp: string;
  };
  aiResponse: {
    content: string;
    suggestions?: string[];
    extractedData?: {
      strategy: string;
      sentiment: string;
    };
    timestamp: string;
  };
  sessionUpdate: {
    id: string;
    currentRound: number;
    extractedTerms: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    relationshipMetrics: {
      trust: number;
      respect: number;
      pressure: number;
    };
    score: number;
  };
  processingTime: number;
}

export interface ExtractedData {
  offer: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  strategy: string;
  sentiment: string;
  confidence: number;
  extractedEntities: string[];
  processingTime: number;
}

export interface ExtractDataRequest {
  message: string;
  context?: {
    scenarioType?: string;
    currentRound?: number;
    [key: string]: any;
  };
}

export interface ExtractDataResponse {
  offer: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  strategy: string;
  sentiment: string;
  confidence: number;
  extractedEntities: string[];
  processingTime: number;
}

export interface GetSessionsResponse {
  sessions: NegotiationSession[];
  total: number;
  limit: number;
  offset: number;
}

export interface NegotiationScenario {
  id: string;
  title: string;
  description: string;
  difficulty: number;
  category: string;
  estimatedDuration: number;
  objectives: string[];
  context: {
    background: string;
    yourRole: string;
    counterpartRole: string;
    stakes: string;
  };
  isUnlocked: boolean;
}

export interface ChatNegotiationMessage {
  id: string;
  sessionId: string;
  role: 'user' | 'ai';
  content: string;
  timestamp: string;
  extractedData?: ExtractedData & {
    topic?: string;
  };
  suggestions?: string[];
  processingTime?: number;
  // Extended properties from conversation history API
  relationshipImpact?: {
    trustChange?: number;
    respectChange?: number;
    pressureChange?: number;
  };
  scoreImpact?: number;
  processingTimeMs?: number;
  confidence?: number;
  detectedStrategy?: string;
  sentiment?: string;
  aiResponseMetadata?: {
    suggestions?: string[];
    responseTime?: number;
  };
}

// Conversation History Response from API
export interface ConversationHistoryResponse {
  sessionId: string;
  messages: ChatNegotiationMessage[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  sessionInfo: {
    currentRound: number;
    totalMessages: number;
    score: number;
    status: string;
    relationshipMetrics: {
      trust: number;
      respect: number;
      pressure: number;
    };
  };
}

export interface NegotiationSessionState {
  session: NegotiationSession | null;
  messages: ChatNegotiationMessage[];
  isLoading: boolean;
  error: string | null;
  currentMessage: string;
  isTyping: boolean;
}

// Error Types
export interface NegotiationApiError {
  statusCode: number;
  message: string;
  error: string;
  details?: any;
  path: string;
  timestamp: string;
}

// Scenario Management Types

export interface Timeline {
  value: number;
  unit: 'days' | 'weeks' | 'months';
}

export interface CreateScenarioRequest {
  name: string;
  description: string;
  industry: string;
  contractType: string;
  difficulty: 'beginner' | 'intermediate' | 'expert';
  timeline: Timeline;
  parties: {
    name: string;
    role: string;
    priorities: string[];
    negotiationStyle: string;
  }[];
  initialOffer?: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  constraints?: {
    maxRounds?: number;
    timeLimit?: number;
    mustHaveTerms?: string[];
    dealBreakers?: string[];
  };
  tags?: string[];
}

export interface ScenarioResponse {
  _id: string;
  id?: string; // For backward compatibility
  name: string;
  description: string;
  industry: string;
  contractType: string;
  difficulty: string;
  parties: {
    name: string;
    role: string;
    priorities: string[];
    negotiationStyle: string;
    constraints?: {
      dealBreakers?: string[];
      mustHaveTerms?: string[];
    };
    id: string;
  }[];
  initialOffer?: {
    price?: number;
    currency?: string;
    paymentTerms?: string;
    warranties?: string[];
    liabilities?: string[];
    customTerms?: {
      keyTerms?: string[];
    };
  };
  constraints?: {
    maxRounds?: number;
    timeLimit?: number;
    mustHaveTerms?: string[];
    dealBreakers?: string[];
    flexibleTerms?: string[];
  };
  timeline?: {
    startDate: string;
    expectedDuration: number;
    maxDuration: number;
    breakDuration: number;
  };
  status: string;
  organizationId: string;
  createdBy: string;
  isTemplate: boolean;
  tags: string[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface CloneScenarioRequest {
  name?: string;
  description?: string;
}

export interface CreateScenarioFromAnalysisRequest {
  analysisId: string;
}

export interface CreateSessionFromDocumentRequest {
  analysisId: string;
  aiPersonality?: AiPersonality;
}

export interface DocumentContextResponse {
  context: {
    analysisId: string;
    contractType: string;
    keyIssues: string[];
    riskFactors: string[];
    recommendations: string[];
    originalDocument: object;
  } | null;
  hasDocumentContext: boolean;
}

export interface GetScenariosQuery {
  industry?: string;
  contractType?: string;
  difficulty?: string;
  tags?: string;
  includeTemplates?: boolean;
}

export interface DeleteScenarioResponse {
  message: string;
  deletedScenarioId: string;
}

// WebSocket Event Types (for future enhancement)
export interface NegotiationEvents {
  'session:created': NegotiationSession;
  'move:sent': SendMoveResponse;
  'ai:typing': { sessionId: string };
  'session:updated': Partial<NegotiationSession>;
}