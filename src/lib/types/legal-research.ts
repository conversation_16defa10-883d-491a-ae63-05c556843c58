// Legal Research Assistant Types
// Based on backend API documentation

export interface ResearchSession {
  sessionId: string;
  title: string;
  description?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  isShared: boolean;
  queryCount: number;
  totalCreditsUsed: number;
  queries?: ResearchQuery[];
}

export interface ResearchQuery {
  queryId: string;
  query?: string;
  question?: string; // For follow-up questions
  timestamp: string;
  creditsUsed: number;
  resultSummary: string;
  type: 'initial' | 'followup'; // Backend uses 'type' instead of 'queryType'
  metadata?: {
    sourcesFound: number;
    synthesisGenerated: boolean;
    responseTime: number;
    sourceBreakdown: Record<string, number>;
  };
  searchResults?: SearchResults;
  aiSynthesis?: AISynthesis;
  followUpSuggestions?: string[];
}

export interface ResearchMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  
  // Research-specific fields
  searchResults?: SearchResults;
  aiSynthesis?: AISynthesis;
  followUpSuggestions?: string[];
  creditsUsed?: number;
  queryType?: 'research' | 'followup';
  queryId?: string;
  sessionId?: string;
}

export interface SearchResults {
  totalSources: number;
  sources: SearchResult[];
}

export interface SearchResult {
  id: string;
  type: 'case_law' | 'statute' | 'regulation' | 'news' | 'other';
  title: string;
  citation: string | string[] | null; // Backend can return array or null
  court?: string | null;
  authority?: string | null;
  date?: string;
  effectiveDate?: string;
  jurisdiction: string;
  practiceArea: string | null;
  url: string;
  snippet: string;
  relevanceScore: number;
  authorityScore: number;
}

export interface KeyFinding {
  finding: string;
  sourceUrls: string[];
}

export interface LegalAnalysis {
  text: string;
  sourceUrls: string[];
}

export interface AISynthesis {
  legalAnalysis: LegalAnalysis;
  keyFindings: KeyFinding[];
  citations: string[];
  confidenceScore: number;
  practiceImplications: string[];
  connectionToPrevious?: string; // For follow-up questions
  jurisdictionalNotes?: string;
  recentDevelopments?: string;
  metadata?: {
    duration: number;
    sourcesAnalyzed: number;
    aiModel: string;
    promptVersion: string;
  };
}

// Raw AI synthesis from backend (JSON string format)
export interface RawAISynthesis {
  summary: string; // This comes as a JSON string that needs parsing
  keyFindings: string[]; // This comes as a JSON string that needs parsing
  legalAnalysis: string; // This comes as a JSON string that needs parsing
  citations: string[];
  confidenceScore: number;
  practiceImplications: string[];
  jurisdictionalNotes: string;
  recentDevelopments: string;
  metadata?: {
    duration: number;
    sourcesAnalyzed: number;
    aiModel: string;
    promptVersion: string;
  };
}

export interface ResearchOptions {
  includeSynthesis?: boolean;
  maxSources?: number;
  jurisdictions?: string[];
  practiceAreas?: string[];
  timeRange?: {
    from: string;
    to: string;
  };
  sourceTypes?: ('case_law' | 'statutes' | 'regulations' | 'news')[];
  synthesisStyle?: 'brief' | 'comprehensive' | 'analytical';
}

// API Request/Response Types
export interface ResearchQueryRequest {
  query: string;
  options?: ResearchOptions;
  sessionId?: string;
}

export interface FollowUpRequest {
  sessionId: string;
  question: string;
  options?: {
    includeSynthesis?: boolean;
    focusOnPrevious?: boolean;
  };
}

export interface ResearchResponse {
  success: boolean;
  data: {
    queryId: string;
    sessionId: string;
    query: string;
    searchResults: SearchResults;
    aiSynthesis?: AISynthesis;
    followUpSuggestions: string[];
    metadata: {
      searchDuration: number;
      synthesisDuration?: number;
      totalDuration: number;
      creditsUsed: number;
      timestamp: string;
    };
  };
}

export interface FollowUpResponse {
  success: boolean;
  data: {
    queryId: string;
    sessionId: string;
    question: string;
    contextualAnalysis?: string;
    searchResults: SearchResults;
    aiSynthesis?: AISynthesis;
    followUpSuggestions: string[];
    metadata: {
      creditsUsed: number;
      timestamp: string;
    };
  };
}

export interface CreateSessionRequest {
  title: string;
  description?: string;
  tags?: string[];
  isShared?: boolean;
}

export interface SessionsResponse {
  success: boolean;
  data: {
    sessions: ResearchSession[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface SessionListParams {
  page?: number;
  limit?: number;
  tags?: string;
  search?: string;
}

export interface AnalyticsParams {
  period: '7d' | '30d' | '90d' | '1y';
  groupBy?: 'day' | 'week' | 'month';
}

export interface AnalyticsResponse {
  success: boolean;
  data: {
    period: string;
    summary: {
      totalQueries: number;
      totalSessions: number;
      totalCreditsUsed: number;
      averageQueriesPerSession: number;
      averageCreditsPerQuery: number;
    };
    trends: {
      queryVolume: Array<{
        date: string;
        queries: number;
        credits: number;
      }>;
    };
    topPracticeAreas: Array<{
      area: string;
      queries: number;
      percentage: number;
    }>;
    topJurisdictions: Array<{
      jurisdiction: string;
      queries: number;
      percentage: number;
    }>;
  };
}

// Error Types
export class LegalResearchError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'LegalResearchError';
  }
}

// UI State Types
export interface ResearchUIState {
  isSearching: boolean;
  isSynthesizing: boolean;
  showFilters: boolean;
  expandedSources: Set<string>;
  selectedJurisdictions: string[];
  selectedPracticeAreas: string[];
  synthesisStyle: 'brief' | 'comprehensive' | 'analytical';
}

// Credit-related types
export interface CreditCheckResult {
  hasCredits: boolean;
  currentBalance: number;
  requiredCredits: number;
  featureCost: {
    name: string;
    credits: number;
    description: string;
  };
}

// Subscription tier limitations
export interface TierLimitations {
  maxSources: number;
  hasSynthesis: boolean;
  sessionHistoryDays: number;
  hasSharedSessions: boolean;
  hasAnalytics: boolean;
  rateLimit: number; // queries per hour
}

export const TIER_LIMITATIONS: Record<string, TierLimitations> = {
  'law_student': {
    maxSources: 5,
    hasSynthesis: false,
    sessionHistoryDays: 7,
    hasSharedSessions: false,
    hasAnalytics: false,
    rateLimit: 5,
  },
  'lawyer': {
    maxSources: 15,
    hasSynthesis: true,
    sessionHistoryDays: 90,
    hasSharedSessions: false,
    hasAnalytics: true,
    rateLimit: 30,
  },
  'law_firm': {
    maxSources: 25,
    hasSynthesis: true,
    sessionHistoryDays: -1, // unlimited
    hasSharedSessions: true,
    hasAnalytics: true,
    rateLimit: 100,
  },
};

// Feature costs
export const RESEARCH_FEATURE_COSTS = {
  'legal_research_basic': {
    name: 'Basic Legal Research',
    credits: 1,
    description: 'Search legal databases without AI synthesis',
  },
  'legal_research_synthesis': {
    name: 'AI Legal Analysis',
    credits: 4,
    description: 'Comprehensive AI analysis with synthesis',
  },
  'legal_research_followup': {
    name: 'Follow-up Question',
    credits: 1,
    description: 'Contextual follow-up research',
  },
} as const;
