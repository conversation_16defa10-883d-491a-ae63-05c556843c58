export type SubscriptionTier = "law_student" | "lawyer" | "law_firm";
export type SubscriptionStatus =
	| "active"
	| "canceled"
	| "past_due"
	| "incomplete";

// Define enum for subscription tiers to ensure consistency
export enum SubscriptionTierEnum {
	LAW_STUDENT = "law_student", // Replaces 'free'
	LAWYER = "lawyer",           // Replaces 'pro'
	LAW_FIRM = "law_firm",       // Replaces 'admin'
}

// Legacy tier mapping for backward compatibility
export const LEGACY_TIER_MAPPING = {
	"free": "law_student",
	"pro": "lawyer",
	"admin": "law_firm",
} as const;

// Helper function to map legacy tiers to new tiers
export function mapLegacyTier(tier: string): SubscriptionTier {
	return LEGACY_TIER_MAPPING[tier as keyof typeof LEGACY_TIER_MAPPING] || tier as SubscriptionTier;
}

// Helper function to get display name for tier
export function getTierDisplayName(tier: SubscriptionTier): string {
	const displayNames = {
		"law_student": "🎓 Law Student",
		"lawyer": "⚖️ Lawyer",
		"law_firm": "🏢 Law Firm",
	};
	return displayNames[tier] || tier;
}

export interface UsageStats {
	documentsProcessed: number;
	analysisCount: number;
	lastUpdated: string;
}

// Credit system types
export interface CreditTransaction {
	type: "allocation" | "purchase" | "usage" | "refund" | "expiration";
	amount: number;
	balance: number;
	featureName?: string;
	transactionId?: string;
	timestamp: string;
	description: string;
}

export interface AutoRecharge {
	enabled: boolean;
	threshold: number;
	amount: number;
	stripePaymentMethodId?: string;
}

export interface CreditBalance {
	current: number;
	monthlyAllocation: number;
	totalEarned: number;
	totalSpent: number;
	lastAllocation: string;
}

export interface CreditPackage {
	id: string;
	name: string;
	credits: number;
	bonus: number;
	price: number;
	total: number;
	popular?: boolean;
	targetTier?: SubscriptionTier;
	description?: string;
}

export interface FeatureCost {
	featureName: string;
	credits: number;
	category: "free" | "basic" | "advanced" | "premium";
	description: string;
}

export interface SubscriptionFeature {
	id: string;
	name: string;
	description: string;
	tiers: SubscriptionTier[];
}

export interface SubscriptionPlan {
	id: SubscriptionTier;
	name: string;
	description: string;
	price: number;
	features: string[];
	limits: {
		documentLimit: number | "unlimited";
		// Analysis limits removed - now unlimited for all tiers within credit allocation
	};
}

export interface Subscription {
	organizationId: string;
	tier: SubscriptionTier;
	status: SubscriptionStatus;
	currentPeriodStart: string;
	currentPeriodEnd: string;
	cancelAtPeriodEnd: boolean;
	trialTier?: SubscriptionTier;
	trialEndDate?: string;
	features: string[];
	usageStats: UsageStats;
	// Credit system fields
	creditBalance: number;
	monthlyCreditsAllocation: number;
	totalCreditsEarned: number;
	totalCreditsSpent: number;
	lastCreditAllocation: string;
	creditHistory: CreditTransaction[];
	autoRecharge: AutoRecharge;
}

// Define feature lists by tier
export const tierFeatures = {
	[SubscriptionTierEnum.LAW_STUDENT]: [
		"basic_analysis",
		"document_upload",
		"chat",
		"basic_comparison",
		"basic_citation_analysis",
		"document_organization",
		"user_feedback",
		"threaded_discussions",
	],
	[SubscriptionTierEnum.LAWYER]: [
		"basic_analysis",
		"document_upload",
		"chat",
		"advanced_analysis",
		"bulk_upload",
		"priority_processing",
		"basic_comparison",
		"enhanced_comparison",
		"basic_citation_analysis",
		"enhanced_citation_analysis",
		"document_organization",
		"user_feedback",
		"custom_training",
		"advanced_document_organization",
		"advanced_analytics",
		"document_comparison",
		"negotiation_playbook",
		"privilege_log_automation",
		"negotiation_simulator",
		"section_comparison",
		"ai_insights",
		"export_capabilities",
		"api_access",
		"custom_workflows",
		"advanced_search",
		"audit_trail",
		"data_retention",
		"priority_support",
		"threaded_discussions",
		"advanced_sharing",
	],
	[SubscriptionTierEnum.LAW_FIRM]: [
		"basic_analysis",
		"document_upload",
		"chat",
		"advanced_analysis",
		"bulk_upload",
		"priority_processing",
		"basic_comparison",
		"enhanced_comparison",
		"basic_citation_analysis",
		"enhanced_citation_analysis",
		"document_organization",
		"user_feedback",
		"custom_training",
		"advanced_document_organization",
		"advanced_analytics",
		"document_comparison",
		"negotiation_playbook",
		"privilege_log_automation",
		"negotiation_simulator",
		"section_comparison",
		"ai_insights",
		"export_capabilities",
		"api_access",
		"custom_workflows",
		"advanced_search",
		"audit_trail",
		"data_retention",
		"priority_support",
		"precedent_analysis",
		"template_generation",
		"ai_assisted_drafting",
		"clause_intelligence",
		"deposition_preparation",
		"litigation_support",
		"contract_risk_scoring",
		"threaded_discussions",
		"advanced_sharing",
	],
};

export const SUBSCRIPTION_FEATURES: SubscriptionFeature[] = [
	// Basic features (Law Student tier and above)
	{
		id: "basic_analysis",
		name: "Document Analysis",
		description: "AI-powered document analysis and insights",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "document_upload",
		name: "Document Upload",
		description: "Upload and manage your legal documents",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "chat",
		name: "Chat with Documents",
		description: "Ask questions about your documents using natural language",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "legal_research",
		name: "Legal Research",
		description: "AI-powered legal research and case law search",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "document_comparison",
		name: "Document Comparison",
		description: "Compare documents side by side with difference highlighting",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "document_organization",
		name: "Document Organization",
		description: "Organize documents with tags and folders",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "case_search",
		name: "Case Law Search",
		description: "Search and analyze case law and legal precedents",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "user_feedback",
		name: "Feedback System",
		description: "Provide feedback to improve analysis accuracy",
		tiers: ["law_student", "lawyer", "law_firm"],
	},

	// Professional features (Lawyer tier and above)
	{
		id: "advanced_analysis",
		name: "Advanced Analysis",
		description: "Deep insights and comprehensive document understanding",
		tiers: ["lawyer", "law_firm"],
	},
	
	{
		id: "enhanced_comparison",
		name: "Enhanced Comparison",
		description: "Advanced document comparison with detailed insights",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "precedent_analysis",
		name: "Precedent Analysis",
		description: "AI-powered analysis of legal precedents and case law",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "deposition_preparation",
		name: "Deposition Analysis",
		description: "Analyze deposition transcripts and prepare questions",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "section_comparison",
		name: "Section Comparison",
		description: "Compare specific sections across multiple documents",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "advanced_analytics",
		name: "Usage Analytics",
		description: "Detailed analytics on document usage and performance",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "export_capabilities",
		name: "Export & Reports",
		description: "Export analysis results and generate detailed reports",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "priority_support",
		name: "Priority Support",
		description: "Priority customer support with faster response times",
		tiers: ["lawyer", "law_firm"],
	},

	// Enterprise features (Law Firm tier only)
	{
		id: "custom_integrations",
		name: "Custom Integrations",
		description: "Custom integrations with your existing legal software",
		tiers: ["law_firm"],
	},
	{
		id: "data_retention",
		name: "Extended Data Retention",
		description: "Extended data retention and backup capabilities",
		tiers: ["law_firm"],
	},
	{
		id: "dedicated_support",
		name: "Dedicated Support",
		description: "Dedicated account manager and support team",
		tiers: ["law_firm"],
	},

];

export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
	{
		id: "law_student",
		name: "Law Student",
		description: "Essential document analysis tools for law students and legal education. 50 monthly credits for AI-powered features.",
		price: 0,
		features: tierFeatures[SubscriptionTierEnum.LAW_STUDENT],
		limits: {
			documentLimit: 10,
		},
	},
	{
		id: "lawyer",
		name: "Lawyer",
		description:
			"Professional plan for individual lawyers with advanced features. Includes unlimited document analysis, advanced AI insights, priority support, 500 monthly credits, and legal research tools.",
		price: 29.99,
		features: tierFeatures[SubscriptionTierEnum.LAWYER],
		limits: {
			documentLimit: 200,
		},
	},
	{
		id: "law_firm",
		name: "Law Firm",
		description:
			"Enterprise plan for law firms with advanced features. Everything in Pro plus advanced workflow management, unlimited users, 2000 monthly credits, custom integrations, and dedicated support.",
		price: 99.99,
		features: tierFeatures[SubscriptionTierEnum.LAW_FIRM],
		limits: {
			documentLimit: "unlimited",
		},
	},
];

// Credit allocations by tier
export const TIER_CREDIT_ALLOCATIONS = {
	[SubscriptionTierEnum.LAW_STUDENT]: 50,
	[SubscriptionTierEnum.LAWYER]: 500,
	[SubscriptionTierEnum.LAW_FIRM]: 2000,
};

// Feature costs configuration
// 🎯 KEY PRINCIPLE: Credits are ONLY consumed for AI-powered features
// All CRUD operations (Create, Read, Update, Delete) are completely FREE
export const FEATURE_COSTS: Record<string, FeatureCost> = {
	// 🆓 FREE FEATURES (0 Credits) - Basic CRUD operations and essential functionality
	document_upload: {
		featureName: "document_upload",
		credits: 0,
		category: "free",
		description: "Upload and process documents",
	},
	document_organization: {
		featureName: "document_organization",
		credits: 0,
		category: "free",
		description: "Organize and categorize documents",
	},
	user_feedback: {
		featureName: "user_feedback",
		credits: 0,
		category: "free",
		description: "Provide feedback on the system",
	},
	clause_library: {
		featureName: "clause_library",
		credits: 0,
		category: "free",
		description: "Access to clause library and suggestions",
	},

	deposition_preparation: {
		featureName: "deposition_preparation",
		credits: 0,
		category: "free",
		description: "Deposition preparation and management",
	},
	audit_logs: {
		featureName: "audit_logs",
		credits: 0,
		category: "free",
		description: "Comprehensive audit logging",
	},
	data_export: {
		featureName: "data_export",
		credits: 0,
		category: "free",
		description: "Data export capabilities",
	},
	custom_integrations: {
		featureName: "custom_integrations",
		credits: 0,
		category: "free",
		description: "Custom integration development",
	},
	white_label_options: {
		featureName: "white_label_options",
		credits: 0,
		category: "free",
		description: "White label customization options",
	},
	dedicated_support: {
		featureName: "dedicated_support",
		credits: 0,
		category: "free",
		description: "Dedicated customer support",
	},
	api_access: {
		featureName: "api_access",
		credits: 0,
		category: "free",
		description: "API access for integrations",
	},

	// 💡 BASIC AI FEATURES (1 Credit) - Essential AI-powered functionality
	basic_analysis: {
		featureName: "basic_analysis",
		credits: 1,
		category: "basic",
		description: "Basic document analysis and insights",
	},
	chat: {
		featureName: "chat",
		credits: 1,
		category: "basic",
		description: "AI chat responses about documents",
	},
	basic_comparison: {
		featureName: "basic_comparison",
		credits: 1,
		category: "basic",
		description: "Basic AI document comparison",
	},
	basic_citation_analysis: {
		featureName: "basic_citation_analysis",
		credits: 1,
		category: "basic",
		description: "Basic citation analysis with simple relationship mapping",
	},
	clause_identification: {
		featureName: "clause_identification",
		credits: 1,
		category: "basic",
		description: "AI clause identification in documents",
	},


	// 🚀 ADVANCED AI FEATURES (2 Credits) - Sophisticated AI analysis and automation
	advanced_analysis: {
		featureName: "advanced_analysis",
		credits: 2,
		category: "advanced",
		description: "Advanced document analysis with deep insights",
	},
	enhanced_comparison: {
		featureName: "enhanced_comparison",
		credits: 2,
		category: "advanced",
		description: "Advanced document comparison with detailed insights",
	},
	precedent_analysis: {
		featureName: "precedent_analysis",
		credits: 2,
		category: "advanced",
		description: "AI-powered precedent analysis",
	},
	template_generation: {
		featureName: "template_generation",
		credits: 2,
		category: "advanced",
		description: "AI-powered template generation",
	},
	deviation_detection: {
		featureName: "deviation_detection",
		credits: 2,
		category: "advanced",
		description: "AI-powered deviation detection from standard terms",
	},
	deposition_insights: {
		featureName: "deposition_insights",
		credits: 2,
		category: "advanced",
		description: "Advanced insights from deposition analysis",
	},
	advanced_citation_analysis: {
		featureName: "advanced_citation_analysis",
		credits: 2,
		category: "advanced",
		description: "Advanced citation analysis with complex relationship mapping",
	},
	compliance_audit: {
		featureName: "compliance_audit",
		credits: 2,
		category: "advanced",
		description: "AI-powered compliance auditing",
	},
	negotiation_playbook: {
		featureName: "negotiation_playbook",
		credits: 2,
		category: "advanced",
		description: "AI-powered negotiation strategy analysis",
	},
	system_analytics: {
		featureName: "system_analytics",
		credits: 2,
		category: "advanced",
		description: "AI-powered system-wide analytics and reporting",
	},
	trend_analysis: {
		featureName: "trend_analysis",
		credits: 2,
		category: "advanced",
		description: "AI-powered trend analysis and insights",
	},

	// 💎 PREMIUM AI FEATURES (3 Credits) - Most advanced AI-powered capabilities
	document_automation: {
		featureName: "document_automation",
		credits: 3,
		category: "premium",
		description: "Automated document processing and generation",
	},
	privilege_detection: {
		featureName: "privilege_detection",
		credits: 3,
		category: "premium",
		description: "AI-powered privilege detection and analysis",
	},
	redaction_automation: {
		featureName: "redaction_automation",
		credits: 3,
		category: "premium",
		description: "AI-powered redaction automation",
	},
	contract_risk_scoring: {
		featureName: "contract_risk_scoring",
		credits: 3,
		category: "premium",
		description: "AI-powered contract risk assessment",
	},
	deposition_analysis: {
		featureName: "deposition_analysis",
		credits: 3,
		category: "premium",
		description: "AI-powered deposition analysis",
	},
	ai_question_generation: {
		featureName: "ai_question_generation",
		credits: 3,
		category: "premium",
		description: "AI-generated questions for depositions",
	},
	litigation_support: {
		featureName: "litigation_support",
		credits: 3,
		category: "premium",
		description: "Comprehensive litigation support tools",
	},
	negotiation_simulator: {
		featureName: "negotiation_simulator",
		credits: 3,
		category: "premium",
		description: "AI-powered negotiation simulation",
	},
	negotiation_training: {
		featureName: "negotiation_training",
		credits: 3,
		category: "premium",
		description: "AI-powered negotiation training",
	},
	advanced_analytics: {
		featureName: "advanced_analytics",
		credits: 3,
		category: "premium",
		description: "AI-powered advanced analytics and insights",
	},
	predictive_analytics: {
		featureName: "predictive_analytics",
		credits: 3,
		category: "premium",
		description: "AI-powered predictive analytics and forecasting",
	},
	risk_forecasting: {
		featureName: "risk_forecasting",
		credits: 3,
		category: "premium",
		description: "AI-powered risk forecasting and prediction",
	},
	custom_ai_models: {
		featureName: "custom_ai_models",
		credits: 3,
		category: "premium",
		description: "Custom AI model training and deployment",
	},
};

// Credit packages for purchase - organized by tier
export const CREDIT_PACKAGES: CreditPackage[] = [
	// Student Credit Package
	{
		id: "student",
		name: "Student Credit Package",
		credits: 50,
		bonus: 0,
		price: 499, // 4.99 in cents
		total: 50,
		popular: false,
		targetTier: "law_student",
		description: "50 credits for law students",
	},
	// Lawyer Small Credit Package
	{
		id: "lawyer_small",
		name: "Lawyer Starter Pack",
		credits: 200,
		bonus: 20,
		price: 1999, // 19.99 in cents
		total: 220,
		popular: true,
		targetTier: "lawyer",
		description: "200 credits + 20 bonus for practicing attorneys",
	},
	// Lawyer Large Credit Package
	{
		id: "lawyer_large",
		name: "Lawyer Professional Pack",
		credits: 500,
		bonus: 75,
		price: 4499, // 44.99 in cents
		total: 575,
		popular: false,
		targetTier: "lawyer",
		description: "500 credits + 75 bonus for busy legal practices",
	},
	// Firm Standard Credit Package
	{
		id: "firm_standard",
		name: "Law Firm Standard Pack",
		credits: 1000,
		bonus: 200,
		price: 7999, // 79.99 in cents
		total: 1200,
		popular: false,
		targetTier: "law_firm",
		description: "1000 credits + 200 bonus for growing legal teams",
	},
	// Firm Enterprise Credit Package
	{
		id: "firm_enterprise",
		name: "Law Firm Enterprise Pack",
		credits: 5000,
		bonus: 1500,
		price: 34999, // 349.99 in cents
		total: 6500,
		popular: false,
		targetTier: "law_firm",
		description: "5000 credits + 1500 bonus for large legal organizations",
	},
];
