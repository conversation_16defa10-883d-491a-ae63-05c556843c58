/**
 * WebSocket Client for Real-time Gamification Updates
 */

import { io, Socket } from 'socket.io-client';
import { Achievement, LevelUpdate, PressureEvent } from '../api/gamification';

export interface GamificationSocketEvents {
  // Incoming events from server
  'achievement_unlocked': (achievement: Achievement) => void;
  'level_up': (levelUpdate: LevelUpdate) => void;
  'xp_gained': (data: { amount: number; source: string; total: number }) => void;
  'pressure_event': (event: PressureEvent) => void;
  'relationship_updated': (data: { characterId: string; changes: any }) => void;
  'leaderboard_updated': (data: { timeframe: string; userRank: number }) => void;
  'session_score_update': (data: { score: number; breakdown: any }) => void;
  'challenge_update': (data: { challengeId: string; progress: any }) => void;
  
  // Connection events
  'connect': () => void;
  'disconnect': () => void;
  'error': (error: any) => void;
}

export interface GamificationSocketEmits {
  // Outgoing events to server
  'join_session': (data: { sessionId: string; userId: string }) => void;
  'leave_session': (data: { sessionId: string }) => void;
  'join_user_room': (data: { userId: string }) => void;
  'request_live_score': (data: { sessionId: string; moveData: any }) => void;
  'acknowledge_pressure_event': (data: { sessionId: string; eventId: string }) => void;
}

class GamificationSocket {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  // Event listeners storage
  private eventListeners: Map<keyof GamificationSocketEvents, Function[]> = new Map();

  constructor() {
    this.initializeSocket();
  }

  private initializeSocket() {
    const socketUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:3001';
    
    this.socket = io(`${socketUrl}/gamification`, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('🎮 Gamification WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connect');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🎮 Gamification WebSocket disconnected:', reason);
      this.isConnected = false;
      this.emit('disconnect');
    });

    this.socket.on('connect_error', (error) => {
      console.error('🎮 Gamification WebSocket connection error:', error);
      this.reconnectAttempts++;
      this.emit('error', error);
    });

    // Gamification events
    this.socket.on('achievement_unlocked', (achievement: Achievement) => {
      console.log('🏆 Achievement unlocked:', achievement);
      this.emit('achievement_unlocked', achievement);
    });

    this.socket.on('level_up', (levelUpdate: LevelUpdate) => {
      console.log('📈 Level up:', levelUpdate);
      this.emit('level_up', levelUpdate);
    });

    this.socket.on('xp_gained', (data) => {
      console.log('⚡ XP gained:', data);
      this.emit('xp_gained', data);
    });

    this.socket.on('pressure_event', (event: PressureEvent) => {
      console.log('🚨 Pressure event:', event);
      this.emit('pressure_event', event);
    });

    this.socket.on('relationship_updated', (data) => {
      console.log('🤝 Relationship updated:', data);
      this.emit('relationship_updated', data);
    });

    this.socket.on('leaderboard_updated', (data) => {
      console.log('🏅 Leaderboard updated:', data);
      this.emit('leaderboard_updated', data);
    });

    this.socket.on('session_score_update', (data) => {
      console.log('📊 Session score update:', data);
      this.emit('session_score_update', data);
    });

    this.socket.on('challenge_update', (data) => {
      console.log('🎯 Challenge update:', data);
      this.emit('challenge_update', data);
    });
  }

  // Public methods
  connect() {
    if (!this.socket) {
      this.initializeSocket();
    } else if (!this.isConnected) {
      // Instead of calling connect() on socket, reinitialize it
      this.destroy();
      this.initializeSocket();
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.isConnected = false;
    }
  }

  // Session management
  joinSession(sessionId: string, userId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_session', { sessionId, userId });
    }
  }

  leaveSession(sessionId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_session', { sessionId });
    }
  }

  joinUserRoom(userId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_user_room', { userId });
    }
  }

  // Live scoring
  requestLiveScore(sessionId: string, moveData: any) {
    if (this.socket && this.isConnected) {
      this.socket.emit('request_live_score', { sessionId, moveData });
    }
  }

  // Pressure events
  acknowledgePressureEvent(sessionId: string, eventId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('acknowledge_pressure_event', { sessionId, eventId });
    }
  }

  // Event listener management
  on<K extends keyof GamificationSocketEvents>(
    event: K,
    listener: GamificationSocketEvents[K]
  ) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off<K extends keyof GamificationSocketEvents>(
    event: K,
    listener: GamificationSocketEvents[K]
  ) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit<K extends keyof GamificationSocketEvents>(
    event: K,
    ...args: Parameters<GamificationSocketEvents[K]>
  ) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          (listener as any)(...args);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  // Utility methods
  isSocketConnected(): boolean {
    return this.isConnected && this.socket !== null;
  }

  getConnectionState(): 'connected' | 'disconnected' | 'connecting' | 'error' {
    if (!this.socket) return 'disconnected';
    if (this.isConnected) return 'connected';
    return 'connecting';
  }

  // Cleanup
  destroy() {
    this.eventListeners.clear();
    if (this.socket) {
      // Use type assertion to access socket methods
      const socket = this.socket as any;
      // Remove all event listeners
      if (socket.off) {
        socket.off('connect');
        socket.off('disconnect');
        socket.off('connect_error');
        socket.off('achievement_unlocked');
        socket.off('level_up');
        socket.off('xp_gained');
        socket.off('pressure_event');
        socket.off('relationship_updated');
        socket.off('leaderboard_updated');
        socket.off('session_score_update');
        socket.off('challenge_update');
      } else if (socket.removeAllListeners) {
        socket.removeAllListeners();
      }
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
  }
}

// Export singleton instance
export const gamificationSocket = new GamificationSocket();

// React Hook for using gamification socket
export function useGamificationSocket() {
  const [connectionState, setConnectionState] = React.useState<'connected' | 'disconnected' | 'connecting' | 'error'>('disconnected');

  React.useEffect(() => {
    const updateConnectionState = () => {
      setConnectionState(gamificationSocket.getConnectionState());
    };

    // Listen for connection changes
    gamificationSocket.on('connect', updateConnectionState);
    gamificationSocket.on('disconnect', updateConnectionState);
    gamificationSocket.on('error', updateConnectionState);

    // Initial state
    updateConnectionState();

    // Connect if not already connected
    if (!gamificationSocket.isSocketConnected()) {
      gamificationSocket.connect();
    }

    return () => {
      gamificationSocket.off('connect', updateConnectionState);
      gamificationSocket.off('disconnect', updateConnectionState);
      gamificationSocket.off('error', updateConnectionState);
    };
  }, []);

  return {
    socket: gamificationSocket,
    isConnected: connectionState === 'connected',
    connectionState,
    joinSession: gamificationSocket.joinSession.bind(gamificationSocket),
    leaveSession: gamificationSocket.leaveSession.bind(gamificationSocket),
    joinUserRoom: gamificationSocket.joinUserRoom.bind(gamificationSocket),
    requestLiveScore: gamificationSocket.requestLiveScore.bind(gamificationSocket),
    acknowledgePressureEvent: gamificationSocket.acknowledgePressureEvent.bind(gamificationSocket),
  };
}

// Import React for the hook
import React from 'react';
