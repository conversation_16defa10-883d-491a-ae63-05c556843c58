import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { AUTH_CONFIG } from "@/lib/config";

// Define paths that don't require authentication
const publicPaths = [
	AUTH_CONFIG.routes.login,
	AUTH_CONFIG.routes.register,
	"/password-recovery",
	"/terms",
	"/privacy",
	// OAuth callback routes
	"/google/callback",
	"/auth/google-callback",
	// Marketing pages
	"/",
	"/features",
	"/pricing",
	"/contact",
];

// Define paths that require authentication but don't require organization ID
const noOrgPaths = ["/billing/plans", "/billing/setup-intent"];

export function middleware(request: NextRequest) {
	const { pathname } = request.nextUrl;

	// Allow public paths
	if (
		publicPaths.some(
			(path) => pathname === path || pathname.startsWith("/public/")
		)
	) {
		return NextResponse.next();
	}

	// Allow static files and images
	if (
		pathname.startsWith("/_next/") ||
		pathname.startsWith("/api/auth/") ||
		pathname.match(/\.(ico|png|jpg|jpeg|svg)$/)
	) {
		return NextResponse.next();
	}

	// Check for access token in cookie or header
	const accessToken =
		request.cookies.get(AUTH_CONFIG.accessTokenKey)?.value ||
		request.headers.get("Authorization")?.split(" ")[1];

	if (!accessToken) {
		// For pages, redirect to login with return URL
		if (!pathname.startsWith("/api")) {
			const loginUrl = new URL(AUTH_CONFIG.routes.login, request.url);
			loginUrl.searchParams.set("returnUrl", pathname);
			return NextResponse.redirect(loginUrl);
		}

		// For API routes, return 401
		return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
	}

	// Handle API requests
	if (pathname.startsWith("/api")) {
		// Create headers for the backend request
		const headers = new Headers(request.headers);

		// Forward the access token
		headers.set("Authorization", `Bearer ${accessToken}`);

		// Forward organization ID if required and present
		if (!noOrgPaths.some((path) => pathname.includes(path))) {
			const organizationId =
				request.cookies.get(AUTH_CONFIG.organizationKey)?.value ||
				request.headers.get("X-Organization-Id");
			if (organizationId) {
				headers.set("X-Organization-Id", organizationId);
			}
		}

		// Add CORS headers for API responses
		const response = NextResponse.next({
			request: {
				headers,
			},
		});

		// Only set CORS headers if origin is in allowed list
		const origin = request.headers.get("origin");
		if (origin && AUTH_CONFIG.allowedOrigins.includes(origin)) {
			response.headers.set("Access-Control-Allow-Origin", origin);
			response.headers.set(
				"Access-Control-Allow-Methods",
				"GET, POST, PUT, DELETE, OPTIONS"
			);
			response.headers.set(
				"Access-Control-Allow-Headers",
				"Content-Type, Authorization, X-Organization-Id"
			);
			response.headers.set("Access-Control-Allow-Credentials", "true");
		}

		return response;
	}

	return NextResponse.next();
}

// Configure paths that trigger the middleware
export const config = {
	matcher: [
		/*
		 * Match all request paths except:
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * - public folder
		 */
		"/((?!_next/static|_next/image|favicon.ico|public/).*)",
		"/api/:path*",
	],
};
