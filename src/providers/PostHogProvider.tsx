// src/providers/PostHogProvider.tsx
'use client';

import React, { useEffect } from 'react';
import posthogJs from 'posthog-js';
import { POSTHOG_CONFIG } from '../config/posthog.config';

// Initialize PostHog instance
const posthog = posthogJs;

// Export the initialized instance
export { posthog };

interface PostHogWrapperProps {
  children: React.ReactNode;
}

export const PostHogWrapper: React.FC<PostHogWrapperProps> = ({ children }) => {
  useEffect(() => {
    if (POSTHOG_CONFIG.apiKey && POSTHOG_CONFIG.host && typeof window !== 'undefined') {
      posthog.init(POSTHOG_CONFIG.apiKey, {
        api_host: POSTHOG_CONFIG.host,
        ...POSTHOG_CONFIG.options
      });
    } else {
      console.warn('PostHog API key or host not configured. PostHog will not be initialized.');
    }
  }, []);

  return <>{children}</>;
};
