declare module 'socket.io-client' {
  export const io: any;
  export class Socket {
    constructor(url: string, options?: any);
    on(event: string, listener: (data: any) => void): this;
    emit(event: string, data: any): this;
    disconnect(): void;
  }
}

declare interface PageProps {
  params: {
    id?: string;
    [key: string]: string | undefined;
  };
  searchParams?: { [key: string]: string | string[] | undefined };
}

declare type AIResponseData = {
  content?: string;
  [key: string]: any;
}

declare type RiskLevel = 'low' | 'medium' | 'high';

declare type PerformanceHistoryEntry = {
  id: string;
  sessionId: string;
  score: number;
  feedback: string;
  timestamp: string;
}

declare interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}